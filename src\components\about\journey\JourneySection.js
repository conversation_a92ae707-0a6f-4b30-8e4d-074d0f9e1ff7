"use client";

import React, { useEffect, useRef } from "react";
import { Col, Container, Row } from "reactstrap";
import Image from "next/image";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import img1 from "/public/aboutPage/Journey/img1.png";
import img2 from "/public/aboutPage/Journey/img2.png";
import img3 from "/public/aboutPage/Journey/img3.png";
import img4 from "/public/aboutPage/Journey/img4.png";
import img5 from "/public/aboutPage/Journey/img5.png";
import img6 from "/public/aboutPage/Journey/img6.png";
import img7 from "/public/aboutPage/Journey/img7.png";
import img8 from "/public/aboutPage/Journey/img8.png";
import img9 from "/public/aboutPage/Journey/img9.png";
import img10 from "/public/aboutPage/Journey/img10.png";
import { ToolplateIcon, WoofferIcon } from "@/utils/icons";
// Register plugin
gsap.registerPlugin(ScrollTrigger);

const timelineData = [
  {
    date: "1 July 2021",
    image: img4,
    title: "TST Technology is now official!  😌",
    content:
      "Our 3 founders, Parth Makwana, Hiren Kalariya and Daxesh Italiya, started TST Technology in the 3rd year of their college to put their skills to use and help startups with the power of technology. The company website, an earlier version of the same website you're on right now, went live in the evening at 6:30 pm on 1st July 2021.",
  },
  {
    date: "15 July 2021",
    image: img7,
    title: "Our First Home  🥳",
    content:
      "The first few teammates of TST Technology were not native to Ahmedabad, so the company acquired a spacious apartment to double as a living premise and an office. The apartment holds some very good memories of late-night coding, long meetings, and shared dreams of the future. TST Technology went global with our first international client from this very same place.",
  },
  {
    date: "1 July 2022",
    image: img8,
    title: "TST Technology Turns One  🎉",
    content:
      "The first successful year of the company was celebrated in high spirits as TST Technology expanded its portfolio and its team size too. The company now had experts for specific domains- NodeJS, ReactJS, DevOps and more. We were working globally on projects from 5 different countries and many major cities of India, such as Mumbai, Pune and Bengaluru.",
  },
  {
    date: "5 October 2022",
    image: img1,
    title: "Our First Corporate Office  🏢",
    content:
      "TST Technology acquired its first corporate office, now serving as its headquarters, in Ahmedabad. We now had 15+ teammates and were working in 10+ industries by then. We say professional but the office is actually made into a second home by its teammates, not only because of the sheer amount of time they spend here!",
  },
  {
    date: "18 December 2022",
    image: img6,
    title: "TST Technology in Surat 🏢",
    content:
      "TST Technology established a second base in Surat for our 20+ teammates situated there. The Surat office is led by our Co-founder and CTO, Mr. Daxesh Italiya and can be labeled as our Chief Development Hub! We have a solid Flutter Development and React Development team working from there.",
  },
  {
    date: "1 July 2023",
    image: img2,
    title: "TST Technology Turns Two  💫",
    content:
      "We had a great birthday bash celebrating 2 years of success with 35+ teammates and acquiring 2 new offices in the past year. The company had by then worked in 7+ countries so far with 20+ successful projects spanning across 15+ industries. The motto of “Your Business is Our Business” was reinforced by thanking all the clients so far for their faith in us and our growth.",
  },
  {
    date: "13 September 2023",
    image: img10,
    title: "We launch Toolplate for AI Tools ",
    icon: <ToolplateIcon className="tw-w-[26px] tw-h-[26px]" />,
    content:
      "TST Technology proudly launched its own first-ever in-house product Toolplate, an AI tools platform, to help people be the most productive and efficient. Toolplate currently has a vast array of offerings but the star feature is its Prompt Search, letting users find AI tools based on what requirement they have- fulfilling our ultimate goal to help everyone save their time and be their very best.",
  },
  {
    date: "12 November 2023",
    image: img9,
    title: "We launch Wooffer for Servers",
    icon: <WoofferIcon className="tw-w-[26px] tw-h-[26px]" />,
    content:
      "Like every other company, TST Technology also faced difficulties. One of these was the lack of options when it came to server monitoring and being alerted to any problems, unless the end users encountered a bug or the client called to let us know. TST Technology, under the initiative and leadership of Daxesh Italiya, created Wooffer, a server monitoring platform that helps developers and business owners stay on top of their tech and be the first ones to know when and what problem arises.",
  },
  {
    date: "1 June 2024",
    image: img3,
    title: "We launch our own HRMS  🤩",
    content:
      "TST Technology created its own HRMS for in-house use that contains the features most required by the team to be their most productive, with a colour scheme that makes your heart happy when you log in to work. We believe in using only the best, and if the best doesn’t exist, we’ll make it ourselves. The same ideology translates even routine work into extraordinary for our clients.",
  },
  {
    date: "1 July 2024",
    image: img5,
    title: "TST Technology Turns Three  🎊",
    content:
      "The third birthday bash comes at a time when the company and team have matured with 3 product launches and 40+ successful projects in the last year. The board of members strive to increase productivity and team growth with Standard Operating Procedures. Innovation Day was also observed under the leadership of Daxesh Italiya, to encourage the team to share improvement areas and suggestions company-wide to make us more user-friendly and efficient.",
  },
];
export default function JourneySection({ className }) {
  const sectionRef = useRef(null);
  const lineRef = useRef(null);
  const cardRefs = useRef([]);
  const dotRefs = useRef([]);

  useEffect(() => {
    if (!sectionRef.current) return;

    // Animate the vertical green line height on scroll
    gsap.to(lineRef.current, {
      height: "100%",
      ease: "none",
      scrollTrigger: {
        trigger: sectionRef.current,
        start: "top center",
        end: "bottom bottom",
        scrub: true,
      },
    });

    // Animate each timeline card (fade-in and move up)
    cardRefs.current.forEach((card, i) => {
      gsap.fromTo(
        card,
        { opacity: 0.3, y: 20 },
        {
          opacity: 1,
          y: 0,
          duration: 0.6,
          ease: "power2.out",
          scrollTrigger: {
            trigger: card,
            start: "top 80%",
            toggleActions: "play none none reverse",
          },
        }
      );
    });
    dotRefs.current.forEach((dot, i) => {
      const innerDot = dot.querySelector("div"); // get the inner dot

      gsap.to(innerDot, {
        backgroundColor: "#35B729", // your green color
        scrollTrigger: {
          trigger: cardRefs.current[i],
          start: "top 80%",
          end: "bottom 50%",
          // toggleActions: "play reverse play reverse",
        },
      });
    });

  }, []);

  return (
    <section className={`${className}`} ref={sectionRef}>
      <Container className="tw-relative tw-flex tw-flex-col tw-items-center tw-gap-[40px] lg:tw-gap-[70px]">
        <div className="tw-relative">
          {/* Vertical Line */}
          <div
            // ref={lineRef}
            className="green-line tw-absolute tw-left-2 md:tw-left-1/2 tw-top-0 tw-w-px tw-bg-[#CCCCCC] -tw-translate-x-1/2"
            style={{ height: "100%" }}
          />
          <div
            ref={lineRef}
            className="green-line tw-absolute tw-left-2 md:tw-left-1/2 tw-top-0 tw-w-px tw-bg-primary_green -tw-translate-x-1/2"
            style={{ height: "96px" }}
          />
          <div className="tw-flex tw-flex-col tw-gap-[40px] lg:tw-gap-[70px] xl:tw-gap-[100px] tw-relative">
            {/* Central vertical line */}

            {timelineData?.map((item, index) => (
              <div
                key={index}
                // ref={(el) => (cardRefs.current[index] = el)}
                className="timeline-card tw-relative tw-z-10"
              >
                <div className="md:tw-flex tw-items-start tw-relative  md:tw-pl-0 tw-pl-[30px]">
                  {/* Left Sticky Content with Dot */}
                  <div className="md:tw-w-1/2 md:tw-sticky md:tw-top-1/2 tw-z-10 tw-flex tw-items-center">
                    <div className="tw-flex tw-flex-col md:tw-text-right tw-gap-2.5 tw-flex-1 xl:tw-pr-[100px] lg:tw-pr-[70px] md:tw-pr-[50px] tw-pr-[20px]">
                      <h3 className="tw-text-primary_green tw-font-bold tw-text-[24px] md:tw-text-[30px] lg:tw-text-[36px] tw-font-bricolageGrotesque tw-mb-0">
                        {item.date}
                      </h3>
                      <div className="tw-flex tw-items-center tw-gap-[10px] tw-justify-start md:tw-justify-end">
                        <h4 className="tw-text-primary_black tw-font-bold tw-text-[18px] lg:tw-text-[22px] xl:tw-text-[26px] tw-font-bricolageGrotesque tw-mb-0 tw-whitespace-nowrap">
                          {item.title}
                        </h4>
                        {item.icon ? (
                          <span>
                            {item.icon}
                          </span>
                        ) : null}
                      </div>

                      {/* Mobile Image */}
                      <div className="md:tw-hidden tw-aspect-[1.37] tw-relative tw-w-full ">
                        <Image
                          src={item.image}
                          fill
                          className="tw-object-contain tw-rounded-2xl"
                          alt={`${item.title} image`}
                        />
                      </div>
                    </div>

                    {/* Timeline Dot - Part of sticky section */}
                    <div
                      ref={(el) => (dotRefs.current[index] = el)}
                      className="tw-p-1.5 tw-absolute md:tw-top-8 tw-top-[22px] -tw-left-[3px] md:tw-left-auto md:tw-right-[-11px] tw-rounded-full tw-shadow-sm tw-flex-shrink-0"
                    >
                      <div className="tw-aspect-square tw-h-2.5 tw-bg-[#CCCCCC] tw-rounded-full tw-z-10" />
                    </div>


                  </div>

                  {/* Right Content */}
                  <div className="md:tw-w-1/2 xl:tw-pl-[100px] lg:tw-pl-[70px] md:tw-pl-[50px] ">
                    <p className="tw-text-[14px] md:tw-text-[16px] tw-text-primary_gray tw-mb-0">
                      {item.content}
                    </p>

                    {/* Desktop Image */}
                    <div className="md:tw-block tw-hidden xl:tw-mr-[145px] lg:tw-mr-[100px]">
                      <div className="tw-aspect-[370/270] tw-relative tw-w-full tw-mt-[50px] tw-rounded-[20px]">
                        <Image
                          src={item.image}
                          fill
                          className="tw-object-cover tw-rounded-[20px]"
                          alt={`${item.title} image`}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>


        </div>
      </Container>
    </section>
  );
}
