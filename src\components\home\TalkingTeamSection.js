"use client"
import { useState } from "react";
import { Container } from "reactstrap";

const TalkingTeamSection = ({
    plainColorTitle,
    colorTitle,
    description,
    descriptionWidth,
    buttonText,
    isButton,
    isInput,
    onInputChange,
    inputValue,
    handleClick,
    inputPlaceholder,
    className = "",
}) => {
    const [formData, setFormData] = useState({
        name: "",
        email: "",
        message: "",
    });

    const handleChange = (e) => {
        setFormData((prev) => ({
            ...prev,
            [e.target.name]: e.target.value,
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        console.log(formData);
        // handle form submission logic here
    };
    return (
        <section className="tw-h-screen tw-bg-no-repeat tw-bg-contain">
            <div
                className={` ${className}`}
            >
                <Container>
                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center xl:tw-py-[140px] md:tw-py-[65px] tw-py-[130px] tw-text-center tw-gap-[40px] tw-w-full">
                        <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px] ">
                            <h1 className="tw-mb-0 xl:tw-text-[2.75rem] lg:tw-text-[40px] md:tw-text-[35px] tw-text-[30px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold md:tw-w-full tw-text-white">
                                {plainColorTitle} <span className="tw-text-secondary_color">&nbsp;{colorTitle}</span>
                            </h1>
                            <p className={`tw-mb-0 md:tw-text-lg tw-text-sm tw-text-white/70 ${descriptionWidth} md:tw-leading-[140%] tw-leading-[120%] tw-max-w-3xl tw-mx-auto`}>
                                {description}
                            </p>
                        </div>

                        <form onSubmit={handleSubmit} className="tw-mt-8 tw-w-full ">
                            <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-4">
                                <div className="tw-flex tw-flex-col tw-items-start tw-w-full">
                                    <label htmlFor="name" className="tw-text-sm tw-mb-1 tw-text-white">
                                        Name<span className="tw-text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        id="name"
                                        name="name"
                                        placeholder="Enter name"
                                        value={formData.name}
                                        onChange={handleChange}
                                        required
                                        className="tw-bg-transparent tw-border tw-text-white tw-border-white/40 tw-rounded-[12px] tw-p-3 tw-outline-none focus:tw-border-purple-400 tw-w-full"
                                    />
                                </div>

                                <div className="tw-flex tw-flex-col tw-items-start">
                                    <label htmlFor="email" className="tw-text-sm tw-mb-1 tw-text-white">
                                        Email<span className="tw-text-red-500">*</span>
                                    </label>
                                    <input
                                        type="email"
                                        id="email"
                                        name="email"
                                        placeholder="Enter email"
                                        value={formData.email}
                                        onChange={handleChange}
                                        required
                                        className="tw-bg-transparent tw-text-white tw-border  tw-border-white/40 tw-rounded-[12px] tw-w-full tw-p-3 tw-outline-none focus:tw-border-purple-400"
                                    />
                                </div>
                            </div>

                            <div className="tw-flex tw-flex-col tw-mt-4 tw-items-start">
                                <label htmlFor="message" className="tw-text-sm tw-mb-1 tw-text-white">
                                    Message
                                </label>
                                <textarea
                                    id="message"
                                    name="message"
                                    rows="5"
                                    placeholder="Enter message"
                                    value={formData.message}
                                    onChange={handleChange}
                                    className="tw-bg-transparent tw-border tw-text-white tw-border-white/40 tw-rounded-[12px] tw-p-3 tw-outline-none focus:tw-border-purple-400 tw-w-full tw-resize-none"
                                ></textarea>
                            </div>

                            <button
                                type="submit"
                                className="tw-mt-6 tw-bg-white tw-text-[#433878] tw-font-medium tw-rounded-[12px] tw-py-3 tw-px-6 lg:tw-px-10 tw-self-center  hover:tw-opacity-80 tw-transition"
                            >
                                Submit
                            </button>
                        </form>
                        {/* {isInput ? (<div className="tw-flex tw-flex-col tw-items-center tw-gap-5 tw-w-full">
                        <div className="tw-flex tw-w-full sm:tw-w-[50%] xl:tw-w-[45%] tw-h-[50px] lg:tw-h-[60px] tw-items-center tw-justify-between tw-rounded-[40px] tw-pl-3 lg:tw-pl-5 tw-pr-[5px] tw-py-[5px] tw-border tw-border-[#8093A5] md:tw-border-[#dfe4e8]">
                            <div className="tw-flex tw-items-center tw-gap-2.5 tw-flex-1">
                                <EmailIcon
                                    strokeColor="black"
                                    className="tw-aspect-square tw-w-6"
                                />
                                <input
                                    type="text"
                                    value={inputValue}
                                    onChange={(e) => onInputChange(e.target.value)}
                                    className="tw-border-0 tw-outline-none tw-bg-transparent tw-h-full tw-p-0 tw-w-full tw-font-normal tw-text-[14px] tw-text-primary_gray lg:tw-text-[16px] placeholder:tw-text-primary_gray"
                                    placeholder={inputPlaceholder}
                                />
                            </div>

                            <FillButton
                                title={buttonText}
                                onClick={handleClick}
                                className="lg:tw-block tw-hidden tw-h-full tw-rounded-[50px] tw-px-8 md:tw-px-5 xl:tw-px-6 tw-text-white tw-font-medium"
                            />
                        </div>
                        <FillButton
                            title={buttonText}
                            onClick={handleClick}
                            className="lg:tw-hidden tw-py-3 tw-rounded-[50px] tw-px-8 md:tw-px-5 xl:tw-px-6 tw-text-white tw-font-medium"
                        /></div>
                    ) : null} */}
                    </div>
                </Container>
            </div>
        </section>
    );
}

export default TalkingTeamSection;