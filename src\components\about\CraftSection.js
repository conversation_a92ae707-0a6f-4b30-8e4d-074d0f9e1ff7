import React from "react";
import { Col, Container, Row } from "reactstrap";
import { SmallServiceCard } from "../card/SmallServiceCard";

import uiuxIcon from "/public/serviceLogo/uiux.png";
import mobileAppIcon from "/public/serviceLogo/mobileApp.png";
import saasDevIcon from "/public/serviceLogo/saasDev.png";
import webDevIcon from "/public/serviceLogo/webDev.png";
import softwareDevIcon from "/public/serviceLogo/softwareDev.png";
import itConsultingIcon from "/public/serviceLogo/itConsulting.png";
import devOpsIcon from "/public/serviceLogo/devOps.png";
import virtualCtoIcon from "/public/serviceLogo/virtualCto.png";
import { CraftSlider } from "@/common/Slider/CraftSlider";

export const CraftSection = ({ className }) => {
  const services = [
    {
      icon: uiuxIcon,
      title: "UI UX Design",
    },
    {
      icon: mobileAppIcon,
      title: "Mobile App Development Services",
    },
    {
      icon: saasDevIcon,
      title: "SaaS Development Services",
    },
    {
      icon: webDevIcon,
      title: "Web Development Services",
    },
    {
      icon: softwareDevIcon,
      title: "Software Development Services",
    },
    {
      icon: itConsultingIcon,
      title: "IT Consulting Services",
    },
    {
      icon: devOpsIcon,
      title: "DevOps Services & Solutions",
    },
    {
      icon: virtualCtoIcon,
      title: "Virtual CTO Services",
    },
    
  ];

  return (
    <section className={`tw-bg-[#F7F9FB] ${className}`}>
      <Container fluid className="tw-flex tw-flex-col tw-gap-[40px] md:tw-gap-[70px] lg:tw-gap-[100px]">
        {/* Title */}
        <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
          <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            Crafting
            <span className="tw-text-primary_green"> Your Digital Future</span>
          </div>
          <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-w-[80%] tw-leading-[140%]">
            We design, build, and scale digital solutions that help businesses
            innovate, grow, and thrive in a fast-changing world.
          </div>
        </div>

        <div className="md:tw-hidden tw-block">
          <Row className="justify-content-center g-3">
            {services.map((service, index) => (
              <Col
                xs="12"
                key={index}
                className="d-flex justify-content-center"
              >
                <SmallServiceCard icon={service.icon} title={service.title} />
              </Col>
            ))}
          </Row>
        </div>

        {/* slider  */}
        <div className="md:tw-block tw-hidden tw-w-full">
          <CraftSlider services={services} />
        </div>
      </Container>
    </section>
  );
};
