"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { Col, Container, Row } from "reactstrap";
import Discovery from "../../../../public/service-page/UI-UX/Discovery.png";
import Research from "../../../../public/service-page/UI-UX/Research.png";
import Sketching from "../../../../public/service-page/UI-UX/Sketching.png";
import Wireframing from "../../../../public/service-page/UI-UX/Wireframing.png";
import UIUXDesign from "../../../../public/service-page/UI-UX/UX Design.png";
import Prototyping from "../../../../public/service-page/UI-UX/Prototyping.png";
import Delivery from "../../../../public/service-page/UI-UX/Delivery.png";
const ProcessSection = ({ className }) => {
  const [activeStep, setActiveStep] = useState(0);
  const steps = [
    {
      id: 0,
      title: "Discovery",
      icon: Discovery,
      description:
        "We start by deeply understanding your goals, challenges, and user needs through research and consultation.",
      className: "tw-order-1",
    },
    {
      id: 1,
      title: "Research",
      icon: Research,
      description:
        "We start by deeply understanding your goals, challenges, and user needs through research and consultation.",
      className: "tw-order-2",
    },
    {
      id: 2,
      title: "Sketching",
      icon: Sketching,
      description:
        "We start by deeply understanding your goals, challenges, and user needs through research and consultation.",
      className: "lg:tw-order-3 tw-order-4",
    },
    {
      id: 3,
      title: "Wireframing",
      icon: Wireframing,
      description:
        "We start by deeply understanding your goals, challenges, and user needs through research and consultation.",
      className: "lg:tw-order-4 tw-order-3",
    },
    {
      id: 4,
      title: "UI/UX Design",
      icon: UIUXDesign,
      description:
        "We start by deeply understanding your goals, challenges, and user needs through research and consultation.",
      className: "tw-order-5",
    },
    {
      id: 5,
      title: "Prototyping",
      icon: Prototyping,
      description:
        "We start by deeply understanding your goals, challenges, and user needs through research and consultation.",
      className: "tw-order-6",
    },
    {
      id: 6,
      title: "Delivery",
      icon: Delivery,
      description:
        "We start by deeply understanding your goals, challenges, and user needs through research and consultation.",
      className: "tw-order-6",
    },
  ];
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev === steps.length - 1 ? 0 : prev + 1));
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className={`tw-bg-service-gradient-2 ${className}`}>
      <Container>
        <h2 className="tw-mb-0 tw-text-primary_black tw-font-bold tw-text-center tw-font-bricolageGrotesque tw-text-[26px] md:tw-text-[36px] 2xl:tw-text-[48px] ">
          Design
          <span className="tw-text-primary_green"> Process</span>
        </h2>

        <div className="md:tw-py-[70px] tw-py-[40px]">
          <div className="xl:tw-bg-process-bg lg:tw-bg-process-mobile-bg-1024 md:tw-bg-process-mobile-bg-768 tw-bg-process-mobile-bg tw-bg-no-repeat lg:tw-bg-[center_35%] md:tw-bg-[center_35%]  tw-bg-[center_10%] tw-grid md:tw-grid-cols-7 tw-grid-cols-2 tw-gap-x-5 tw-gap-y-10 tw-place-items-center tw-place-content-center tw-justify-center">
            {steps.map((step, index) => {
              const isLast = index === steps.length - 1;
              const isOdd = steps.length % 2 !== 0;

              // Only apply center class if it's the last item and total items are odd
              const mobileCenterClass =
                isLast && isOdd
                  ? "tw-col-span-2 tw-justify-self-center md:tw-col-span-1 lg:tw-justify-self-auto"
                  : "";

              return (
                <div
                  onClick={() => setActiveStep(step.id)}
                  key={index}
                  className={`tw-cursor-pointer tw-flex tw-flex-col tw-items-center lg:tw-gap-y-[25px] tw-gap-y-[10px] tw-text-center tw-max-w-[120px] ${mobileCenterClass} ${
                    step.className || ""
                  }`}
                >
                  <div
                    className={`tw-bg-white xl:tw-p-[30px] lg:tw-p-[20px] md:tw-p-[16px] tw-p-[24px] tw-rounded-full tw-flex tw-items-center tw-justify-center tw-shadow-tech_card`}
                  >
                    <div className="tw-relative xl:tw-w-[50px] xl:tw-h-[50px] lg:tw-w-[30px] lg:tw-h-[30px] md:tw-w-[24px] md:tw-h-[24px] tw-w-[40px] tw-h-[40px]">
                      <Image
                        src={step?.icon}
                        alt="icon"
                        fill
                        className="tw-object-contain"
                      />
                      <div
                        className={`tw-absolute tw-inset-0 tw-bg-primary_green ${
                          activeStep === step.id
                            ? "tw-opacity-100"
                            : "tw-opacity-0"
                        } tw-mix-blend-overlay`}
                      ></div>
                    </div>
                  </div>
                  <span
                    className={`xl:tw-text-lg lg:tw-text-base md:tw-text-[12px]  ${
                      activeStep === step.id
                        ? "tw-text-primary_green tw-font-semibold"
                        : ""
                    }`}
                  >
                    {step.title}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Description of active step */}
        <div
          key={activeStep}
          className="tw-flex tw-flex-col tw-text-center tw-items-center tw-gap-[15px] tw-animate-slideInRight"
        >
          <h3 className="tw-mb-0 tw-text-primary_black tw-text-[24px] tw-font-medium tw-font-bricolageGrotesque tw-leading-[120%]">
            {steps[activeStep].title || ""}
          </h3>
          <p className="tw-text-primary_gray tw-text-[18px] tw-leading-[120%] tw-w-full">
            {steps[activeStep].description || ""}
          </p>
        </div>
      </Container>
    </section>
  );
};

export default ProcessSection;
