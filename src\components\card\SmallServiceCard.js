import Image from "next/image";
import React from "react";
import { NextArrowIcon } from "@/utils/icons";

export const SmallServiceCard = ({ icon, title }) => {
  return (
    // <div className="tw-bg-white tw-flex tw-items-center tw-gap-2.5 tw-py-[15px] tw-px-2.5 md:tw-py-[18px] md:tw-px-[12px] lg:tw-py-[24px] lg:tw-px-[16px] xl:tw-py-[28px] xl:tw-px-[20px] tw-rounded-full tw-shadow-small-service_card">
    <div className="tw-bg-white tw-flex tw-items-center tw-gap-2.5 tw-p-2.5 md:tw-p-3 xl:tw-p-4 tw-rounded-full tw-shadow-small-service_card">
      <div className="tw-bg-[#E0FBD7] tw-rounded-full tw-p-2.5 md:tw-p-3 xl:tw-p-4">
        <div className=" tw-relative tw-aspect-[1] tw-w-[15px] md:tw-w-[18px] lg:tw-w-[20px] 2xl:tw-w-6">
          <Image fill src={icon} alt={title} className="tw-object-contain" />
        </div>
      </div>
      <h3 className="tw-font-medium tw-text-[16px] lg:tw-text-[18px] xl:tw-text-[20px] 2xl:tw-text-[24px] tw-mb-0">
        {title}
      </h3>
    </div>
  );
};
