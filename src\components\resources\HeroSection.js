"use client";
import React, { useLayoutEffect, useState } from 'react';
import { Container } from 'reactstrap';
import Image from 'next/image';
import FillButton from '../buttons/FillButton';

const HeroSection = () => {
    const images = [
        {
            id: 1, src: "/resourcePage/1.png", alt: "Gallery image 1", rotation: -8,
            className: "tw-w-[250px] tw-h-[250px] tw-left-[0px] tw-top-[48px] tw-max-md:tw-w-[200px] tw-max-md:tw-h-[200px] tw-max-md:tw-left-[0px] tw-max-md:tw-top-[36px] tw-max-sm:tw-w-[150px] tw-max-sm:tw-h-[150px] tw-max-sm:tw-left-[0px] tw-max-sm:tw-top-[25px]"
        },
        {
            id: 2, src: "/resourcePage/2.png", alt: "Gallery image 2", rotation: -5,
            className: "tw-w-[250px] tw-h-[250px] tw-left-[128px] tw-top-[36px] tw-max-md:tw-w-[200px] tw-max-md:tw-h-[200px] tw-max-md:tw-left-[105px] tw-max-md:tw-top-[25px] tw-max-sm:tw-w-[150px] tw-max-sm:tw-h-[150px] tw-max-sm:tw-left-[100px] tw-max-sm:tw-top-[20px]"
        },
        {
            id: 3, src: "/resourcePage/3.png", alt: "Gallery image 3", rotation: -2,
            className: "tw-w-[250px] tw-h-[250px] tw-left-[256px] tw-top-[28px] tw-max-md:tw-w-[200px] tw-max-md:tw-h-[200px] tw-max-md:tw-left-[210px] tw-max-md:tw-top-[20px] tw-max-sm:tw-w-[150px] tw-max-sm:tw-h-[150px] tw-max-sm:tw-left-[190px] tw-max-sm:tw-top-[15px]"
        },
        {
            id: 4, src: "/resourcePage/4.png", alt: "Gallery image 4", rotation: 0,
            className: "tw-w-[250px] tw-h-[250px] tw-left-[384px] tw-top-[28px] tw-max-md:tw-w-[200px] tw-max-md:tw-h-[200px] tw-max-md:tw-left-[315px] tw-max-md:tw-top-[20px] tw-max-sm:tw-w-[150px] tw-max-sm:tw-h-[150px] tw-max-sm:tw-left-[280px] tw-max-sm:tw-top-[15px]"
        },
        {
            id: 5, src: "/resourcePage/5.png", alt: "Gallery image 5", rotation: 2,
            className: "tw-w-[250px] tw-h-[250px] tw-left-[512px] tw-top-[28px] tw-max-md:tw-w-[200px] tw-max-md:tw-h-[200px] tw-max-md:tw-left-[420px] tw-max-md:tw-top-[20px] tw-max-sm:tw-w-[150px] tw-max-sm:tw-h-[150px] tw-max-sm:tw-left-[370px] tw-max-sm:tw-top-[15px]"
        },
        {
            id: 6, src: "/resourcePage/6.png", alt: "Gallery image 6", rotation: 5,
            className: "tw-w-[250px] tw-h-[250px] tw-left-[640px] tw-top-[36px] tw-max-md:tw-w-[200px] tw-max-md:tw-h-[200px] tw-max-md:tw-left-[525px] tw-max-md:tw-top-[25px] tw-max-sm:tw-w-[150px] tw-max-sm:tw-h-[150px] tw-max-sm:tw-left-[460px] tw-max-sm:tw-top-[20px]"
        },
        {
            id: 7, src: "/resourcePage/7.png", alt: "Gallery image 7", rotation: 8,
            className: "tw-w-[250px] tw-h-[250px] tw-left-[768px] tw-top-[48px] tw-max-md:tw-w-[200px] tw-max-md:tw-h-[200px] tw-max-md:tw-left-[630px] tw-max-md:tw-top-[36px] tw-max-sm:tw-w-[150px] tw-max-sm:tw-h-[150px] tw-max-sm:tw-left-[550px] tw-max-sm:tw-top-[25px]"
        }
    ];

    const cardWidth = 250;
    const [gap, setGap] = useState(110);

    useLayoutEffect(() => {
        const updateGap = () => {
            const width = window.innerWidth;
            if (width >= 1280) return 110;
            if (width >= 1024) return 140;
            if (width >= 768) return 160;
            if (width >= 640) return 180;
            if (width >= 320) return 200;
            return 110;
        };

        const handleResize = () => setGap(updateGap());

        handleResize(); // On mount
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    return (
        <section className="tw-bg-[#F7F9FB] tw-shadow-home_hero_section lg:tw-pb-[.9375rem] tw-pb-2">
            <div className="tw-bg-resourceBgDesktop tw-bg-cover lg:tw-rounded-[1.25rem] tw-rounded-[.9375rem] lg:tw-mx-[.9375rem] tw-mx-2 ">
                <Container>
                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center xl:tw-py-[80px] lg:tw-py-[70px] md:tw-py-[50px] tw-py-[90px] tw-text-center lg:tw-gap-[40px] tw-gap-[30px]">
                        <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px]">
                            <h1 className="tw-mb-0 xl:tw-text-[70px] lg:tw-text-[55px] md:tw-text-[40px] tw-text-[38px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold md:tw-w-[78%]">
                                Explore. Learn. Level Up.
                            </h1>
                            <p className="tw-mb-0 tw-text-[22px] lg:tw-text-xl md:tw-text-lg tw-text-base tw-text-primary_gray lg:tw-w-[65%] md:tw-leading-[140%] tw-leading-[120%]">
                                Discover a growing collection of expert-crafted guides, templates, case studies, and tools - designed to help startups, creators, & tech teams grow smarter & faster.
                            </p>
                        </div>
                        <FillButton
                            title="Browse Resources"
                            className="tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-5 tw-py-[8px] tw-px-4"
                        />
                        <div className="tw-relative tw-w-full tw-h-[300px] tw-flex tw-justify-center tw-items-center 2xl:tw-mt-[50px] tw-mt-[30px]">
                            <div className="tw-relative tw-h-[300px] tw-w-full tw-max-w-[1200px]">
                                {images.map((image, index) => {
                                    const centerIndex = Math.floor(images.length / 2);
                                    const offset = index - centerIndex;
                                    const left = offset * (cardWidth - gap);
                                    const curve = Math.pow(offset, 2) * 8;
                                    const zIndex = 1000 - Math.abs(offset);

                                    return (
                                        <div
                                            key={image.id}
                                            className="tw-absolute tw-transition-transform tw-duration-500 tw-ease-in-out tw-rounded-[30px] 
                                            xl:tw-w-[250px] xl:tw-h-[250px] 
                                            lg:tw-w-[200px] lg:tw-h-[200px] 
                                            md:tw-w-[150px] md:tw-h-[150px] 
                                            tw-w-[80px] tw-h-[80px]"
                                            style={{
                                                left: `calc(50% + ${left}px)`,
                                                top: `${curve}px`,
                                                transform: `translateX(-50%) rotate(${image.rotation}deg)`,
                                                zIndex,
                                            }}
                                        >
                                            <Image
                                                src={image.src}
                                                alt={image.alt}
                                                width={500}
                                                height={500}
                                                className="tw-w-full tw-h-full tw-object-cover lg:tw-rounded-[30px] md:tw-rounded-[20px] tw-rounded-[10px] tw-transition-transform tw-duration-500 hover:tw--translate-y-[10%] hover:tw-z-50"
                                            />
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>

                    {/* Resource Images */}

                </Container>
            </div>
        </section>
    );
};

export default HeroSection;
