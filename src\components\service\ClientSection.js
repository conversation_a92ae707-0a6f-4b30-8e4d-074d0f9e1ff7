"use client"
import React, { useRef, useState } from "react";
import { Container } from "reactstrap";
import { ClientCard } from "../card/ClientCard";
import clientImage from "/public/clientsLogo/clientImage2.png";
import clientLogo from "/public/clientsLogo/clientlogo.png";
import Slider from "react-slick";
import { LeftArrowIcon, RightArrowIcon } from "@/utils/icons";

export const ClientSection = ({ className }) => {
  const sliderRef = useRef(null);
  const [count, setCount] = useState(0);
  const clients = [
    {
      image: clientImage,
      logo: clientLogo,
      description:
        "Lorem ipsum dolor sit amet consectetur. Diam ultrices feugiat hendrerit non neque dis eget sed. Ipsum dolor sit consectetur. Ipsum dolor sit amet consectetur.",
      owner: {
        name: "<PERSON><PERSON><PERSON> <PERSON>",
        position: "Founder and CEO of CAARD",
      },
    },
    {
      image: clientImage,
      logo: client<PERSON>ogo,
      description:
        "Lorem ipsum dolor sit amet consectetur. Diam ultrices feugiat hendrerit non neque dis eget sed. Ipsum dolor sit consectetur. Ipsum dolor sit amet consectetur.",
      owner: {
        name: "Dhairya Shah",
        position: "Founder and CEO of CAARD",
      },
    },
    {
      image: clientImage,
      logo: clientLogo,
      description:
        "Lorem ipsum dolor sit amet consectetur. Diam ultrices feugiat hendrerit non neque dis eget sed. Ipsum dolor sit consectetur. Ipsum dolor sit amet consectetur.",
      owner: {
        name: "Dhairya Shah",
        position: "Founder and CEO of CAARD",
      },
    },
  ];
  const settings = {
    dots: false,
    pauseOnHover: true,
    arrows: false,
    infinite: false,
    swipeToSlide: true,
    // autoplay: true,
    // autoplaySpeed: 2000,
    // speed: 1000,
    beforeChange: (oldIndex, newIndex) => setCount(newIndex),
  };

  const goToPrev = () => {
    if (count > 0) {
      sliderRef.current?.slickPrev();
    }
  };

  const goToNext = () => {
    if (count < clients.length - 1) {
      sliderRef.current?.slickNext();
    }
  };

  return (
    <Container tag="section" className={`tw-flex tw-flex-col tw-gap-y-[50px] ${className}`}>
      <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
        <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
          <span>Proven Results,</span>
          <span className="tw-text-primary_green">Trusted Clients</span>
        </div>
        <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-leading-[120%]">
          {`Hear Directly from Our Clients About Their Journey and the Results We’ve Delivered Together.`}
        </div>
      </div>
      <Slider {...settings} ref={sliderRef}>
        {clients?.map((client, idx) => (
          <div key={idx} className="tw-px-1">
            <ClientCard

              clientImage={client.image}
              Logo={client.logo}
              description={client.description}
              owner={client.owner}
            />
          </div>

        ))}
      </Slider>
      <div className="tw-flex tw-justify-center tw-items-center tw-gap-5 ">
        <button
          className={`tw-rounded-full tw-p-[10px] ${count === 0 ? "tw-bg-[#e0fbd7]" : "tw-bg-primary_green"
            }`}
          onClick={goToPrev}
          disabled={count === 0}
          aria-label="Previous"
        >
          <LeftArrowIcon
            className={`${count === 0 ? "tw-fill-primary_green" : "tw-fill-white"
              }`}
          />
        </button>
        <button
          className={`tw-rounded-full tw-p-[10px] ${count === clients.length - 1
            ? "tw-bg-[#e0fbd7]"
            : "tw-bg-primary_green"
            }`}
          onClick={goToNext}
          disabled={count === clients.length - 1}
          aria-label="Next"
        >
          <RightArrowIcon
            className={`tw-w-6 tw-h-6 ${count === clients.length - 1
              ? "tw-fill-primary_green"
              : "tw-fill-white"
              }`}
          />
        </button>
      </div>
    </Container>
  );
};
