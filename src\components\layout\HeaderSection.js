"use client";
import React, { useState } from "react";
import { Container, Row, Col, Offcanvas, OffcanvasHeader, OffcanvasBody } from "reactstrap";
import Image from "next/image";
import Link from "next/link";
import { CloseIcon, MenuIcon } from "@/utils/icons";
import FillButton from "../buttons/FillButton";
import OutlineButton from "../buttons/OutlineButton";

const HeaderSection = () => {
  const [isOffcanvasOpen, setIsOffcanvasOpen] = useState(false);

  const toggleOffcanvas = () => {
    setIsOffcanvasOpen(prev => !prev);
  };

  const us

  const headerLinks = [
    { name: "Home", href: "/" },
    // {
    //   name: "About",
    //   href: "/about",
    //   submenu: [
    //     { name: "About Us", href: "/about" },
    //     { name: "Our Team", href: "/our-team" },
    //     { name: "Culture And Value", href: "/culture-value" },
    //     { name: "Agile Mindset", href: "/agile-mindset" },
    //     { name: "Our Journey", href: "/journey" },
    //     { name: "Happenings At TST Technology", href: "/happenings-at-tst-technology" },
    //   ],
    // },
    // {
    //   name: "Service", href: "/service", submenu: [
    //     { name: "UI UX Design", href: "/service/ui-ux-design" },
    //     { name: "App Development", href: "/service/app-development" },
    //     { name: "Saas Product Development", href: "/service/saas-product-development" },
    //     { name: "Web Development", href: "/service/web-development" },
    //     { name: "Software Development", href: "/service/software-development" },
    //     { name: "IT Consultant", href: "/service/it-consultant" },
    //     { name: "DevOps as a Service", href: "/service/devops-as-a-service" },
    //     { name: "Virtual CTO as a Service", href: "/service/virtual-cto-as-a-service" },
    //   ]
    // },
    // {
    //   name: "Resource", href: "/resources-library", submenu: [
    //     { name: "Resource Library", href: "/resources-library" },
    //     { name: "Blog", href: "/blog" },
    //   ]
    // },
    // { name: "Portfolio", href: "/portfolio" },
    { name: "Service", href: "/service" },
    { name: "Partners & Praise", href: "/partners-and-praise" },
    { name: "About", href: "/about" },
    { name: "Contact Us", href: "/contact-us" },
  ];

  return (
    <header className="tw-fixed tw-top-0 tw-left-0 tw-right-0 tw-z-50 tw-bg-transparent">
      <div className="tw-bg-primary_color tw-text-white  tw-border-[#FFFFFF33] tw-border-[1.5px] tw-shadow-[0_0_7px_0_#00000008] lg:tw-rounded-[1.25rem] tw-rounded-[.9375rem] lg:tw-my-[.9375rem] lg:tw-mx-16 tw-m-2 lg:tw-py-[.9375rem] lg:tw-px-[1.25rem] tw-p-[15px]">
        <Container fluid>
          <div className="tw-flex lg:tw-gap-5 xl:tw-gap-6 tw-justify-between tw-items-center">

            {/* Logo */}
            <div className="tw-flex lg:tw-gap-5 xl:tw-gap-6 tw-items-center">
              <div xs={9} md={10} lg={1} xxl={1}>
                <Link href="/" className="" title='Logo' aria-label="TST Technology Home">
                  <div className="tw-relative tw-w-[126px] tw-h-[1.25rem]">
                    <Image
                      src="/logo/logo.png"
                      alt="TST Technology Logo"
                      priority
                      className="tw-object-contain"
                      fill
                    />
                  </div>
                </Link>
              </div>

              {/* Navigation Links */}
              <div md={7} lg={7} xxl={8} className="lg:tw-block tw-hidden">
                <nav aria-label="Primary Navigation">
                  <ul className="tw-flex  lg:tw-gap-5 xl:tw-gap-6 tw-text-sm xl:tw-text-base tw-font-medium tw-list-none tw-m-0 tw-p-0">
                    {headerLinks.map((link, index) => (
                      <li
                        key={index}
                        className="tw-relative tw-group   hover:tw-bg-opacity-100"
                      >
                        <Link
                          href={link.href}
                          className="tw-text-primary_gray group-hover:tw-text-third_color tw-no-underline"
                        >
                          {link.name}
                        </Link>

                        {/* Bottom underline */}
                        <div className="tw-w-6 tw-mx-auto tw-h-[2px] tw-bg-third_color tw-rounded-full group-hover:tw-visible tw-invisible"></div>

                        {/* Invisible bridge (prevents losing hover between link & dropdown) */}
                        <div className="tw-absolute tw-left-0 tw-top-full tw-w-full tw-h-4"></div>

                        {/* Dropdown */}
                        {link.submenu && (
                          <ul
                            className="tw-absolute tw-left-1/2 tw--translate-x-1/2 tw-top-full tw-mt-4 tw-bg-white tw-rounded-lg tw-shadow-lg tw-z-50
                    tw-opacity-0 group-hover:tw-opacity-100 tw-invisible group-hover:tw-visible
                    tw-transition-opacity tw-duration-200 tw-w-[250px] tw-text-center tw-list-none tw-px-2 tw-py-3"
                          >
                            {link.submenu.map((sublink, subIndex) => (
                              <li key={subIndex}>
                                <Link
                                  href={sublink.href}
                                  className="tw-block tw-px-6 tw-no-underline tw-py-3 tw-text-sm tw-text-primary_gray hover:tw-bg-third_color/10 hover:tw-text-third_color tw-rounded-lg"
                                >
                                  {sublink.name}
                                </Link>
                              </li>
                            ))}
                          </ul>
                        )}
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            </div>

            {/* Action Buttons */}
            <div md={3} lg={4} xxl={3} className="lg:tw-flex tw-hidden tw-justify-end tw-gap-4">
              <OutlineButton title={'Talk to us'} className={'tw-rounded-[12px] xl:tw-py-[.5rem] xl:tw-px-6 lg:tw-py-[8px] lg:tw-px-4'} />
              <FillButton title={'Get Free Quote'} className={'tw-rounded-[12px] !tw-bg-primary_bg xl:tw-py-[.5rem] xl:tw-px-6 lg:tw-py-[8px] lg:tw-px-4 '} />
            </div>

            {/* Mobile Menu Button */}
            <div xs={2} md={1} className="lg:tw-hidden tw-block">
              <button
                type="button"
                aria-label="Open menu"
                onClick={toggleOffcanvas}
                className="ripple-button tw-relative tw-inline-block  tw-border-third_color tw-text-white tw-bg-primary_bg tw-rounded-[.625rem] tw-p-3 tw-font-medium"
              >
                <MenuIcon />
              </button>
            </div>

          </div>
        </Container>
      </div>

      {/* Mobile Offcanvas Menu */}
      <Offcanvas
        isOpen={isOffcanvasOpen}
        toggle={toggleOffcanvas}
        direction="end"
        className="!tw-bg-primary_color !tw-text-white"
        cssModule={{}}
      >
        {/* <OffcanvasHeader toggle={toggleOffcanvas} className="!tw-pt-7 !tw-w-full">
         
        </OffcanvasHeader> */}
        <div className="tw-flex tw-justify-between tw-items-center tw-w-full tw-pt-6 tw-px-5">
          <div className="tw-relative tw-w-[126px] tw-h-[1.25rem]">
            <Image
              src="/logo/logo.png"
              alt="TST Technology Logo"
              priority
              className="tw-object-contain"
              fill
            />
          </div>
          <button type="button" onClick={toggleOffcanvas} className="tw-text-white">
            <CloseIcon size={35} />
          </button>
        </div>
        <OffcanvasBody className="tw-p-0">
          <nav aria-label="Mobile Navigation">
            <ul className="tw-list-none tw-m-0 tw-p-0">
              {headerLinks.map((link, index) => (
                <li key={index} className="">
                  <Link
                    href={link.href}
                    className="tw-block tw-px-6 tw-py-4  tw-text-white hover:tw-text-third_color hover:tw-bg-[#FFFFFF10] tw-no-underline tw-transition-colors"
                    onClick={toggleOffcanvas}
                  >
                    {link.name}
                  </Link>
                  {/* Submenu for mobile */}
                  {link.submenu && (
                    <ul className="tw-bg-[#FFFFFF05] tw-list-none tw-m-0 tw-p-0">
                      {link.submenu.map((sublink, subIndex) => (
                        <li key={subIndex}>
                          <Link
                            href={sublink.href}
                            className="tw-block tw-px-10 tw-py-3 tw-text-sm tw-text-primary_gray hover:tw-text-third_color hover:tw-bg-[#FFFFFF10] tw-no-underline tw-transition-colors"
                            onClick={toggleOffcanvas}
                          >
                            {sublink.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </li>
              ))}
            </ul>

            {/* Mobile Action Buttons */}
            {/* <div className="tw-p-6 tw-space-y-4">
              <OutlineButton
                title={'Talk to us'}
                className={'tw-w-full tw-rounded-[12px] tw-py-3 tw-px-6'}
              />
              <FillButton
                title={'Get Free Quote'}
                className={'tw-w-full tw-rounded-[12px] !tw-bg-primary_bg tw-py-3 tw-px-6'}
              />
            </div> */}
          </nav>
        </OffcanvasBody>
      </Offcanvas>
    </header>
  );
};

export default HeaderSection;
