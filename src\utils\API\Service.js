import axios from "axios";
import authStorage  from './AuthStorage'
import { NEXT_API_KEY, NEXT_PUBLIC_BASE_URL } from "../constant";
const Services = axios.create({
  baseURL: NEXT_PUBLIC_BASE_URL,
  headers: {
    Accept: "application/json",
    Authorization: `Bearer ${authStorage?.getAuthToken() ? authStorage?.getAuthToken() :""}`,
    'x-api-key': `${NEXT_API_KEY}`,
    // 'X-RapidAPI-Key': `${NEXT_API_KEY}`,
  },
});

export default Services;