import Image from "next/image";
import React from "react";
import { Container } from "reactstrap";
import mapImage from "../../../public/home/<USER>";

const MapSection = ({ className }) => {
  return (
    <Container fluid className={className}>
      <div className="lg:tw-hidden tw-block">
        <Title classes="tw-text-center tw-mb-10 sm:tw-mb-12 " />

        <div className=" tw-relative tw-aspect-video tw-w-full">
          <Image
            fill
            src={mapImage}
            className="tw-object-contain"
            alt="Clients location map image"
          />
        </div>
      </div>

      <div
        className="lg:tw-block tw-hidden tw-bg-no-repeat tw-bg-contain tw-bg-top tw-relative tw-aspect-[2/1] tw-w-full"
        style={{
          backgroundImage: `url("/home/<USER>")`,
        }}
      >
        <Title classes="tw-absolute tw-left-[6.94vw] tw-bottom-[7.78vh] tw-text-left" />
      </div>
    </Container>
  );
};

export default MapSection;

const Title = ({ classes }) => {
  return (
    <div className={`${classes} tw-leading-[120%]`}>
      <h2 className="tw-mb-0 tw-text-secondary_color tw-font-bold tw-text-[32px] sm:tw-text-[36px] lg:tw-text-[40px] xl:tw-text-[48px] 2xl:tw-text-[60px] tw-font-bricolageGrotesque">
        Our Clients
      </h2>
      <h2 className="tw-mb-0 tw-text-white tw-text-[26px] sm:tw-text-[28px] lg:tw-text-[32px] xl:tw-text-[36px] 2xl:tw-text-[42px] tw-font-bold">
        Located Worldwide
      </h2>
      <p className=" tw-mb-0 tw-text-[#D2D2D2] tw-text-[12px] md:tw-text-[14px] ">
        TST Technology provides quality services across the globe.
      </p>
    </div>
  );
};
