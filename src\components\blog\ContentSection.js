import React from "react";
import Image from "next/image";

import youtubeIcon from "/public/blogpage/youtubeIcon.png";
import linkedinIcon from "/public/blogpage/linkedinIcon.png";

const plateforms = [
  {
    name: "LinkedIn",
    url: "https://www.linkedin.com/company/tsttechnology/",
    icon: linkedinIcon,
    className: "tw-bg-[#1275B11A]",
    ringClass: "tw-ring-[#1275B11A]",
  },
  {
    name: "Youtube",
    url: "https://www.youtube.com/c/TSTTechnology",
    icon: youtubeIcon,
    className: "tw-bg-[#FF03021A]",
    ringClass: "tw-ring-[#FF03021A]",
  },
];

export const ContentSection = () => {
  return (
    <section className="tw-bg-contentBgMobile md:tw-bg-contentBgDesktop tw-shadow-happenings_card tw-bg-cover tw-bg-no-repeat tw-bg-center tw-py-[70px] lg:tw-py-0 tw-px-5 lg:tw-px-[60px] xl:tw-px-[80px] tw-w-full sm:tw-w-[70%] lg:tw-w-full tw-h-full tw-rounded-[30px]">
      <div className="tw-grid tw-grid-cols-1 lg:tw-grid-cols-2 tw-gap-12 xl:tw-gap-[90px] tw-items-center ">
        <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center tw-gap-10 md:tw-gap-12 lg:tw-gap-[30px] md:tw-w-[70%] lg:tw-w-full lg:tw-py-[60px] xl:tw-py-[80px] tw-w-full tw-mx-auto">
          <h2 className="tw-font-bricolageGrotesque tw-text-primary_black tw-font-bold tw-text-[26px] md:tw-text-[30px] xl:tw-text-[36px] tw-mb-0">
            Explore our Content
          </h2>

          <div className="tw-grid tw-grid-cols-2 tw-gap-5 md:tw-gap-6 lg:tw-gap-8 tw-w-full">
            {plateforms.map((platform) => (
              <div
                key={platform.name}
                className="tw-col-span-1 tw-flex tw-flex-col tw-items-center tw-p-5 tw-gap-5 tw-bg-white tw-rounded-[20px] tw-shadow-happenings_card tw-backdrop-blur-sm"
              >
                <div className={`tw-w-[60px] tw-aspect-square tw-rounded-full tw-ring-2 ${platform.ringClass} tw-flex tw-justify-center tw-items-center`}>
                  <div className={`tw-w-[50px] tw-aspect-square tw-rounded-full tw-flex tw-justify-center tw-items-center ${platform.className} `}>
                    <Image
                      src={platform.icon}
                      alt={platform.name}
                      width={30}
                      height={30}
                    />
                  </div>
                </div>

                <h3 className="tw-font-inter tw-text-primary_black tw-font-medium tw-text-[20px] tw-leading-[1.2] tw-mb-2 tw-text-center">
                  {platform.name}
                </h3>
              </div>
            ))}
          </div>
        </div>

        <div className="tw-relative tw-aspect-[1.47] tw-w-full">
          <Image
            src="/blogpage/contentImage.png"
            alt="Content Image"
            fill
            className="tw-object-contain"
          />
        </div>
      </div>
    </section>
  );
};
