import { ListIcons } from "@/utils/icons";
import Image from "next/image";
import React from "react";

export const BusinessCard = ({ title, icon, serviceList }) => {
  return (
    <div className="tw-bg-white tw-shadow-[0px_1px_2px_0px_#5569871A] tw-min-h-[484px] tw-h-full tw-flex tw-flex-col tw-gap-[15px] tw-items-start tw-p-[32px] tw-rounded-[20px]">
      <div className="tw-relative tw-w-[50px] tw-h-[50px]">
        <Image
          src={icon}
          alt={`${title} Icon`}
          className="tw-aspect-square tw-object-contain tw-w-full tw-rounded-[12px]"
        />
      </div>

      <div className="tw-font-bricolageGrotesque tw-font-semibold tw-text-[20px] lg:text-[24px] tw-leading-[130%]">{title}</div>

      <ul className="tw-list-none tw-mb-0 tw-pl-0 tw-flex tw-flex-col tw-gap-[15px]">
        {serviceList &&
          serviceList.map((item, idx) => (
            <li key={idx} className="tw-flex tw-gap-2.5 tw-items-start">
              <span className="tw-shrink-0 tw-mt-[2px]"><ListIcons className="tw-fill-primary_green tw-h-2.5 tw-w-2" /></span>
              <span className="tw-text-primary_gray tw-font-inter tw-text-[14px] tw-leading-[120%]">{item}</span>
            </li>
          ))}
      </ul>
    </div>
  );
};
