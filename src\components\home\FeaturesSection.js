import React from "react";
import { ArchitectureIcon, DevelopmentIcon, KeyIcon, LifecycleIcon, TeamIcon } from "@/utils/icons";
import { Container } from "reactstrap";
import FillButton from "../buttons/FillButton";
export const FeaturesSection = ({ className }) => {
  const quadrants = [
    {
      bgStyle: { backgroundImage: `url(/home/<USER>
      iconBg: "tw-bg-[#029688]",
      title: "Sustainable Architecture",
      description: "Engineered for speed, scalability, and long-term security.",
      icon: <ArchitectureIcon className='lg:tw-w-[30px] lg:tw-h-[30px] tw-w-[18px] tw-h-[18px]' />,
      className: "md:tw-bg-topRight tw-bg-topRightMobile",
    },
    {
      iconBg: "tw-bg-[#2196F3]",
      title: "Agile lifecycle",
      description:
        "Adapt quickly and deploy faster with secure, iterative development.",
      icon: <LifecycleIcon className='lg:tw-w-[30px] lg:tw-h-[30px] tw-w-[18px] tw-h-[18px]' />,
    },
    {
      iconBg: "tw-bg-[#9C7BFB]",
      title: "Dedicated Team",
      description:
        "Work with specialists committed to your goals and project success.",
      icon: <TeamIcon className='lg:tw-w-[30px] lg:tw-h-[30px] tw-w-[18px] tw-h-[18px]' />,
    },
    {
      bgStyle: { backgroundImage: `url(/home/<USER>
      iconBg: "tw-bg-[#FF9800]",
      title: "Development Transparency",
      description:
        "Gain full visibility with real-time updates and seamless transactions.",
      icon: <DevelopmentIcon className='lg:tw-w-[30px] lg:tw-h-[30px] tw-w-[18px] tw-h-[18px]' />,
      className: "md:tw-bg-bottomLeft tw-bg-bottomLeftMobile",
    },
  ];

  return (
    <Container className={className} tag="section">
      <div className="tw-flex tw-flex-col tw-items-center md:tw-gap-2.5 tw-gap-2">
        <h2 className="tw-text-primary_black md:tw-text-4xl tw-text-2xl tw-font-bold tw-text-center tw-font-bricolageGrotesque tw-mb-0">
          <span>
            AI-Powered Scalable {" "}
          </span>
          <span className="tw-text-primary_green">
            Software Solutions
          </span>
        </h2>

      </div>
      <div className="tw-relative tw-mx-auto md:tw-py-[50px] tw-py-[40px] tw-rounded-lg tw-overflow-hidden tw-bg-white">
        <div className="tw-grid tw-grid-cols-2 tw-grid-rows-2 tw-w-full tw-h-full">
          {quadrants.map((q, index) => (
            <div
              key={index}
              className={`tw-relative xl:tw-p-[80px] lg:tw-p-[60px] md:tw-p-[30px] tw-px-[15px] tw-py-[30px] tw-grid  ${q.className} tw-bg-no-repeat tw-bg-contain`}
              style={{
                backgroundPosition: index === 3 ? "" : "bottom right",
              }}
            >
              <div className="tw-grid tw-place-items-center">
                <div
                  className={`${q.iconBg} lg:tw-p-[15px] tw-p-[10px] lg:tw-rounded-[10px] tw-rounded-lg tw-text-white tw-w-fit lg:tw-mb-[40px] tw-mb-[24px]`}
                >
                  {q.icon}
                </div>
                <h3 className="lg:tw-text-[24px] md:tw-text-[22px] tw-text-[18px] tw-font-medium tw-leading-[120%]  tw-text-center tw-text-gray-800 tw-mb-0 tw-font-bricolageGrotesque tw-line-clamp-2 ">
                  {q.title}
                </h3>
                <p className="lg:tw-text-[18px] md:tw-text-[16px] tw-text-[12px] tw-font-normal tw-leading-[120%]  tw-text-center tw-text-primary_gray tw-max-w-[362px] tw-mt-2 tw-mb-0">
                  {q.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Center Icon */}

        <div className="tw-absolute tw-left-1/2 tw-top-1/2 tw--translate-x-1/2 tw--translate-y-1/2 tw-z-20">
          <div className="tw-relative">
            <div className="tw-animate-spin-slow lg:tw-w-[140px] lg:tw-h-[140px] tw-w-[70px] tw-h-[70px]  tw-bg-contain tw-bg-center tw-bg-no-repeat tw-flex tw-items-center tw-justify-center md:tw-bg-round tw-bg-roundMobile" />
            <div className="tw-absolute tw-top-1/2 tw-left-1/2 tw-transform -tw-translate-x-1/2 -tw-translate-y-1/2">
              <div className="lg:tw-w-[60px] lg:tw-h-[60px] tw-w-[30px] tw-h-[30px]  tw-border-[3px] tw-border-[#35b729] tw-rounded-full tw-flex tw-items-center tw-justify-center tw-shadow-lg tw-bg-[#35b729]">
                <KeyIcon className='lg:tw-w-[30px] lg:tw-h-[30px] tw-w-[15px] tw-h-[15px]' />
              </div>
            </div>

          </div>
        </div>
      </div>
      <div className="tw-flex tw-justify-center">
        <FillButton title={'Book A 30 Min Call'} className={'tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-5 tw-py-[8px] tw-px-4'} />
      </div>

    </Container>

  );
};
