import React from "react";
import { CraftSection } from "./CraftSection";
import { IndustriesSection } from "./IndustriesSection";
import { ValuesSection } from "./ValuesSection";
import { LeaderSection } from "./LeaderSection";
import { PartnershipsSection } from "../home/<USER>";
import { StatsSection } from "./StatsSection";
import JourneySection from "./JourneySection";
import HeroSection from "./HeroSection";
import DetailSection from "../singleService/UIUX/DetailSection";
import HappeningsSection from "./HappeningsSection";
import MissionSection from "./MissionSection";
import DriveTST from "./DriveTST";
import { JourneyPage } from "./journey/JourneyPage";
import { ContactUsSection } from "./journey/ContactUsSection";
import TalkingTeamSection from "../home/<USER>";
import PhilosophySection from "./PhilosophySection";
import MeetOurTeam from "./MeetOurTeam";
import OurTeam from "./OurTeam";

export const AboutPage = () => {
  return (
    <>
      <HeroSection />
      <DetailSection
        className="md:tw-py-[100px] tw-py-[70px]"
        description={<>
          <p>
            We started it because too many great ideas were being held back — stuck between overpriced agencies and unreliable freelancers. Startups, founders,  and small businesses were being forced to choose between quality, speed, and affordability.
          </p>
          <p className="tw-mt-8">
            <span className="tw-italic">We thought: Why not offer all three?</span> So we built OneBuild — a global team that delivers EU-standard software, fixed, transparent pricing, and a clean, modern process that just makes sense.
          </p>
        </>}
      />
      <MissionSection className="md:tw-my-[100px]  tw-my-[70px]" />
      <DriveTST className="md:tw-py-[100px] tw-py-[70px] tw-overflow-hidden" />
      {/* <StatsSection className="md:tw-py-[100px]  tw-py-[70px]" /> */}
      {/* <JourneySection className="md:tw-py-[100px]  tw-py-[70px]" /> */}

      <PhilosophySection className="md:tw-py-[100px] tw-py-[70px] " />
      <OurTeam className="md:tw-py-[100px] tw-py-[70px] " />
      <MeetOurTeam
        title={<>
          Meet Our <span className="tw-text-secondary_color">Team</span>
        </>}
        description="Led by vision, driven by collaboration — meet the people shaping tomorrow’s digital solutions."
      />
      {/* <ValuesSection className="md:tw-py-[100px] tw-py-[70px]" />
      <IndustriesSection />
      <CraftSection className="md:tw-py-[100px] tw-py-[70px]" />
      <PartnershipsSection className="md:tw-py-[100px]  tw-py-[70px]" />
      <LeaderSection />
      <HappeningsSection className="md:tw-pt-[100px] tw-pt-[70px]" /> */}

      <TalkingTeamSection
        plainColorTitle={"Let’s Build Something Great – Talk to"}
        colorTitle={"Our Team"}
        description={"We offer a range of services. Whether you are a startup or an enterprise, you can choose from our services. We customise them for all our clients, so you get a high quality product."}
        className="  tw-max-w-5xl tw-mx-auto" />
      {/* <ContactUsSection /> */}
    </>
  );
};
