"use client";
import { Container } from "reactstrap";
import Image from "next/image";
import { DiamondIcon } from "@/utils/icons";
import FillButton from "../buttons/FillButton";
const DecorativeLine = ({
  src,
  mobileSrc,
  alt,
  className,
  mobileClassName,
}) => (
  <>
    <div className={`tw-hidden md:tw-block tw-absolute ${className}`}>
      <Image src={src} alt={alt} fill className="tw-object-contain" />
    </div>
    {mobileSrc && <div className={`tw-block md:tw-hidden tw-absolute ${mobileClassName}`}>
      <Image src={mobileSrc} alt={alt} fill className="tw-object-contain" />
    </div>}
  </>
);
const DriveCard = ({ title, desc, icon, subTitle }) => (
  <div className="tw-z-10 tw-relative tw-flex tw-flex-col tw-items-center tw-gap-[10px] tw-bg-[#FFFFFF14] tw-backdrop-blur-[40px] tw-p-2 md:tw-p-5 xl:tw-p-10 tw-rounded-lg md:tw-rounded-[24px]">
    <span className="tw-absolute tw-top-3 tw-left-3 lg:tw-top-5 lg:tw-left-5">
      <DiamondIcon className="tw-w-[12px] tw-h-[12px] md:tw-w-[24px] md:tw-h-[24px]" />
    </span>
    <span className="tw-absolute tw-top-3 tw-right-3 lg:tw-top-5 lg:tw-right-5">
      <DiamondIcon className="tw-w-[12px] tw-h-[12px] md:tw-w-[24px] md:tw-h-[24px]" />
    </span>

    <div className="tw-text-white tw-text-[12px] md:tw-text-base lg:tw-text-xl tw-font-bold tw-text-center tw-font-bricolageGrotesque tw-mt-[32px] xl:tw-mt-[28px]">
      {title}
    </div>
    <div className="tw-text-white/80 tw-text-[10px] md:tw-text-sm lg:tw-text-base tw-font-medium tw-text-center tw-font-bricolageGrotesque tw-mt-[8px] xl:tw-mt-[4px]">
      {subTitle}
    </div>

    <div className="tw-text-white/60 tw-text-[10px] md:tw-text-sm xl:tw-text-base tw-font-inter tw-text-center">
      {desc}
    </div>

    <div
      className="tw-absolute tw-z-20 -tw-top-3 md:-tw-top-7 tw-left-1/2 -tw-translate-x-1/2 tw-transform tw-flex tw-items-center tw-justify-center tw-w-[27px] tw-h-[27px] md:tw-w-[54px] md:tw-h-[54px] tw-rounded-full tw-bg-[#7D5FBE]"
      style={{
        backdropFilter: "blur(60px)",
        boxShadow: "4px 4px 8px 0px #FFFFFF59 inset",
      }}
    >
      <div className="tw-absolute tw-inset-0 tw-flex tw-items-center tw-justify-center tw-z-0">
        <div className="tw-absolute tw-w-[130%] tw-h-[130%] tw-rounded-full tw-border tw-border-[#fff]/20 tw-opacity-80" />
        <div className="tw-absolute tw-w-[170%] tw-h-[170%] tw-rounded-full tw-border tw-border-[#fff]/20 tw-opacity-60" />
      </div>
      <div className="tw-relative tw-w-[15px] tw-h-[15px] md:tw-w-[27px] md:tw-h-[27px] tw-z-10">
        <Image src={icon} alt="icon" fill className="tw-object-contain" />
      </div>
    </div>
  </div>
);
const DriveTST = ({ className }) => {
  return (
    // tw-bg-drive-tst-bg-mobile md:tw-bg-drive-tst-bg  tw-bg-no-repeat tw-bg-top tw-bg-cover xl:tw-h-[1254px] tw-aspect-square
    <div
      className={`tw-relative tw-bg-primary_color tw-w-full  ${className}`}
    >
      <Container className="tw-relative tw-z-10">
        {/* Heading */}
        <div className="tw-text-center tw-flex tw-flex-col tw-items-center tw-gap-2 md:tw-mb-[70px] tw-mb-[40px]">
          <div className="tw-text-white tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            Why Choose
            <span className="tw-text-[#7D5FBE]"> OneBuild</span>
          </div>
          {/* <div className="tw-text-primary_gray tw-font-inter tw-w-[80%] tw-leading-[140%] tw-text-[10px] lg:tw-text-[14px]">
            Our core pillars shape every solution we build — driven by mindset,
            methods, and values that empower growth, spark innovation, and fuel
            lasting success.
          </div> */}
        </div>

        {/* Decorative Lines */}
        <div className="tw-absolute tw-inset-0 tw-pointer-events-none tw-z-0">
          <DecorativeLine
            src="/aboutPage/driveTST/left.png"
            // src="/aboutPage/driveTST/line-left.svg"
            // mobileSrc="/aboutPage/driveTST/line-left-mobile.png"
            alt="line left"
            className="md:tw-w-[287px] md:tw-h-[119px] md:tw-left-[18%] md:tw-top-[233px]"
            mobileClassName="tw-w-[38px] tw-h-[97px] tw-left-[24%] 425:tw-top-[170px] tw-top-[200px]"
          />
          <DecorativeLine
            // src="/aboutPage/driveTST/line-right.svg"
            src="/aboutPage/driveTST/right.png"
            // mobileSrc="/aboutPage/driveTST/line-right-mobile.png"
            alt="line right"
            className="md:tw-w-[287px] md:tw-h-[119px] md:tw-right-[18%] md:tw-top-[233px]"
            mobileClassName="tw-w-[38px] tw-h-[97px] tw-right-[24%] 425:tw-top-[170px] tw-top-[200px]"
          />
          <div className="tw-relative tw-left-1/2 md:tw-w-[4px] tw-w-[2px] xl:tw-h-[346px] lg:tw-h-[300px] md:tw-h-[290px] tw-h-[200px] 425:tw-h-[190px] lg:tw-top-[350px] md:tw-top-[335px] tw-top-[180px] 425:tw-top-[220px] 320:tw-top-[270px]">
            <Image
              src="/aboutPage/driveTST/center.png"
              // src="/aboutPage/driveTST/line-bottom.svg"
              alt="line bottom"
              fill
              className="tw-object-contain"
            />
          </div>
        </div>

        {/* Cards */}
        <div className="tw-relative tw-z-10 tw-flex tw-flex-col tw-items-center md:tw-gap-10 tw-gap-7 320:tw-gap-4">
          <div className="tw-relative xl:tw-w-[180px] xl:tw-h-[180px] md:tw-w-[160px] md:tw-h-[160px] tw-w-[100px] tw-h-[100px] tw-flex tw-items-center tw-justify-center">
            <div
              className="tw-absolute tw-w-[60px] tw-h-[60px] tw-rounded-full tw-bg-[#7D5FBE]"
              style={{
                filter: "blur(60px)",
                opacity: 1,
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
              }}
            ></div>
            <div
              style={{
                backdropFilter: "blur(60px)",
                boxShadow: "4px 4px 8px 0px #FFFFFF59 inset",
              }}
              className="tw-relative tw-flex tw-items-center tw-justify-center tw-w-[55px] tw-h-[55px] md:tw-w-[100px] md:tw-h-[100px] tw-rounded-full tw-bg-[#7D5FBE] tw-z-10"
            >
              <div className="tw-absolute tw-inset-0 tw-flex tw-items-center tw-justify-center tw-z-0">
                <div className="tw-absolute tw-w-[120%] tw-h-[120%] tw-rounded-full tw-border tw-border-[#fff]/20 tw-opacity-80" />
                <div className="tw-absolute tw-w-[140%] tw-h-[140%] tw-rounded-full tw-border tw-border-[#fff]/20 tw-opacity-60" />
                <div className="tw-absolute tw-w-[160%] tw-h-[160%] tw-rounded-full tw-border tw-border-[#fff]/20 tw-opacity-40" />
                <div className="tw-absolute tw-w-[180%] tw-h-[180%] tw-rounded-full tw-border tw-border-[#fff]/20 tw-opacity-20" />
              </div>
              <div className="tw-relative tw-w-[27px] tw-h-[27px] md:tw-w-[50px] md:tw-h-[50px] tw-z-10">
                <Image
                  src="/aboutPage/driveTST/cup.png"
                  alt="flow"
                  fill
                  className="tw-object-contain"
                />
              </div>
            </div>
          </div>

          {/* Top Two Cards */}
          <div className="tw-grid tw-grid-cols-2 tw-gap-x-[40px] md:tw-gap-x-[220px] lg:tw-gap-x-[280px] xl:tw-gap-x-[320px] tw-mt-2 md:tw-mt-10">
            <DriveCard
              title="Cost-effective"
              subTitle={"Transparent and Cost-Efficient Pricing"}
              desc="Whether through fixed packages or hourly engagements, our pricing is straightforward and tailored to your needs — often 30–50% less than traditional EU agencies, without compromising quality."
              icon="/aboutPage/driveTST/cost.png"
            />
            <DriveCard
              title="Global Team"
              subTitle={"European Quality, Global Efficiency"}
              desc="Led by experienced project managers and powered by top global talent, we deliver premium results at the standards you expect — with faster turnaround and sharper execution."
              icon="/aboutPage/driveTST/team.png"
            />
          </div>

          {/* Bottom Card */}
          <div className="tw-w-full tw-flex tw-justify-center tw-px-[95px] 320:tw-px-[80px] md:tw-px-[170px] lg:tw-px-[250px] xl:tw-px-[351px] tw-mt-[30px] md:tw-mt-[60px] lg:tw-mt-[105px]">
            <DriveCard
              subTitle={"Scalability"}
              title="Scalable by Design"
              desc="We build digital products that are ready to grow — architected for flexibility, expansion, and long-term success from day one."
              icon="/aboutPage/driveTST/growth.png"
            />
          </div>
        </div>
        {/* <div className="tw-flex tw-justify-center md:tw-mt-[70px] tw-mt-[40px]">
          <FillButton
            title={"Ready to grow with us?"}
            className={
              "tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-5 tw-py-[8px] tw-px-4 "
            }
          />
        </div> */}
      </Container>
      <div className="tw-absolute tw-bottom-[36rem]  -tw-right-[6rem]  tw-h-[8rem] tw-w-[16rem] tw-rounded-full tw-bg-[#433878] tw-blur-[110px] " />
    </div>
  );
};

export default DriveTST;
