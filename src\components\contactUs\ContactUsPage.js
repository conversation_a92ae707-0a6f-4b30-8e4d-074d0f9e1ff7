import React from "react";
import HeroSection from "../service/HeroSection";
import { ContactUsSection } from "./ContactUsSection";
import { BuildSection } from "./BuildSection";
import { TestimonialsSection } from "../home/<USER>";
import { FaqSection } from "@/common/FaqSection";
import { MapSection } from "./MapSection";
import { BenefitsSection } from "../home/<USER>";
import { homeBenefitsData, serviceBenefitsData } from "@/utils/constant";

const heroStats = [
  { value: "20", sign: "", label: "Industries Served" },
  { value: "4", sign: "", label: "Years in business" },
  { value: "30", sign: "+", label: "Industry Experts" },
];

export const ContactUsPage = () => {

  return (
    <>
      <HeroSection
        title="Get in Touch with Us"
        subTitle="We create impactful digital experiences through strategy, design, and cutting-edge technology—helping businesses grow with confidence."
        btnText="Let’s Connect"
        states={heroStats}
        className="tw-bg-contactUsHeroBg tw-pt-[2rem] md:tw-pt-[4rem] lg:tw-pt-[6rem] xl:tw-pt-[8rem] 2xl:tw-pt-[11rem]"
      />
      <BenefitsSection
        className="md:tw-py-[100px] tw-py-[30px]"
        data={serviceBenefitsData}
      />
      {/* <ContactUsSection /> */}
      <MapSection className="md:tw-py-[100px] tw-py-[70px]" />
      {/* <BuildSection className="md:tw-py-[100px] tw-py-[70px]" /> */}
      <TestimonialsSection
        isButton={true}
        className="md:tw-py-[100px] tw-py-[70px]"
        blackTitle="What "
        greenTitle="Our Clients Say"
        subTitle="Our team of developers quickly adapts to new technological innovations to provide best solutions to our clients. Our tech stack is AI-enhanced to deliver solutions better and quicker."
      />
      <FaqSection className="md:tw-py-[100px] tw-py-[40px]" />
    </>

  );
};
