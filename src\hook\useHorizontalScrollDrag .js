import { useRef, useEffect } from "react";

export const useHorizontalScrollDrag = () => {
  const ref = useRef(null);

  useEffect(() => {
    const el = ref.current;
    if (!el) return;

    let isDragging = false;
    let startX = 0;
    let scrollStart = 0;

    const down = (e) => {
      isDragging = true;
      el.classList.add("tw-cursor-grabbing");
      startX = e.pageX;
      scrollStart = el.scrollLeft;
    };

    const move = (e) => {
      if (!isDragging) return;
      const x = e.pageX;
      el.scrollLeft = scrollStart - (x - startX);
    };

    const up = () => {
      isDragging = false;
      el.classList.remove("tw-cursor-grabbing");
    };

    el.addEventListener("mousedown", down);
    window.addEventListener("mousemove", move);
    window.addEventListener("mouseup", up);

    return () => {
      el.removeEventListener("mousedown", down);
      window.removeEventListener("mousemove", move);
      window.removeEventListener("mouseup", up);
    };
  }, []);

  return ref;
};
