import React from "react";
import Image from "next/image";
import { Col, Row } from "reactstrap";

export const ValuesCard = ({ image, title, description, isHorizontal }) => {
  return (
   
    <div
      className={`tw-flex tw-gap-[30px] md:tw-gap-[34px] xl:tw-gap-10 tw-items-center tw-justify-center tw-p-[30px] md:tw-p-[34px] xl:tw-p-10 tw-h-full tw-bg-[#FFFFFF4D] tw-shadow-happenings_card tw-rounded-[30px] ${isHorizontal ? "xl:tw-flex-row tw-flex-col" : "tw-flex-col"}`}
    >
      {/* Image Section */}
      <div className="tw-relative tw-aspect-[1.18] tw-w-[200px] tw-flex-shrink-0 tw-rounded-xl tw-overflow-hidden">
        <Image src={image} alt={title} fill className="tw-object-contain" />
      </div>

      {/* Text Content Section */}
      <div className={`tw-flex tw-flex-col tw-gap-[15px] tw-flex-1 ${isHorizontal ? "xl:tw-items-start tw-items-center" : "tw-items-center"}`}>
        <h3 className={`tw-text-primary_black tw-font-semibold tw-text-[26px] tw-leading-[1.2] tw-font-bricolageGrotesque tw-mb-0 ${isHorizontal ? "xl:tw-text-left tw-text-center" : "tw-text-center"}`}>
          {title}
        </h3>
        <p className={`tw-text-primary_gray tw-font-normal tw-font-inter tw-text-[16px] tw-leading-[1.4] tw-mb-0 ${isHorizontal ? "xl:tw-text-left tw-text-center" : "tw-text-center"}`}>
          {description}
        </p>
      </div>
    </div>
  );
};
