"use client";
import { BusinessCard } from "@/components/card/BusinessCard";
import React, { useRef, useState } from "react";
import Slider from "react-slick";

const BusinessSlider = ({ plans }) => {
  const settings = {
    dots: true,
    arrows: false,
    infinite: false,
    speed: 500,
    slidesToShow: 3, // default for >1024px
    slidesToScroll: 3,
    swipeToSlide: true,
    responsive: [
      {
        breakpoint: 1024, // applies when width <= 1024px
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 590, // applies when width <= 450px
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <>
      <Slider {...settings}>
        {plans.map((plan, idx) => (
          <div key={idx} className="lg:tw-p-3 tw-p-2">
            <BusinessCard
              key={idx}
              title={plan.title}
              icon={plan.image}
              serviceList={plan.serviceList}
            />
          </div>
        ))}
      </Slider>
    </>
  );
};

export default BusinessSlider;
