import React from "react";
import { FounderSection } from "./FounderSection";
import { TestimonialsSection } from "@/components/home/<USER>";
import DetailSection from "@/components/singleService/UIUX/DetailSection";
import { EmpowerSection } from "./EmpowerSection";
import HeroSection from "./HeroSection";

export const OurTeamPage = () => {
  return (
    <>
      <HeroSection />
      <DetailSection
        className="md:tw-py-[100px] tw-py-[70px]"
        description={`Every company is built on strong pillars - individuals whose leadership, vision, empathy, and execution shape the path forward. At TST Technology, these pillars don’t just guide growth; they define it. Their innovative thinking and unwavering drive fuel our mission, while their values form the heartbeat of our culture. Together, they embody what we stand for and where we’re headed.`}
      />
      <FounderSection className="md:tw-py-[100px] tw-py-[70px]" />
      <TestimonialsSection
        isButton={true}
        className="md:tw-py-[100px] tw-py-[70px]"
        blackTitle="What "
        greenTitle="Our Clients Say"
        subTitle="Our team of developers quickly adapts to new technological innovations to provide best solutions to our clients. Our tech stack is AI-enhanced to deliver solutions better and quicker."
      />
      <EmpowerSection className="md:tw-py-[100px] tw-py-[70px]" />
    </>
  );
};
