import CountUp from '@/common/CountUp'
import React from 'react'

const StateCard2 = ({ data }) => {
    return (
        <>
            <div className="tw-font-inter tw-text-center md:tw-rounded-[20px] tw-rounded-[12px] tw-backdrop-blur-[100px] tw-border tw-border-white/30">
                <div className="md:tw-py-[20px] md:tw-px-[40px] tw-py-[15px] tw-px-[8px]">
                    <span className="tw-block tw-text-white tw-font-bold tw-text-[24px] md:tw-text-[38px] lg:tw-text-[42px] xl:tw-text-[44px] 2xl:tw-text-[48px]">
                        <CountUp start={0} end={data?.value || 0} duration={30} />{data?.sign}
                    </span>
                    <span className="tw-block tw-text-[#E9E9E9] tw-font-medium tw-text-[10px] md:tw-text-[16px] xl:tw-text-[18px]2xl:tw-text-[20px]">
                        {data?.label}
                    </span>
                </div>

            </div>
        </>
    )
}

export default StateCard2