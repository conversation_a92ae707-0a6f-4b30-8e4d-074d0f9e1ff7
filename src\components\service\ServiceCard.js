import Image from "next/image";
import React from "react";
import { NextArrowIcon, RightArrowIcon } from "@/utils/icons";
import Link from "next/link";
import FillButton from "../buttons/FillButton";

export const ServiceCard = ({
    icon,
    title,
    subtitle,
    link,
    isImgRightSide = true,
    features = [],
    isLastElement,

}) => {
    return (
        // tw-bg-service-card-bg tw-backdrop-blur-200
        <div className={`tw-group  tw-grid  tw-grid-cols-1 lg:tw-grid-cols-2 tw-px-10 tw-py-16 tw-gap-5 lg:tw-gap-10  tw-h-full ${isLastElement ? "tw-border-b-0" : "tw-border-b tw-border-[#CCCCCC]"}`}>

            <div className={`tw-relative  tw-transition-all tw-duration-500 tw-ease-in-out ${isImgRightSide ? "lg:tw-order-2" : "tw-order-1"}`}>
                <Image width={565} src={icon} alt={title} className="" />
            </div>
            <div className={`${isImgRightSide ? "lg:tw-order-1" : "tw-order-2"}`}>
                <h3
                    className="tw-font-medium tw-text-[22px] xl:tw-text-[30px] 2xl:tw-text-[32px] tw-text-white">
                    {title}
                </h3>
                <h4
                    className="tw-font-normal tw-my-5 md:tw-text-lg lg:tw-text-base xl:tw-text-lg 2xl:tw-text-xl tw-text-[#E9E9E9] "
                >
                    {subtitle}
                </h4>
                {features.length > 0 && <div>
                    <p className="tw-text-white tw-font-medium tw-text-xl tw-mb-3">
                        Key Features:
                    </p>
                    <ul className="tw-list-disc">
                        {features.map((feature, index) => (
                            <li key={index} className={`tw-text-[#E9E9E9]   tw-text-lg tw-my-1.5`}>
                                {feature.label}
                            </li>
                        ))}
                    </ul>
                    <div className="tw-flex tw-justify-center lg:tw-justify-start">
                        <FillButton title={"View More"} className={'tw-bg-white !tw-text-[#433878] tw-rounded-full xl:tw-py-3 xl:tw-px-5 tw-py-2 tw-mt-2 tw-px-7  !tw-text-base !tw-font-semibold tw-border-none'} />
                    </div>
                </div>}
            </div>
        </div>
    );
};
