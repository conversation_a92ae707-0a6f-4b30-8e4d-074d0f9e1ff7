// "use client";
// import { DownArrowIcon } from "@/utils/icons";
// import React from "react";

// export const FaqItem = ({ question, answer, isOpen, toggleOpen }) => {
//   return (
//     <div
//       className="tw-bg-white tw-shadow-tech_card w-tw-w-full tw-rounded-[10px] tw-mb-6"
//       onClick={toggleOpen}
//     >
//       {isOpen ? (
//         <div className="tw-flex tw-w-full tw-items-center tw-gap-5 tw-flex-wrap tw-px-5 tw-py-[18px] tw-cursor-pointer">
//           <div className="tw-line-clamp-2 tw-self-stretch tw-flex-1 tw-shrink tw-basis-[0%] tw-gap-5 tw-text-base tw-text-primary_black tw-font-medium tw-my-auto tw-max-md:tw-max-w-full">
//             {question}
//           </div>
//           <span
//             className={` tw-transition-transform ${isOpen ? "tw-rotate-180" : ""
//               }`}
//           >
//             <DownArrowIcon />
//           </span>
//         </div>
//       ) : (
//         <div className="tw-flex tw-items-start tw-w-full tw-gap-5 tw-flex-wrap tw-p-5 tw-cursor-pointer">
//           <div className="tw-flex tw-gap-5 tw-flex-1 tw-shrink tw-basis-[0%] tw-max-md:tw-max-w-full">
//             <div className="tw-min-w-60 tw-w-full tw-flex-1 tw-shrink tw-basis-[0%] tw-max-md:tw-max-w-full">
//               <div className="tw-text-primary_black tw-text-base tw-font-medium tw-max-md:tw-max-w-full tw-line-clamp-2">
//                 {question}
//               </div>
//               <div className="tw-text-[rgba(131,131,135,1)] tw-text-sm tw-font-normal tw-mt-[5px] tw-max-md:tw-max-w-full">
//                 {answer}
//               </div>
//             </div>
//           </div>
//           <span
//             className={` tw-transition-transform ${isOpen ? "tw-rotate-180" : ""
//               }`}
//           >
//             <DownArrowIcon />
//           </span>
//         </div>
//       )}
//     </div>
//   );
// };
"use client";
import { DownArrowIcon } from "@/utils/icons";
import React from "react";

export const FaqItem = ({ question, answer, isOpen, onToggle }) => {
  return (
    <div
      className="tw-bg-transparent tw-shadow-tech_card tw-w-full tw-rounded-[10px] md:tw-mb-6 tw-mb-[18px] tw-cursor-pointer tw-border-1.5 tw-border-[#FFFFFF4D]"
      onClick={onToggle}
    >
      <div className={`tw-flex tw-items-center tw-justify-between tw-px-5 tw-pt-5 ${isOpen ? "tw-pb-0" : "tw-pb-5"}`}>
        <div className="tw-text-base tw-font-medium tw-text-white tw-max-md:tw-max-w-full tw-line-clamp-2">
          {question}
        </div>
        <span className={`tw-transition-transform  ${isOpen ? "tw-rotate-180" : ""}`}>
          <DownArrowIcon />
        </span>
      </div>

      {isOpen && (
        <div className="tw-text-[#FFFFFFB2] tw-text-sm tw-font-normal tw-px-5 tw-pb-5 tw-pt-0 tw-max-md:tw-max-w-full">
          {answer}
        </div>
      )}
    </div>
  );
};
