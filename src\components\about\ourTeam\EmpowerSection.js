import { Col, Container, Row } from "reactstrap";
import React from "react";

export const EmpowerSection = ({ className }) => {
  const values = [
    {
      title: "Empowering Our Team",
      description:
        "Specialized skill-growth roadmaps are given to every teammate by team leaders to help them upskill.",
    },
    {
      title: "Personality Sessions",
      description:
        "Overall life development is given importance with sessions on soft skills like leadership, team building, creativity, etc.",
    },
    {
      title: "Career Counseling",
      description:
        "Team leaders provide regular counselling to teammates to understand their goals, remove their doubts and help them achieve them.",
    },
    {
      title: "Flexible Schedule",
      description:
        "Remote and hybrid work is offered to teammates with genuine reasons as we wish to help teammates thrive.",
    },
    {
      title: "Recognition and Rewards",
      description:
        "Appreciation for work well done breeds consistency and motivation which keeps us going ahead full steam.",
    },
  ];

  return (
    <section className={`tw-bg-empowerbgMobile md:tw-bg-empowerbg tw-bg-cover tw-bg-no-repeat ${className}`}>
      <Container
        className={`tw-flex tw-flex-col tw-items-center tw-gap-[40px] lg:tw-gap-[70px]`}
      >
        <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
          <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            <span>Empowering </span>
            <span className="tw-text-primary_green"> Our Team</span>
          </div>
          <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-leading-[120%]">
            Teammates make TST Technology and it is always our priority to help
            every teammate advance in their life.
          </div>
        </div>

        <Row className="justify-content-center g-3 g-m-4 g-xl-5">
          {values.map((value, index) => {

            const isOddRow = (index % 5) < 3;
            const md = isOddRow ? 4 : 6;

            return (
              <Col key={index} xs="12" sm="6" md={md}>
                <div className="tw-bg-white tw-rounded-[30px] tw-border tw-border-[#DFE4E8] tw-p-[30px] lg:tw-p-[36px_40px] tw-h-full tw-w-full tw-flex tw-flex-col tw-items-start tw-gap-[30px] xl:tw-gap-[40px]">
                  <h1 className="tw-font-bricolageGrotesque tw-text-primary_green tw-font-bold tw-text-[26px] md:text-[30px] xl:tw-text-[36px] tw-leading-[120%] tw-mb-0">
                    {index >= 9 ? "" : "0"}{index + 1}
                  </h1>
                  <div className="tw-flex tw-flex-col tw-gap-[15px]">
                    <h2 className="tw-font-bricolageGrotesque tw-text-primary_black tw-font-bold tw-text-[20px] lg:tw-text-[26px] tw-leading-[120%] tw-mb-0">
                      {value.title}
                    </h2>
                    <h4 className="tw-font-inter tw-text-primary_gray tw-font-normal tw-text-[16px] lg:tw-text-[18px] tw-leading-[120%]  tw-mb-0">
                      {value.description}
                    </h4>
                  </div>
                </div>
              </Col>
            );
          })}
        </Row>
      </Container>
    </section>
  );
};
