"use client";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import footerLogo from "../../../public/logo/footerLogo.png"
import googleLogo from "../../../public/Rating/google.png"
import clutchLogo from "../../../public/Rating/clutch.png"
import glassdoor<PERSON>ogo from "../../../public/Rating/glassdoor.png"
import ISO_9001Logo from "../../../public/Rating/ISO_9001.png"
import ISO_27001Logo from "../../../public/Rating/ISO_27001.png"
import selectedFirms from "../../../public/Rating/selectedFirms.png"

import { BeIcon, DribbleIcon, EmailIcon, FacebookIcon, InstagramIcon, LinkedinIcon, PhoneIcon, PintrestIcon, SendIcon, StarIcon, TwitterIcon } from "@/utils/icons";
import { blurDataURL } from "@/utils/utils";
export const FooterSection = () => {

  const ratingLogos = [
    { id: 1, title: 'googleLogo', url: googleLogo, className: 'tw-w-[100px] tw-h-[40px] tw-mt-1.5', link: "https://www.google.com/search?q=tst+technology&rlz=1C1GCEU_enIN1010IN1010&oq=tst+technology&aqs=chrome..69i57j0i512l9.1032j0j7&sourceid=chrome&ie=UTF-8", rate: '5.0' },
    { id: 2, title: 'clutchLogo', url: clutchLogo, className: 'tw-w-[100px] tw-h-[40px] tw-mt-1.5', link: "https://clutch.co/profile/tst-technology", rate: '4.8' },
    { id: 3, title: 'glassdoorLogo', url: glassdoorLogo, className: 'tw-w-[125px] tw-h-[55px]', link: "https://www.glassdoor.co.in/Overview/Working-at-TST-Technology-EI_IE1234567.11,28.htm", rate: '4.7' },
    { id: 6, title: 'selectedFirmsLogo', url: selectedFirms, className: 'tw-w-[153px] tw-h-[60px]' },
    { id: 4, title: 'ISO_9001Logo', url: ISO_9001Logo, className: 'tw-w-[70px] tw-h-[70px]' },
    { id: 5, title: 'ISO_27001Logo', url: ISO_27001Logo, className: 'tw-w-[70px] tw-h-[70px]' },
  ]
  const footerLinks = [
    {
      category: "About Company",
      links: [
        { title: "About Us", href: "/about" },
        { title: "Contact Us", href: "/contact-us" },
        { title: "Our Team", href: "/our-team" },
        { title: "Culture And Value", href: "/culture-value" },
        { title: "Agile Mindset", href: "/agile-mindset" },
        { title: "Our Journey", href: "/journey" },
        { title: "Happenings At TST Technology", href: "/happenings-at-tst-technology" },
      ],
    },
    {
      category: "Services",
      links: [
        { title: "UI UX Design", href: "/service/ui-ux-design" },
        { title: "App Development", href: "/service/app-development" },
        { title: "Saas Product Development", href: "/service/saas-product-development" },
        { title: "Web Development", href: "/service/web-development" },
        { title: "Software Development", href: "/service/software-development" },
        { title: "IT Consultant", href: "/service/it-consultant" },
        { title: "DevOps as a Service", href: "/service/devops-as-a-service" },
        { title: "Virtual CTO as a Service", href: "/service/virtual-cto-as-a-service" },
      ],
    },
    {
      category: "Other Pages",
      links: [
        { title: "Careers", href: "/careers" },
        { title: "Privacy Policy", href: "/privacy-policy" },
        { title: "Terms and Conditions", href: "/terms" },
        { title: "Disclaimer", href: "/disclaimer" },
        { title: "Sitemap", href: "/sitemap" },
        { title: "FAQs", href: "/faqs" },
      ],
    },
    // {
    //   category: "Resources",
    //   links: [
    //     { title: "Resource Library", href: "/resources" },
    //     { title: "Blogs", href: "/blog" },
    //   ],
    // },
    {
      category: "Contact",
      contacts: [
        {
          type: "phone", label: "+91 9825582469", href: "tel:+919825582469", icon: <PhoneIcon className='md:tw-w-6 md:tw-h-6 tw-w-4 tw-h-4' />
        },
        {
          type: "email", label: "<EMAIL>", href: "mailto:<EMAIL>", icon: <EmailIcon className=' tw-stroke-white md:tw-w-6 md:tw-h-6 tw-w-4 tw-h-4 tw-shrink-0' />
        },
      ],
      socials: [
        { id: 3, title: "Twitter", icon: <TwitterIcon />, href: "/" },
        { id: 3, title: "Facebook", icon: <FacebookIcon />, href: "/" },
        { id: 1, title: "Instagram", icon: <InstagramIcon />, href: "/" },
        { id: 2, title: "Linkedin", icon: <LinkedinIcon />, href: "/" },

      ],
    },
  ];

  return (
    <footer className=" tw-bg-primary_color tw-flex tw-w-full tw-flex-col tw-overflow-hidden tw-items-start tw-font-semibold 2xl:tw-pt-[300px] xl:tw-pt-[220px] lg:tw-pt-[200px] md:tw-pt-[160px] tw-pt-[80px] tw-pb-5 md:tw-px-20 tw-px-5 tw-max-md:tw-max-w-full tw-max-md:tw-mt-10 tw-max-md:tw-pt-[100px] tw-max-md:tw-px-5">
      {/* <div className="tw-relative">
        <div
          className="tw-animate-spin-slow lg:tw-w-[140px] lg:tw-h-[140px] tw-w-[100px] tw-h-[100px] tw-bg-contain tw-bg-center tw-bg-no-repeat tw-flex tw-items-center tw-justify-center tw-bg-footer-logo-round">

        </div>
        <div className="tw-absolute tw-top-1/2 tw-left-1/2 tw-transform -tw-translate-x-1/2 -tw-translate-y-1/2">
          <div className="tw-relative md:tw-w-[75px] md:tw-h-[75px] tw-w-[53px] tw-h-[53px] tw-bg-[#000323]">
            <Image
              src={footerLogo}
              fill
              className="tw-object-contain"
              alt="Company logo"
            />
          </div>
        </div>

      </div>

      <div className="tw-text-white md:tw-text-[20px] tw-text-[18px] tw-text-center md:tw-mt-10 tw-mt-[30px]">
        TST Tech Matrix PVT. LTD.
      </div>
      <div className="tw-text-white md:tw-text-5xl tw-text-[26px] tw-text-center md:tw-mt-10 tw-mt-[30px] tw-font-bricolageGrotesque">
        Get Our Latest News & Updates
      </div>
      <form className="tw-border tw-flex tw-min-w-[175px] tw-items-center tw-text-sm tw-justify-between tw-mt-10 tw-pl-5 tw-pr-[5px] tw-py-[5px] tw-rounded-[40px] tw-border-[#8093A5] md:tw-min-w-[450px]">
        <div className="tw-flex tw-items-center tw-gap-2.5 tw-justify-center tw-my-auto">
          <div className="tw-flex tw-w-6 tw-shrink-0 tw-h-6 tw-my-auto" ><EmailIcon /></div>
          <input
            type="email"
            placeholder="Email Address"
            className="tw-my-auto !tw-bg-transparent tw-border-none tw-outline-none tw-text-white tw-placeholder-white placeholder:tw-opacity-40 tw-w-full "
            required
          />
        </div>
        <button
          type="submit"
          className=" tw-bg-primary_green tw-w-10  tw-h-10  tw-rounded-[30px]"
          aria-label="Submit"
        ><span className="tw-flex tw-justify-center tw-items-center"><SendIcon /></span></button>
      </form> */}
      {/* <div className="tw-border tw-w-full tw-shrink-0 tw-max-w-full tw-h-px tw-mt-[53px] tw-border-[rgba(255,255,255,0.1)] tw-border-solid tw-max-md:tw-mt-10" /> */}

      <div className={`tw-relative tw-w-[17.75rem] tw-h-[4rem]`}>
        <Image
          src={footerLogo}
          alt="TST Technology Logo"
          priority
          className={`tw-object-contain `}
          placeholder="blur"
          blurDataURL={blurDataURL(126, 28)}
        />
      </div>

      <div className="2xl:tw-container tw-grid xl:tw-grid-cols-4 md:tw-grid-cols-4 tw-grid-cols-2 tw-gap-5 tw-w-full tw-font-normal md:tw-justify-between md:tw-flex-wrap tw-mt-[60px] tw-max-md:tw-mt-10">
        {footerLinks.map((section, index) => {
          // If contact section
          if (section.category === "Contact") {
            return (
              <div key={index} className="">
                <div className="lg:tw-text-xl tw-text-secondary_color">{section.category}</div>
                <div className="tw-flex tw-w-full tw-flex-col tw-items-stretch lg:tw-text-lg tw-text-sm tw-mt-6">
                  {section.contacts?.map((contact, i) => (
                    <div
                      key={i}
                      className={`tw-flex tw-items-center tw-gap-2.5 ${i > 0 ? 'tw-mt-4' : ''}`}
                    >
                      {contact.icon}
                      <Link
                        href={contact.href}
                        className="tw-self-stretch tw-my-auto hover:tw-text-third_color tw-text-[#FFFFFFCC] tw-no-underline tw-transition-colors"
                        title={contact.type}
                      >
                        {contact.label}
                      </Link>
                    </div>
                  ))}
                  {section.socials && (
                    <nav className="tw-w-full tw-text-lg tw-mt-7">
                      <ul className="tw-p-0 tw-flex tw-gap-4">
                        {section.socials.map((item, i) => (
                          <li key={i}>
                            <Link
                              href={item.href}
                              title={item.title}
                              target="_blank"
                            >
                              <span className="tw-w-10 tw-h-10 tw-flex tw-justify-center tw-items-center ">
                                {item.icon}
                              </span>
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </nav>
                  )}
                </div>
              </div>
            );
          }
          // Standard link sections
          return (
            <div key={index} className="">
              <div className="tw-text-secondary_color lg:tw-text-xl">{section.category}</div>
              <nav className="tw-w-full lg:tw-text-lg tw-text-sm tw-mt-6">
                <ul className="tw-p-0">
                  {section.links?.map((link, i) => (
                    <li key={i} className={i > 0 ? "tw-mt-4" : ""}>
                      <Link
                        href={link.href}
                        title={link.title.toLowerCase()}
                        className="tw-text-white/80 tw-no-underline hover:tw-text-third_color tw-transition-colors"
                      >
                        {link.title}
                      </Link>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>
          );
        })}
      </div>

      {/* <div className="tw-border tw-w-full tw-shrink-0 tw-max-w-full tw-h-px tw-mt-[53px] tw-border-[rgba(255,255,255,0.1)] tw-border-solid tw-max-md:tw-mt-10" />
      <nav className="tw-w-full tw-text-lg tw-mt-7">
        <ul className="tw-p-0 tw-flex tw-flex-wrap xl:tw-justify-center lg:tw-justify-between md:tw-justify-center tw-justify-around tw-items-center xl:tw-gap-20  tw-gap-10">
          {ratingLogos?.map((item) => (
            <li key={item?.id}>
              {item?.link ?
                <Link
                  href={item?.link}
                  className="tw-text-white tw-no-underline tw-text-sm tw-flex tw-flex-col tw-justify-center"
                  title={item?.title}
                >
                  <span className="tw-flex tw-gap-2.5 tw-items-center tw-justify-center">
                    <StarIcon /> <span className="tw-mt-0.5">{item?.rate}</span>
                  </span>
                  <div className={`${item?.className} tw-aspect-video tw-relative`}>
                    <Image
                      src={item.url}
                      className="tw-object-contain"
                      fill
                      alt={item?.title}

                    />
                  </div>
                </Link>
                :
                <div className={`${item?.className} tw-aspect-video tw-relative`}>
                  <Image
                    src={item.url}
                    className="tw-object-contain"
                    fill
                    alt={item?.title}
                  />
                </div>
              }
            </li>
          ))}
        </ul>
      </nav> */}
      <div className="tw-w-full tw-text-center tw-text-white tw-mt-3 lg:tw-mt-8 tw-text-sm lg:tw-text-base tw-font-normal">
        &copy; OneBuild Inc. All Rights Reserved.
      </div>
    </footer>
  );
};
