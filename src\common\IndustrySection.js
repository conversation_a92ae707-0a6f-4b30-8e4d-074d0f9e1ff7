"use client";
import {
  I1,
  I2,
  I3,
  I4,
  I5,
  I6,
  I7,
  I8,
  I9,
  I10,
  I11,
  I12,
  I13,
  I14,
  I15,
} from "@/utils/icons";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { Container } from "reactstrap";
import { iconMap } from "@/utils/constant";
import Image from "next/image";
gsap.registerPlugin(ScrollTrigger);
const IconClass =
  "xl:tw-w-[47px] xl:tw-h-[47px] lg:tw-w-[32px] lg:tw-h-[32px] md:tw-w-[26px] md:tw-h-[26px] tw-w-[14px] tw-h-[14px]";

const hiddenTiles = new Set([
  34, 36, 47, 48, 49, 50, 60, 61, 62, 63, 64, 65, 75, 76, 77, 78, 91,
]);
const GridTile = ({ icon, ref, className }) => (
  <div
    ref={ref}
    className={`${icon ? "card-image" : ""
      } 2xl:tw-w-[83px] 2xl:tw-h-[83px] xl:tw-w-[72px] xl:tw-h-[72px]  lg:tw-w-[58px] lg:tw-h-[58px] md:tw-w-[43px] md:tw-h-[43px] tw-w-[20px] tw-h-[20px] xl:tw-rounded-lg md:tw-rounded-md tw-rounded tw-flex tw-items-center tw-justify-center tw-text-white tw-text-xl tw-font-bold ${className}`}
  >
    <div className="tw-relative xl:tw-w-[47px] xl:tw-h-[47px] lg:tw-w-[32px] lg:tw-h-[32px] md:tw-w-[26px] md:tw-h-[26px] tw-w-[14px] tw-h-[14px]">
      {icon ? (
        <Image src={icon} className="tw-object-contain " alt="icons" fill />
      ) : null}
    </div>
  </div>
);

const IndustrySection = ({ className, iconsData, title }) => {
  const rows = 10;
  const cols = 14;
  const totalCells = rows * cols; // = 140
  const listsData = { ...iconMap, ...iconsData };
  const iconCardRefs = useRef([]);
  const iconCardPositions = useRef([]);

  useEffect(() => {
    iconCardRefs.current.forEach((el, i) => {
      if (!el) return;

      const col = iconCardPositions.current[i]?.col ?? 0;

      const isLeft = col <= cols / 2;
      const xStart = isLeft ? -100 : 100;

      gsap.fromTo(
        el,
        {
          x: xStart,
          opacity: 0,
          scale: 1.1,
          filter: "blur(8px)",
        },
        {
          x: 0,
          opacity: 1,
          scale: 1,
          filter: "blur(0px)",
          duration: 2.5,
          ease: "power3.out",
          scrollTrigger: {
            trigger: el,
            start: "top 90%",
            end: "top 50%", // defines the scroll range
            scrub: 1, // makes animation track scroll position
          },
        }
      );
    });
    return () => {
      gsap.killTweensOf(iconCardRefs.current);
    };
  }, []);

  let iconRefIndex = 0;

  return (
    <Container className={`tw-relative ${className}`} tag="section">
      <>
        <div
          className=" cards tw-grid tw-gap-2 tw-grid-flow-row tw-justify-center tw-items-center tw-mx-auto tw-overflow-hidden md:tw-overflow-auto"
          style={{
            gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))`,
          }}
        >
          {Array.from({ length: totalCells }).map((_, i) => {
            if (hiddenTiles.has(i))
              return (
                <div
                  key={i}
                  className="2xl:tw-w-[83px] 2xl:tw-h-[83px] xl:tw-w-[72px] xl:tw-h-[72px] lg:tw-w-[58px] lg:tw-h-[58px] md:tw-w-[43px] md:tw-h-[43px] tw-w-[20px] tw-h-[20px]"
                />
              );
            const iconData = listsData[i] || {};
            const row = Math.floor(i / cols);
            const col = i % cols;

            const refProp =
              iconData.icon != null
                ? (el) => {
                  iconCardRefs.current[iconRefIndex] = el;
                  iconCardPositions.current[iconRefIndex] = { row, col };
                  iconRefIndex++;
                }
                : null;
            return (
              <GridTile
                key={i}
                ref={refProp}
                icon={iconData.icon}
                className={iconData.color}
              />
            );
          })}
        </div>
        <div className="tw-absolute tw-z-40 tw-top-[46%] tw-left-1/2 tw-transform -tw-translate-x-1/2 -tw-translate-y-1/2  tw-text-center tw-px-4">
          <h2 className="tw-text-center tw-font-bold tw-text-white tw-text-[12px] md:tw-text-2xl xl:tw-text-5xl tw-px-16 tw-font-bricolageGrotesque">
            <span className="tw-block">{title}</span>
          </h2>
        </div>
      </>
    </Container>
  );
};

export default IndustrySection;
