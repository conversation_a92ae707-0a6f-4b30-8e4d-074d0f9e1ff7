@tailwind base;
@tailwind components;
@tailwind utilities;

h1 {
  @apply tw-text-[70px] tw-font-semibold tw-leading-[130%];
}

h2 {
  @apply tw-text-[48px] tw-font-bold tw-leading-[120%];
}

h3 {
  @apply tw-text-[36px] tw-font-bold tw-leading-[120%];
}

h4 {
  @apply tw-text-[30px] tw-font-medium tw-leading-[120%];
}

h5 {
  @apply tw-text-[24px] tw-font-medium tw-leading-[120%];
}

h6 {
  @apply tw-text-[20px] tw-font-bold tw-leading-[120%];
}

input::placeholder,
textarea::placeholder {
  @apply tw-text-[#DFE4E8];
}

@layer components {
  .ripple-button::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.4s ease;
    z-index: 0;
  }

  .ripple-button:active {
    scale: 0.98;
  }

  .ripple-button:hover::after {
    width: 300px;
    /* large enough to fill any button shape */
    height: 300px;
    opacity: 1;
  }

  .ripple-button {
    overflow: hidden;
    position: relative;
    z-index: 1;
  }

  .ripple-fill-button::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: linear-gradient(95.94deg, #602a9a 2.68%, #7d5fbe 97.07%);
    /* Change to your desired color */
    border-radius: 9999px;
    /* fully rounded */
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.6s ease;
    z-index: 0;
  }

  .ripple-fill-button:active {
    scale: 0.98;
  }

  .ripple-fill-button:hover::after {
    width: 300px;
    height: 300px;
    opacity: 1;
  }

  .ripple-fill-button {
    overflow: hidden;
    position: relative;
    z-index: 1;
  }
}

:root {
  --background: #121212;
  --foreground: #977fcb;
  /* --background: #f7f9fb;
  --foreground: #212125; */
}
body {
  background: var(--background) !important;
  color: var(--foreground);
  font-family: "Inter", sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow-x: hidden;
}

.header {
  backdrop-filter: blur(20px);
  content-visibility: initial;
}

.testbgImage {
  background-image: url("/home/<USER>");
  background-size: contain;
  background-repeat: no-repeat;
}

.backgroundImage {
  background-image: url("/home/<USER>");
  background-size: cover;
  background-position: 50% 0%;
  /* aspect-ratio: 16 / 9; */
  border-radius: 20px;
}

.footer-backgroundImage {
  background-image: url("/halfCircle.png");
  background-size: cover;
  background-position: top;
  aspect-ratio: 16 / 9;
  background-repeat: no-repeat;
}
/* @media screen and (min-width: 1520) {
  .backgroundImage {
    aspect-ratio: 21 / 10;
  }
} */

@media screen and (max-width: 425px) {
  .backgroundImage {
    background-image: url("/home/<USER>");
    border-radius: 15px;
  }
}

.bgBlur {
  /* create 4 linear gradients fading from center to edges */
  mask-image: linear-gradient(
      to right,
      transparent 0%,
      black 5%,
      black 95%,
      transparent 100%
    ),
    linear-gradient(
      to bottom,
      transparent 0%,
      black 5%,
      black 95%,
      transparent 100%
    );
  -webkit-mask-image: linear-gradient(
      to right,
      transparent 0%,
      black 5%,
      black 95%,
      transparent 100%
    ),
    linear-gradient(
      to bottom,
      transparent 0%,
      black 5%,
      black 95%,
      transparent 100%
    );
  mask-composite: intersect;
  /* combine both masks to get square fade */
  -webkit-mask-composite: destination-in;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-position: center;
  mask-size: 100% 100%;
  -webkit-mask-size: 100% 100%;
}

.Marquee {
  box-sizing: border-box;
  width: 100%;
  padding: 1em;
  display: flex;
  align-items: center;
  overflow: hidden;
  -webkit-mask-image: linear-gradient(
    to right,
    transparent,
    black 10%,
    black 90%,
    transparent
  );
  mask-image: linear-gradient(
    to right,
    transparent,
    black 10%,
    black 90%,
    transparent
  );
}

.Marquee-content {
  display: flex;
  align-items: center;
  animation: marquee 10s linear infinite running;
  gap: 70px;
}

.Marquee-content-craft {
  display: flex;
  align-items: center;
  animation: marquee 20s linear infinite running;
  gap: 20px;
}

.Marquee-content:hover .Marquee-content-craft:hover {
  animation-play-state: paused;
}
.Marquee-content-tech {
  display: flex;
  align-items: center;
  animation: marquee 10s linear infinite running;
  gap: 22px;
}
/* .Marquee-content-tech:hover {
  animation-play-state: paused;
} */
.Marquee-tag {
  margin: 0 0.5em;
  padding: 0.5em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.Marquee-tag:hover {
  /* background: rgba(255, 255, 255, 0.5); */
  transform: scale(1.1);
  cursor: pointer;
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-50%);
  }
}

.custom-bg-gradient {
  /* background: linear-gradient(
    220.21deg,
    rgba(53, 183, 41, 0) 4.19%,
    rgba(53, 183, 41, 0) 27.09%,
    #35b729 50%,
    rgba(53, 183, 41, 0) 72.91%,
    rgba(53, 183, 41, 0) 95.81%
  ); */

  background: linear-gradient(
    220.21deg,
    rgba(151, 127, 203, 0) 4.19%,
    rgba(151, 127, 203, 0) 27.09%,
    #977fcb 50%,
    rgba(151, 127, 203, 0) 72.91%,
    rgba(83, 69, 148, 0) 95.81%
  );

  border-image-slice: 1;
}

.custom-border-gradient::before,
.custom-border-gradient::after {
  content: "";
  position: absolute;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(53, 183, 41, 0) 0%,
    #977fcb 50%,
    rgba(53, 183, 41, 0) 100%
  );
  transition: opacity 0.3s ease;
  opacity: 0;
}

.custom-border-gradient::before {
  top: 0;
}

.custom-border-gradient::after {
  bottom: 0;
}

.group:hover .custom-border-gradient::before,
.group:hover .custom-border-gradient::after {
  opacity: 1;
}

.customer-section-bg {
  background-image: url("/customer-bg.webp");
  background-size: cover;
  background-position: center;
  height: fit-content;
}

.customer-card .custom-border-gradient::before {
  top: 0 !important;
  left: 35% !important;
}

.customer-card .custom-border-gradient::before,
.customer-card .custom-border-gradient::after {
  content: "";
  position: absolute;
  left: 0;
  width: 60%;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(53, 183, 41, 0) 0%,
    #977fcb 50%,
    rgba(53, 183, 41, 0) 100%
  );
  transition: opacity 0.3s ease;
  opacity: 0;
}

.SlideOutButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.5s ease;
  background-color: #dbffd8;
  padding: 0 20px;
}

.SlideOutButton-text {
  white-space: nowrap;
  overflow: hidden;
  opacity: 0;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.5s ease;
  flex-grow: 0;
  max-width: 0;
}

.SlideOutButton-sign {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.5s ease;
  flex-shrink: 0;
}

.SlideOutButton:hover {
  width: 156px;
  border-radius: 12px;
  background-color: #35b729;
}

.SlideOutButton:hover .SlideOutButton-text {
  opacity: 1;
  flex-grow: 1;
  max-width: 100px; /* adjust as needed */
  /* padding-left: 10px; */
}

.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
.custom-notch-polygon {
  clip-path: path(
    "M0 0 H800 V250Q800 300 750 300 H450 A50 50 0 0 1 350 300 H50 Q0 300 0 250 Z"
  );
}

.flip-wrapper {
  perspective: 1000px;
}

.flip-inner {
  transition: transform 0.6s;
  transform-style: preserve-3d;
  position: relative;
  width: 100%;
  height: 100%;
}

.flip-wrapper:hover .flip-inner {
  transform: rotateY(-180deg);
}

.flip-front,
.flip-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: inherit;
  top: 0;
  left: 0;
}

.flip-back {
  transform: rotateY(180deg);
}

.custom-sticky {
  position: sticky;
  top: 50%;
  transform: translateY(-50%);
}

.card-with-corner-border::before {
  content: "";
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 0;
  border-radius: 20px;
  background: radial-gradient(circle at top left, #35b729 0%, transparent 70%)
      top left,
    radial-gradient(circle at top right, #35b729 0%, transparent 70%) top right,
    radial-gradient(circle at bottom left, #35b729 0%, transparent 70%) bottom
      left,
    radial-gradient(circle at bottom right, #35b729 0%, transparent 70%) bottom
      right;
  background-repeat: no-repeat;
  background-size: 100px 100px;
}
@keyframes spin-slow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 12s linear infinite;
  transform-origin: center;
}

.location-card-hover::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  height: 0%;
  width: 100%;
  background-color: white;
  z-index: 0;
  transition: height 0.4s ease;
  border-radius: 10px;
}

.location-card-hover:hover::before {
  height: 100%;
}
