"use client";
import React, { useState } from "react";
import { Container, Row, Col, Button } from "reactstrap";
import hireImg from "../../../public/Hire.png"
import c4 from "../../../public/client/c4.png"
import Image from "next/image";
import { DownArrowIcon, PlayIcon } from "@/utils/icons";
import FillButton from "../buttons/FillButton";
import VideoModal from "@/common/VideoModal";
export const HiringSection = ({ className }) => {
  const listData = [
    {
      id: 1,
      question: "Built for Your Budget:",
      answer: "Get premium results without the premium price  tag. OneBuild is lean, efficient, and fairly priced.",
    },
    {
      id: 2,
      question: "Architecture That Evolves:",
      answer: "Your business won’t stay the same — and  neither should your tech. We plan for tomorrow, not just today.",
    },
    {
      id: 3,
      question: "European Reliability, Global Efficiency:",
      answer: "Top-tier quality, reliable  timelines, and clear communication — the OneBuild way.",
    }
  ]
  const [openStep, setOpenStep] = useState(null);
  const [isPlayVideo, setIsPlayVideo] = useState(false);

  const toggleStep = (id) => {
    setOpenStep(prev => (prev === id ? null : id));
  };
  return (
    <section className={`tw-bg-primary_color ${className}`}>
      <Container
        className=" tw-w-full tw-overflow-hidden tw-px-20 tw-max-md:tw-max-w-full tw-max-md:tw-px-5"
      >
        <div className="tw-grid tw-grid-cols-1 lg:tw-grid-cols-2 tw-gap-10">
          <div className="tw-hidden lg:tw-block">
            <ThumbnailImage setIsPlayVideo={setIsPlayVideo} />
          </div>
          <div  >
            <div className="tw-flex tw-w-full tw-flex-col tw-max-md:tw-max-w-full tw-max-md:tw-mt-10">
              <h2 className="tw-text-third_color xl:tw-text-5xl md:tw-text-4xl tw-text-[28px] tw-font-bold tw-max-md:tw-text-[40px] tw-font-bricolageGrotesque lg:!tw-text-start tw-text-center ">
                3 Key Benefits
              </h2>
              <h3 className="tw-text-white xl:tw-text-4xl md:tw-text-3xl tw-text-2xl tw-font-bold tw-mb-[14px] lg:!tw-text-start tw-text-center">
                Affordable, Scalable, EU quality
              </h3>
              {/* <p className="tw-text-primary_gray md:tw-text-sm tw-text-[14px] xl:tw-w-[85%] lg:tw-w-full md:tw-w-[85%] lg:tw-mx-0 tw-mx-auto tw-mb-0 lg:!tw-text-start tw-text-center">
                Hiring software talent is easy with TST Technology. We streamline
                the process into three clear steps — define your ideal team,
                connect with top candidates, and hire efficiently. Get skilled
                professionals who align with your goals and drive real results.
              </p> */}
              {/* <div className="lg:tw-aspect-[0.84] tw-w-full  tw-flex tw-justify-center lg:tw-hidden tw-mt-[20px] tw-mb-[10px]">
                <Image
                  src={hireImg}
                  className="tw-object-contain "
                  alt="Hiring"
                />
              </div> */}
              <div className=" lg:tw-hidden">
                <ThumbnailImage setIsPlayVideo={setIsPlayVideo} />
              </div>
              {listData.map((item) => {
                const isOpen = openStep === item.id;
                return (
                  <div
                    key={item.id}
                    onClick={() => toggleStep(item.id)}
                    className="tw-border tw-border-[#FFFFFF33] tw-shadow-tech_card tw-cursor-pointer xl:tw-w-[94%] tw-gap-6 tw-flex-wrap xl:tw-mt-10 lg:tw-mt-5 tw-mt-[15px] tw-py-2.5 tw-pl-2.5 tw-pr-5 tw-rounded-xl tw-transition-all"
                  >
                    <div className={`tw-flex tw-justify-between  tw-items-center tw-w-full`}>
                      <div className={`tw-flex tw-items-start tw-gap-5 tw-flex-1 tw-shrink`}>
                        <div
                          className="tw-text-xl tw-bg-third_color tw-text-[#433878] tw-font-bold tw-whitespace-nowrap tw-text-center tw-rounded-[5px] tw-p-2.5 tw-h-[50px] tw-w-[50px]   tw-shrink-0"
                        >
                          {item.id}
                        </div>
                        <div>
                          <div className="tw-text-white tw-text-base tw-font-medium tw-w-full">
                            {item.question}
                          </div>

                          <div
                            className={`tw-overflow-hidden tw-text-[#FFFFFFCC] tw-text-sm tw-mt-[5px] tw-max-h-[500px] tw-opacity-100 tw-visible`}
                          >
                            {item.answer}
                          </div>

                        </div>
                      </div>
                      {/* <span
                        className={`tw-shrink-0 tw-ml-2 tw-transition-transform ${isOpen ? "tw-rotate-180" : ""
                          }`}
                      >
                        <DownArrowIcon />
                      </span> */}
                    </div>
                  </div>
                );
              })}
              <div className="tw-flex tw-justify-center">
                <FillButton title={'Start Your Project'} className={'!tw-bg-white !tw-border-none !tw-text-[#593D96]  xl:tw-mt-[50px] lg:tw-mt-[20px] tw-mt-[40px] tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-10 lg:tw-py-[8px] lg:tw-px-4 tw-px-10 tw-py-[13px]'} />
              </div>
            </div>
          </div>
        </div>
        {isPlayVideo && (
          <VideoModal
            isOpen={isPlayVideo}
            onClose={() => setIsPlayVideo(false)}
            videoSrc={"/videos/PM-Video.mp4"}
          />
        )}
      </Container>
    </section>

  );
};

const ThumbnailImage = ({ setIsPlayVideo }) => {
  return <div className="">
    <div className="custom-bg-gradient tw-rounded-[45px] tw-p-[3px]" >
      <div className="tw-h-full tw-w-full tw-bg-primary_color tw-rounded-[45px] tw-px-3 tw-pt-4 tw-pb-2.5">
        <div className="tw-relative tw-px-1">
          <div className="tw-w-full tw-h-full tw-aspect-[443/540] lg:tw-block tw-flex tw-justify-center tw-rounded-[30px] tw-overflow-hidden">
            <Image
              src={c4}
              className="tw-object-contain tw-rounded-[30px]"
              alt={"parth"}
              fill
            />
          </div>
          <div className="tw-absolute tw-left-1/2 tw-top-1/2 tw-transform -tw-translate-x-1/2 -tw-translate-y-1/2 tw-cursor-pointer" onClick={() => { setIsPlayVideo(true) }}>
            <PlayIcon />
          </div>
        </div>
      </div>
    </div>

  </div>
}