import React from 'react'

const LocationCard = ({ country, type, name, address, phone }) => {
    return (
        <div className="tw-relative  tw-text-white tw-rounded-[10px] tw-border tw-border-[#262B44] tw-min-h-[255px] tw-max-h-full tw-overflow-hidden tw-group hover:tw-text-primary_black tw-transition-colors tw-duration-500 location-card-hover">
            <div className="tw-flex tw-items-center tw-justify-between tw-border-b tw-border-b-[#262B44] group-hover:tw-border-b-[#DFE4E8] tw-p-[15px] tw-relative z-10 tw-transition-colors tw-duration-500">
                <span className="tw-text-lg tw-font-semibold">{country}</span>
                <span className={`${type === 'Headquarter' ? 'tw-text-secondary_color ' : 'tw-text-[#75757A]'} tw-text-sm`}>
                    {type}
                </span>
            </div>

            <div className="tw-space-y-[25px] tw-p-[15px] tw-relative z-10">
                <h2 className="tw-text-xl tw-mb-0">{name}</h2>
                <p className="tw-text-white/70 group-hover:tw-text-[#75757A] tw-text-sm tw-mb-0 tw-line-clamp-3">{address}</p>
                <p className="tw-text-white/70 group-hover:tw-text-[#75757A] tw-mb-0">{phone}</p>
            </div>
        </div>

    )
}

export default LocationCard