import Image from "next/image";
import React from "react";

export const BuiltCard = ({ icon, title, description }) => {
  return (
    <article className="tw-flex tw-flex-col tw-items-center md:tw-items-start tw-gap-2.5 tw-h-full">
      <div className="tw-relative tw-aspect-square tw-w-[40px]">
        <Image
          src={icon}
          alt={`${title}_Icon`}
          fill
          className="tw-object-contain"
        />
      </div>
      <div className="tw-flex tw-flex-col tw-items-center lg:tw-items-start tw-gap-2.5 md:tw-gap-[15px]">
        <h3 className="tw-self-stretch tw-text-center md:tw-text-left tw-text-primary_black tw-text-[22px] lg:tw-text-[24px] tw-font-bold tw-leading-[1.2] tw-mb-0">
          {title}
        </h3>
        <p className="tw-self-stretch tw-text-center md:tw-text-left tw-text-primary_gray tw-text-[16px] tw-font-normal tw-leading-[1.4] tw-mb-0">
          {description}
        </p>
      </div>
    </article>
  );
};
