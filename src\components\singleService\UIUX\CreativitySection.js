"use client";
import { useEffect } from "react";
import { RightArrowIcon2 } from "@/utils/icons";
import Image from "next/image";
import React, { useState } from "react";
import { Container } from "reactstrap";
import mobileUI from "../../../../public/service-page/UI-UX/mobileUI.png";
import mobileUI2 from "../../../../public/service-page/UI-UX/mobileUI2.png";
import mobileUI3 from "../../../../public/service-page/UI-UX/mobileUI3.png";
import mobileUI4 from "../../../../public/service-page/UI-UX/mobileUI4.png";
import <PERSON><PERSON>ogo from "../../../../public/service-page/UI-UX/BeLogo.png";
import half from "../../../../public/service-page/UI-UX/thinkingBGhalf2.png";
import Link from "next/link";
const CreativitySection = ({ className }) => {
  const templateGroups = [
    {
      // logo: BeLogo,
      cards: [
        { image: mobileUI, title: "Moxa Template", subtitle: "MOBILE APP" },
        { image: mobileUI2, title: "FinApp", subtitle: "FINANCE APP" },
      ],
    },
    {
      // logo: mobileUI3,
      cards: [
        { image: mobileUI3, title: "EduLearn", subtitle: "EDUCATION APP" },
        { image: mobileUI4, title: "HealthTrack", subtitle: "HEALTH APP" },
      ],
    },
  ];

  const [groupIndex, setGroupIndex] = useState(0);
  const [animation, setAnimation] = useState("fadeIn");
  const [cardKey, setCardKey] = useState(0); // To trigger animation

  useEffect(() => {
    const interval = setInterval(() => {
      setAnimation("fadeOut");
      setTimeout(() => {
        setGroupIndex((prev) => (prev + 1) % templateGroups.length);
        setAnimation("fadeIn");
      }, 500); // match fadeOut
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  // Get 2 cards to show
  const { cards: visibleCards, logo } = templateGroups[groupIndex];

  return (
    <section className={` ${className} `}>
      <div className="tw-relative">
        <Container className="tw-relative tw-z-10">
          <div className="tw-bg-primary_blue tw-rounded-[30px] tw-p-5 tw-grid tw-gap-y-5 lg:tw-grid-cols-3 lg:tw-items-center lg:tw-justify-center lg:tw-gap-y-0">
            {/* Left card - only on desktop */}
            <div className="tw-hidden lg:tw-block">
              <TemplateCard
                {...visibleCards[0]}
                cardKey={`${cardKey}-left`}
                animation={animation}
              />
            </div>

            <DetailCard logo={logo} animation={animation} />

            {/* Mobile view: both cards below DetailCard */}
            <div className="tw-grid tw-grid-cols-2 tw-gap-x-5 lg:tw-hidden">
              {visibleCards.map((card, idx) => (
                <TemplateCard
                  key={idx}
                  {...card}
                  cardKey={`${cardKey}-right`}
                  animation={animation}
                />
              ))}
            </div>

            {/* Right card - only on desktop */}
            <div className="tw-hidden lg:tw-block">
              <TemplateCard
                {...visibleCards[1]}
                className="tw-ml-auto"
                cardKey={cardKey}
                animation={animation}
              />
            </div>
          </div>
        </Container>

        {/* Bottom background and button */}
        <Container className="tw-relative">
          <div className="tw-absolute tw-z-0 tw-left-0 xl:-tw-bottom-[199px] lg:-tw-bottom-[159px] md:-tw-bottom-[188px] 425:-tw-bottom-[117px] 375:-tw-bottom-[104px] 360:-tw-bottom-[104px] 320:-tw-bottom-[89px] tw-w-full">
            <div className="tw-relative tw-w-full xl:tw-h-[200px] lg:tw-h-[160px] md:tw-h-[190px] 425:tw-h-[118px] 375:tw-h-[105px] 360:tw-h-[105px] 320:tw-h-[90px] tw-aspect-[16/9]">
              <Image
                src={half}
                fill
                alt="Background Half Shape"
                className="tw-object-contain"
              />
            </div>
          </div>
          <div className="tw-absolute xl:-tw-bottom-[179px] lg:-tw-bottom-[139px] md:-tw-bottom-[165px] 425:-tw-bottom-[105px] 375:-tw-bottom-[95px] 360:-tw-bottom-[95px] -tw-bottom-[80px] tw-left-1/2 tw-transform -tw-translate-x-1/2 tw-z-30">
            <Link href="https://www.behance.net/tsttechnology" title="Be" target="_blank" className="tw-no-underline">
              <div
                className={`tw-flex tw-items-center tw-justify-center tw-uppercase ripple-button tw-border-[.0938rem] tw-border-primary_green tw-text-white tw-bg-primary_green tw-rounded-full tw-font-medium tw-text-nowrap tw-relative xl:tw-w-[100px] xl:tw-h-[100px] lg:tw-w-[80px] lg:tw-h-[80px] md:tw-w-[100px] md:tw-h-[100px] tw-p-2.5 425:tw-p-5 375:tw-p-4 360:tw-p-4 xl:tw-text-base md:tw-text-sm tw-text-[12px] 320:tw-text-[10px]`}
              >
                View <br /> More
              </div>
            </Link>
          </div>
        </Container>
      </div>
    </section>
  );
};

export default CreativitySection;

const TemplateCard = ({
  image,
  title,
  subtitle,
  className,
  cardKey,
  animation,
}) => {
  return (
    <div
      key={cardKey}
      className={`${className} tw-relative tw-z-20 xl:tw-w-[330px] xl:tw-h-[423px] lg:tw-w-[235px] lg:tw-h-[305px] tw-w-full md:tw-h-[390px] 425:tw-h-[220px] 375:tw-h-[198px] 360:tw-h-[180px] tw-h-[157px] ${
        animation === "fadeIn" ? "tw-animate-fadeIn" : "tw-animate-fadeOut"
      } `}
    >
      <div className="tw-absolute tw-z-30 md:tw-top-[20px] tw-top-[10px] md:tw-right-[20px] tw-right-[10px] tw-text-white">
        <RightArrowIcon2 className="md:tw-w-10 md:tw-h-10 tw-w-8 tw-h-8 tw-fill-white" />
      </div>
      <Image
        src={image}
        alt={title}
        fill
        className="tw-object-contain tw-z-10"
      />
      <div className="tw-text-white tw-absolute tw-z-30 md:tw-bottom-[26px] md:tw-left-[26px] tw-bottom-[10px] tw-left-[15px]">
        <span className="md:tw-text-sm tw-text-[12px] 320:tw-text-[10px]">
          {subtitle}
        </span>
        <p className="md:tw-text-2xl tw-text-base 320:tw-text-sm tw-font-bricolageGrotesque tw-mb-0">
          {title}
        </p>
      </div>
    </div>
  );
};

const DetailCard = ({ logo, animation }) => {
  return (
    // <div className={`tw-relative tw-text-center tw-text-white `} key={`detail-card-${animation}`}>
    <div className={`tw-relative tw-text-center tw-text-white `}>
      <div className="tw-flex tw-flex-col tw-items-center">
        {/* <div className={`tw-relative ${animation === "fadeIn" ? "tw-animate-fadeIn" : "tw-animate-fadeOut"}`}> */}
        <div className={`tw-relative`}>
          <div className="tw-animate-spin-slow md:tw-w-[100px] md:tw-h-[100px] tw-w-[50px] tw-h-[50px] tw-bg-contain tw-bg-center tw-bg-no-repeat tw-flex tw-items-center tw-justify-center tw-bg-BE-Round"></div>
          <div className="tw-absolute tw-top-1/2 tw-left-1/2 tw-transform -tw-translate-x-1/2 -tw-translate-y-1/2">
            <div className="tw-relative md:tw-w-[50px] md:tw-h-[50px] tw-w-[25px] tw-h-[25px]">
              <Image
                src={BeLogo}
                fill
                className="tw-object-contain"
                alt="Company logo"
              />
            </div>
          </div>
        </div>
        <p className="md:tw-text-[20px] tw-text-sm tw-text-primary_gray tw-mb-0 xl:tw-mt-[40px] md:tw-mt-[28px] tw-mt-[20px]">
          TST TECH MATRIX PVT. LTD.
        </p>
        <h2 className="tw-font-bold xl:tw-text-[48px] md:tw-text-[34px] tw-text-[26px] tw-font-bricolageGrotesque xl:tw-mt-[50px] md:tw-mt-[35px] tw-mt-[25px] tw-mb-0">
          Thinking for Creativity
        </h2>
      </div>
    </div>
  );
};
