import Image from "next/image";
import React from "react";
import { Col, Row } from "reactstrap";
import { QuotesIcon } from "@/utils/icons";
import Link from "next/link";

export const ClientCard = ({ clientImage, Logo, description, owner }) => {
  return (
    <div>
      <Row className="g-md-5 g-4 tw-items-center">
        <Col lg={6}>
          <div className="tw-relative tw-group">
            <div className="tw-relative tw-w-full tw-aspect-video">
              <Image
                src={clientImage}
                className="tw-object-contain tw-rounded-[30px]"
                alt="Hiring"
              />

            </div>
            <div className="tw-opacity-0 tw-pointer-events-none group-hover:tw-opacity-100 group-hover:tw-pointer-events-auto tw-transition-all tw-duration-500 tw-ease-in-out tw-absolute tw-top-0 tw-left-0 tw-bg-project-img-linear tw-h-full tw-overflow-hidden tw-w-full tw-rounded-[30px]">
              <div className="tw-flex tw-justify-center tw-items-end  tw-h-full ">
                <Link className="hover:tw-underline tw-text-white tw-no-underline tw-mb-[30px] tw-text-xl tw-font-medium" href="#">
                  View Case Study
                </Link>   </div>
            </div>

          </div>

        </Col>

        <Col lg={6}>
          <div className="tw-flex tw-flex-col flex xl:tw-gap-[35px] tw-gap-[25px]">
            <QuotesIcon className="tw-aspect-square tw-w-12 md:tw-mb-[15px]" />

            <div className="tw-font-medium xl:tw-text-[30px] tw-text-[20px] tw-leading-[130%] ">
              {description}
            </div>

            <div className="tw-w-full tw-border-t tw-border-[#D5D5D8]"></div>

            <div className="tw-flex tw-justify-between tw-items-center">
              <div>
                <p className="tw-text-primary_black tw-font-semibold md:tw-text-[20px] tw-text-base tw-leading-[120%] tw-font-inter md:tw-mb-2">
                  {owner.name}
                </p>
                <span className="tw-block tw-text-primary_gray md:tw-text-[16px] tw-text-[12px] tw-leading-[120%] tw-font-inter">
                  {owner.position}
                </span>
              </div>
              <div className="tw-relative tw-w-36 tw-h-10">
                <Image
                  src={Logo}
                  className="tw-object-contain"
                  alt="client Company Logo"
                />
              </div>
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
};
