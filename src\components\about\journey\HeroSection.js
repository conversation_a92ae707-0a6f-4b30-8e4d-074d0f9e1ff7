"use client";
import { DownArrowIcon2, SeeHistoryIcon } from "@/utils/icons";
import React from "react";
import { Container } from "reactstrap";



const HeroSection = () => {
  return (
    <section className=" tw-bg-[#F7F9FB] tw-shadow-home_hero_section lg:tw-pb-[.9375rem] tw-pb-2">
      <div className="tw-bg-journeyHeroBgMobile md:tw-bg-journeyHeroBg tw-bg-cover lg:tw-rounded-[1.25rem] tw-rounded-[.9375rem] lg:tw-mx-[.9375rem] tw-mx-2 ">
        <Container>
          <div className="tw-flex tw-flex-col tw-items-center tw-justify-center xl:tw-py-[120px] lg:tw-py-[90px] md:tw-py-[50px] tw-py-[90px] tw-text-center lg:tw-gap-[60px] tw-gap-[50px]">
            <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px]">
              <h1 className="tw-mb-0 xl:tw-text-[70px] lg:tw-text-[55px] md:tw-text-[40px] tw-text-[38px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold md:tw-w-[78%]">
                Our Journey: Small Steps. Bold Leaps. Infinite Possibilities.
              </h1>
              <p className="tw-mb-0 tw-text-[22px] lg:tw-text-xl md:tw-text-lg tw-text-base tw-text-primary_gray lg:tw-w-3/4 xl:tw-w-[55%] md:tw-w-[87%] tw-w-[90%] md:tw-leading-[140%] tw-leading-[120%]">
                Every milestone is a testament to our passion, perseverance, and
                people — shaping a future we&apos;re excited to build.
              </p>
            </div>
            {/*button  */}
            <div className="tw-relative ">
              <SeeHistoryIcon className='md:tw-w-[136px] md:tw-h-[136px] tw-w-[100px] tw-h-[100px] tw-animate-spin-slow-20' />

              {/* Center Icon */}
              <div className=" tw-absolute tw-top-1/2 tw-left-1/2 tw-transform -tw-translate-x-1/2 -tw-translate-y-1/2">
                <DownArrowIcon2 className="md:tw-w-[30px] md:tw-h-[30px] tw-w-[22px] tw-h-[22px]" />
              </div>
            </div>
          </div>
        </Container>
      </div>
    </section>
  );
};

export default HeroSection;
