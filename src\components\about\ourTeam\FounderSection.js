import React from "react";
import { Container } from "reactstrap";

import ceoImg from "/public/aboutPage/ourTeam/ceo.png";
import cooImg from "/public/aboutPage/ourTeam/coo.png";
import ctoImg from "/public/aboutPage/ourTeam/cto.png";
import fmImg from "/public/aboutPage/ourTeam/fm.png";
import agileIcon from "/public/aboutPage/ourTeam/agile.png";
import nodeJsIcon from "/public/aboutPage/ourTeam/nodeJs.png";
import npmIcon from "/public/aboutPage/ourTeam/npm.png";
import productManagerIcon from "/public/aboutPage/ourTeam/productManager.png";
import angelInvestorIcon from "/public/aboutPage/ourTeam/angelInvestor.png";
import startupsIcon from "/public/aboutPage/ourTeam/startups.png";
import harvardIcon from "/public/aboutPage/ourTeam/harvard.png";

import { FillLinkedinIcon } from "@/utils/icons";
import { FoundersCard } from "@/components/card/FoundersCard";

const founders = [
  {
    name: "<PERSON><PERSON>iya",
    role: "Co-founder and CEO",
    description:
      "Hiren Kalariya is a management wizard and an Agile Methodologist. He is the Product Management expert and always keeps brainstorming processes that get work done better and faster. His versatility stems from his adherence to learning discipline. Hiren has the most expertise in product management, shown in TST Technology's first-ever product Toolplate, in which he spearheaded the complete design, development and management. Under his guidance, TST Technology consistently follows Agile processes and delivers successful projects.",
    certifications: [
      {
        text: "Top Project Management Voice",
        icon: <FillLinkedinIcon className="tw-aspect-square tw-w-full" />,
      },
      { text: "Agile Coach", iconPath: agileIcon },
      { text: "Node JS certified Developer", iconPath: nodeJsIcon },
      { text: "Certified Product manager", iconPath: productManagerIcon },
      { text: "Certified Product manager", iconPath: npmIcon },
    ],
    imageUrl: ceoImg,
  },
  {
    name: "Parth Makwana PM",
    role: "Founder and COO",
    description:
      'Parth Makwana is a visionary who dreams of helping others achieve their dreams- the creator of our "Your business is my business" motto. He ensures that all departments have a smooth process for frictionless work. PM is also an Angel Investor for startup and small business growth. He is also a certified Harvard Delegate. PM has a keen eye for finding new talent and initiative takers suitable for growth. He is a firm believer in ethics and punctuality, enforcing that behavior company-wide. Having provided IT consultancy to over 100+ individuals and companies so far along with featuring on podcasts and career guidance sessions, he has amassed a huge following on LinkedIn.',
    certifications: [
      { text: "Angel Investor", iconPath: angelInvestorIcon },
      {
        text: "10k+ Linkedin Followers",
        icon: <FillLinkedinIcon className="tw-aspect-square tw-w-full" />,
      },
      { text: "worked with 30+ starups", iconPath: startupsIcon },
      {
        text: "Top IT Consulting Voice",
        icon: <FillLinkedinIcon className="tw-aspect-square tw-w-full" />,
      },
      { text: "Harvard Delegate", iconPath: harvardIcon },
    ],
    imageUrl: cooImg,
  },
  {
    name: "Daxesh Italiya",
    role: "Co-founder and CTO",
    description:
      "Daxesh is a technical genius with an innovative streak. He's the product creator of an NPM package designed to save time for React Developers in creating forms and an architecture framework that reduces development time by 10x. Daxesh has a broad grasp of most of the technologies and he makes it a point to keep up with all emerging developments as well. His design thinking is also remarkable, balancing his creative and logical side. Wooffer, a server monitoring platform by TST Technology, is Daxesh's brainchild aimed to help developers and business owners by giving all server-related alerts to them and helping reduce server downtimes.",
    certifications: [
      {
        text: "Top Software Development Voice",
        icon: <FillLinkedinIcon className="tw-aspect-square tw-w-full" />,
      },
      { text: "Agile Coach", iconPath: agileIcon },
      { text: "Node JS certified Developer", iconPath: nodeJsIcon },
      { text: "Creator of NPM package", iconPath: npmIcon },
    ],
    imageUrl: ctoImg,
  },
  {
    name: "Anshu Joshi",
    role: "Founding Member",
    description:
      "Anshu is a Founding Member and the Head of Communications at TST Technology. Anshu leads the Founder's Room and oversees the execution of innovative ideas and processes. She keeps herself updated with the latest market trends, sharing stories and anecdotes that motivate teammates to keep working towards their goals. Anshu is versatile as she can tackle any task, learn about it swiftly, and execute it precisely. Known for her impactful use of words, Anshu stands up for what's right and necessary, for keep making positive changes. She creates simple and effective solutions for little problems to improve daily life. She led the content team for TST Technology's product, Toolplate, an AI tools platform. Anshu's dedication to excellence and her ability to inspire those around her make her an invaluable asset to the team.",
    certifications: [
      {
        text: "LinkedIn Certified Content Strategist",

        icon: <FillLinkedinIcon className="tw-aspect-square tw-w-full" />,
      },
    ],
    imageUrl: fmImg,
  },
];

export const FounderSection = ({ className }) => {
  return (
    <section className={` ${className}`}>
      <Container tag="section" className={``}>
        <div className="tw-grid tw-grid-cols-1 tw-gap-[70px]">
          {founders?.map((founder, index) => (
            <FoundersCard key={index} founder={founder} />
          ))}
        </div>
      </Container>
    </section>
  );
};
