"use client";
import gsap from "gsap";
import React, { useRef, useLayoutEffect } from "react";
import { Container } from "reactstrap";
import { usePathname } from "next/navigation";

const cards = [
    { text: "<PERSON><PERSON> & Grow", color: "tw-bg-[#08C0AA]", rotate: "tw-rotate-3" },
    { text: "Shared Success", color: "tw-bg-[#ADADFF]", rotate: "-tw-rotate-3" },
    { text: "Bonding Beyond Work", color: "tw-bg-[#6ED748]", rotate: "tw-rotate-3" },
    { text: "Creative Vibes", color: "tw-bg-[#08B1D7]", rotate: "tw-rotate-3" },
    { text: "Celebrate Together", color: "tw-bg-[#35B729]", rotate: "tw-rotate-0" },
    { text: "Smiles All Around", color: "tw-bg-[#56D8FD]", rotate: "tw-rotate-0" },
];

const groupCards = (cards) => {
    const groups = [];
    for (let i = 0; i < cards.length; i += i % 3 === 0 ? 1 : 2) {
        groups.push(cards.slice(i, i + (i % 3 === 0 ? 1 : 2)));
    }
    return groups;
};

const HeroSection = ({ className }) => {
    const groupedCards = groupCards(cards);
    const titleRef = useRef(null);
    const descRef = useRef(null);
    const cardsRef = useRef([]);
    const pathname = usePathname();

    // 🔁 Clear refs before every render
    cardsRef.current = [];

    useLayoutEffect(() => {
        let ctx;
        requestAnimationFrame(() => {
            ctx = gsap.context(() => {
                gsap.set(cardsRef.current, { opacity: 0, y: -30 });
                gsap.set([descRef.current, titleRef.current], { opacity: 0, y: -20 });

                const tl = gsap.timeline();

                // Animate cards with stagger, label the end
                tl.to(cardsRef.current.reverse(), {
                    opacity: 1,
                    y: 0,
                    duration: 0.4,
                    ease: "power3.out",
                    stagger: 0.1,
                }, "start");

                // Animate description AFTER cards are done
                tl.to(descRef.current, {
                    opacity: 1,
                    y: 0,
                    duration: 0.4,
                    ease: "power2.out",
                }, ">"); // wait until previous ends

                // Animate title after description
                tl.to(titleRef.current, {
                    opacity: 1,
                    y: 0,
                    duration: 0.4,
                    ease: "power2.out",
                }, ">"); // same: run after previous

            });
        });

        return () => ctx?.revert();
    }, [pathname]);


    return (
        <section className={`tw-bg-[#F7F9FB] tw-shadow-home_hero_section tw-bg-happingHeroBg tw-bg-cover tw-bg-[50%_0%] lg:tw-rounded-[1.25rem] tw-rounded-[.9375rem] lg:tw-mx-[.9375rem] tw-mx-2 ${className}`}>
            <Container>
                <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[130px] tw-gap-[50px]">
                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px]">
                        <h1 ref={titleRef} className="tw-mb-0 xl:tw-text-[70px] lg:tw-text-[55px] md:tw-text-[40px] tw-text-[38px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold">
                            Where Innovation Meets Culture
                        </h1>
                        <p ref={descRef} className="tw-mb-0 tw-text-[22px] lg:tw-text-xl md:tw-text-lg tw-text-base tw-text-primary_gray lg:tw-w-3/4 xl:tw-w-[60%] md:tw-w-[87%] tw-w-[90%] md:tw-leading-[140%] tw-leading-[120%]">
                            Discover the vibrant energy of our team through moments, milestones, and memories that shape our journey.
                        </p>
                    </div>

                    <div className="tw-flex tw-flex-col tw-items-center md:tw-gap-7 tw-gap-3">
                        {groupedCards.map((row, rowIndex) => (
                            <div
                                key={rowIndex}
                                className={`tw-flex tw-gap-4 tw-flex-wrap tw-justify-center ${row.length === 1 ? "tw-w-full lg:tw-w-[400px]" : ""}`}
                            >
                                {row.map((card, idx) => (
                                    <div
                                        key={`${rowIndex}-${idx}`}
                                        ref={(el) => cardsRef.current.push(el)}
                                        className={`tw-text-white md:tw-rounded-[10px] tw-rounded-md tw-text-start tw-font-medium md:tw-p-5 tw-px-3 tw-py-2 tw-font-bricolageGrotesque  tw-text-sm md:tw-text-[26px] md:tw-w-[320px] lg:tw-w-[400px] ${card.color} ${card.rotate}`}
                                    >
                                        {card.text}
                                    </div>
                                ))}
                            </div>
                        ))}
                    </div>
                </div>
            </Container>
        </section>
    );
};

export default HeroSection;
