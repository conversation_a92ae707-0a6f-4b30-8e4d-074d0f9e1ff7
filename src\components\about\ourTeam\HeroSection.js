"use client";

import React, { useLayoutEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import { gsap } from "gsap";
import { Container } from "reactstrap";
import FillButton from "@/components/buttons/FillButton";
import workImg from "../../../../public/aboutPage/ourTeam/workImg.png";
import environment from "../../../../public/aboutPage/ourTeam/environment.png";
import culture from "../../../../public/aboutPage/ourTeam/culture.png";
import Image from "next/image";

const HeroSection = () => {
    const pathname = usePathname();

    // Container ref for GSAP context
    const sectionRef = useRef(null);
    const titleRef = useRef(null);
    const descRef = useRef(null);
    const buttonRef = useRef(null);
    const centerCardRef = useRef(null);
    const leftCardRef = useRef(null);
    const rightCardRef = useRef(null);

    useLayoutEffect(() => {
        // Use gsap.context to safely scope animations
        const ctx = gsap.context(() => {
            const tl = gsap.timeline({ defaults: { ease: "power3.out", duration: 1 } });

            tl.from(titleRef.current, { y: -50, opacity: 0 })
                .from(descRef.current, { y: -30, opacity: 0 }, "-=0.8")
                .from(buttonRef.current, { y: -20, opacity: 0 }, "-=0.6")
                .from(centerCardRef.current, { scale: 0.5, opacity: 0 }, "-=0.3")
                .from([leftCardRef.current, rightCardRef.current], {
                    scale: 0.5,
                    opacity: 0,
                    stagger: 0.2,
                }, "-=0.5");
        }, sectionRef); // scoped to section

        return () => ctx.revert(); // clean up on unmount
    }, [pathname]); // re-run on route change

    return (
        <section ref={sectionRef} className="tw-bg-[#F7F9FB] tw-shadow-home_hero_section lg:tw-pb-[.9375rem] tw-pb-2">
            <div className="tw-bg-teamHeroBgMobile md:tw-bg-teamHeroBg tw-bg-no-repeat tw-bg-cover tw-bg-top lg:tw-rounded-[1.25rem] tw-rounded-[.9375rem] lg:tw-mx-[.9375rem] tw-mx-2">
                <Container>
                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center xl:tw-py-[65px] lg:tw-py-[45px] md:tw-py-[50px] tw-py-[40px] tw-text-center tw-gap-[50px]">
                        <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px]">
                            <h1
                                ref={titleRef}
                                className="tw-mb-0 xl:tw-text-[70px] lg:tw-text-[55px] md:tw-text-[40px] tw-text-[34px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold md:tw-w-[90%]"
                            >
                                Meet the Minds Behind TST Technology
                            </h1>
                            <p
                                ref={descRef}
                                className="tw-mb-0 tw-text-[22px] lg:tw-text-xl md:tw-text-lg tw-text-base tw-text-primary_gray lg:tw-w-[65%] md:tw-w-[80%] tw-w-[94%] md:tw-leading-[140%] tw-leading-[120%]"
                            >
                                Passionate crew of creators, innovators, and problem-solvers working together to build the future.
                            </p>
                        </div>

                        <FillButton
                            ref={buttonRef}
                            title="Explore Our Team"
                            className="tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-5 tw-py-[8px] tw-px-4"
                        />

                        <div className="tw-grid lg:tw-grid-cols-3 tw-grid-cols-2 tw-justify-center tw-items-center lg:tw-gap-0 tw-gap-x-3 xl:tw-mx-[60px] lg:tw-mt-0 md:tw-mt-[20px] tw-mt-[10px]">
                            {/* Left Card */}
                            <div ref={leftCardRef} className="tw-flex tw-h-fit tw-items-center tw-flex-col tw-bg-white/70 md:tw-rounded-[30px] tw-rounded-[12px] tw-border tw-border-[#DFE4E8] md:tw-p-[30px] tw-p-[12px] tw-rotate-[-10deg] tw-order-1">
                                <h3 className="tw-font-bold md:tw-text-xl tw-text-sm md:tw-mb-[20px] tw-mb-[5px]">Work Culture</h3>
                                <p className="md:tw-text-[12px] tw-text-[8px] tw-text-primary_gray md:tw-mb-[20px] tw-mb-[5px]">
                                    We believe in a team-first mindset where openness, trust, & cooperation fuel our day-to-day. Every member supports one another to grow & succeed.                                </p>
                                <div className="tw-relative tw-aspect-square md:tw-w-[108px] md:tw-h-[106px] tw-w-[60px] tw-h-[60px]">
                                    <Image src={workImg} fill alt="workImg" className="tw-object-contain" />
                                </div>
                            </div>

                            {/* Center Card */}
                            <div ref={centerCardRef} className="tw-place-self-center tw-flex tw-items-center tw-flex-col tw-h-fit tw-bg-white/70 md:tw-rounded-[30px] tw-rounded-[4] tw-border tw-border-[#DFE4E8] md:tw-p-[30px] tw-p-[12px] lg:tw-order-2 tw-order-3 tw-relative lg:tw-top-7 md:-tw-top-9 -tw-top-4 tw-z-20 lg:tw-col-span-1 tw-col-span-2 lg:tw-w-auto md:tw-w-[350px] 425:tw-w-[180px] tw-w-[170px]">
                                <h3 className="tw-font-bold md:tw-text-xl tw-text-sm md:tw-mb-[20px] tw-mb-[5px]">Team Environment</h3>
                                <p className="md:tw-text-[12px] tw-text-[8px] tw-text-primary_gray md:tw-mb-[20px] tw-mb-[5px]">
                                    From celebrating wins to tackling problems, we foster a healthy, inclusive space where everyone feels safe, heard, and valued.
                                </p>
                                <div className="tw-relative tw-aspect-square md:tw-w-[108px] md:tw-h-[106px] tw-w-[60px] tw-h-[60px]">
                                    <Image src={environment} fill alt="environment" className="tw-object-contain" />
                                </div>
                            </div>

                            {/* Right Card */}
                            <div ref={rightCardRef} className="tw-flex tw-items-center tw-h-fit tw-flex-col tw-bg-white/70 md:tw-rounded-[30px] tw-rounded-[12px] tw-border tw-border-[#DFE4E8] md:tw-p-[30px] tw-p-[12px] tw-rotate-[10deg] lg:tw-order-3 tw-order-2">
                                <h3 className="tw-font-bold md:tw-text-xl tw-text-sm md:tw-mb-[20px] tw-mb-[5px]">Supportive Culture</h3>
                                <p className="md:tw-text-[12px] tw-text-[8px] tw-text-primary_gray md:tw-mb-[20px] tw-mb-[5px]">
                                    We cultivate a work environment built on trust, safety, cooperation, and accountability - empowering every team member to thrive.
                                </p>
                                <div className="tw-relative tw-aspect-square md:tw-w-[108px] md:tw-h-[106px] tw-w-[60px] tw-h-[60px]">
                                    <Image src={culture} fill alt="culture" className="tw-object-contain" />
                                </div>
                            </div>
                        </div>
                    </div>
                </Container>
            </div>
        </section>
    );
};

export default HeroSection;
