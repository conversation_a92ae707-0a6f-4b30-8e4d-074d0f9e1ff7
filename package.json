{"name": "one-build-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3560 -H 0.0.0.0", "prepare-standalone": "mkdir -p .next/standalone/.next && cp -r .next/static .next/standalone/.next/static && cp -r public .next/standalone/public", "build:standalone": "next build && npm run prepare-standalone", "build": "next build", "start:standalone": "concurrently -k \"cd .next/standalone && node server.js\"", "start": "next start -p 3560 -H 0.0.0.0", "lint": "next lint"}, "dependencies": {"axios": "^1.9.0", "bootstrap": "^5.3.6", "concurrently": "^9.1.2", "gsap": "^3.13.0", "lenis": "^1.3.3", "next": "15.3.2", "next-pwa": "^5.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-slick": "^0.30.3", "reactstrap": "^9.2.3", "slick-carousel": "^1.8.1", "split-type": "^0.3.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}}