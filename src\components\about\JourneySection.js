"use client";

import React, { useEffect, useRef, useState } from "react";
import { Col, Container, Row } from "reactstrap";
import FillButton from "../buttons/FillButton";

const timelineData = [
  {
    date: "1 July 2021",
    content:
      "TST Technology was founded in college by three passionate minds <PERSON><PERSON><PERSON> (Tech Lead), <PERSON><PERSON> (Project & Operations), and <PERSON><PERSON> (Business Development). On 1st July 2021 at 6:30 PM, our first website went live. Since then, we've been empowering startups with services like UI/UX Design, Web & Mobile App Development, SaaS Product Development, and IT Consulting.",
  },
  {
    date: "15 July 2021",
    content:
      "Before having an office, TST Technology found its first home—a shared apartment in Ahmedabad that doubled as our workspace. It became the heart of late-night coding, long meetings, and big dreams. From that very space, we landed our first international client and began our global journey.",
  },
  {
    date: "1 July 2022",
    content:
      "TST Technology was founded in college by three passionate minds <PERSON><PERSON><PERSON> (Tech Lead), <PERSON><PERSON> (Project & Operations), and <PERSON><PERSON> (Business Development). On 1st July 2021 at 6:30 PM, our first website went live. Since then, we've been empowering startups with services like UI/UX Design, Web & Mobile App Development, SaaS Product Development, and IT Consulting.",
  },
];

export default function JourneySection() {
  const [lineTop, setLineTop] = useState(0);
  const firstItemRef = useRef(null);

  useEffect(() => {
    if (firstItemRef.current) {
      const height = firstItemRef.current.offsetHeight;
      console.log(height);

      setLineTop(height / 2);
    }
  }, []);

  return (
    <Container className="tw-flex tw-flex-col tw-items-center tw-gap-[40px] lg:tw-gap-[70px]">
      {/* Title */}
      <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
        <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
          Our Journey
          <span className="tw-text-primary_green"> So Far</span>
        </div>
        <div className="lg:tw-text-[14px] tw-text-[12px] md:text-[14px] tw-text-primary_gray tw-font-inter tw-w-[80%] md:tw-w-full tw-leading-[140%]">
          From a humble start to a trusted partner in digital transformation.
        </div>
      </div>

      {/* Timelines */}
      <div className="tw-relative">
        {/* Vertical Line */}
        <div
          style={{ top: `${lineTop}px` }}
          className="tw-absolute tw-left-1/2 tw-bottom-0 tw-w-[2px] tw-bg-primary_black -tw-translate-x-1/2"
        />

        {/* Bottom gradient */}
        <div className="tw-z-20 tw-absolute tw-bottom-0 tw-h-[35%] lg:tw-h-[40%] tw-bg-lightWhite-gradient tw-w-full" />

        <div className="tw-flex tw-flex-col tw-gap-[89px] tw-pb-[78px]">
          {timelineData.map((item, index) => {
            const isEven = index % 2 == 0;

            const leftSideClasses =
              "tw-pr-[16px] md:tw-pr-[40px] lg:tw-pr-[70px] xl:tw-pr-[100px] tw-text-right";
            const rightSideClasses =
              "tw-pl-[16px] md:tw-pl-[40px] lg:tw-pl-[70px] xl:tw-pl-[100px] tw-text-left";

            return (
              <div key={index} className="tw-relative">
                <Row className="align-items-center g-0">
                  <Col
                    xs="6"
                    className={`${isEven ? "tw-order-1" : "tw-order-2"}`}
                  >
                    <h3
                      className={`${isEven ? leftSideClasses : rightSideClasses
                        } tw-text-primary_green tw-font-bold tw-text-[24px] md:text-[30px] lg:tw-text-[36px] tw-leading-[1.2] tw-font-bricolageGrotesque tw-mb-0`}
                    >
                      {item.date}
                    </h3>
                  </Col>

                  <Col
                    xs="6"
                    className={` ${isEven ? "tw-order-2" : "tw-order-1"}`}
                  >
                    <p
                      ref={index === 0 ? firstItemRef : null}
                      className={`${isEven ? rightSideClasses : leftSideClasses
                        } tw-font-inter tw-font-normal tw-text-[14px]  md:tw-text-[16px] xl:tw-text-[18px] tw-leading-[1.2] tw-mb-0`}
                    >
                      {item.content}
                    </p>
                  </Col>
                </Row>

                {/* Timeline Point */}
                <div className="tw-bg-white tw-p-2 tw-absolute tw-left-1/2 tw-top-1/2 -tw-translate-x-1/2 -tw-translate-y-1/2">
                  <div className="tw-aspect-square tw-h-2 lg:tw-h-2.5 tw-bg-primary_black tw-rounded-full tw-z-10" />
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <FillButton
        title={"Explore Our Story"}
        className={
          "tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-5 tw-py-[8px] tw-px-4"
        }
        isLink
        href="/journey"
      />
    </Container>
  );
}
