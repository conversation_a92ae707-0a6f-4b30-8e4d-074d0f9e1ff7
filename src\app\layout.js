import { Inter, Bricolage_Grotesque } from "next/font/google";
import "./globals.css";
import "bootstrap/dist/css/bootstrap.min.css";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import HeaderSection from "../components/layout/HeaderSection";
import { FooterSection } from "@/components/layout/FooterSection";
import SmoothScroll from "@/common/SmoothScroll";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"], // You can set this to any custom variable name, e.g., --font-poppins
  display: "swap",
});

const bricolageGrotesque = Bricolage_Grotesque({
  variable: "--font-bricolage-grotesque",
  subsets: ["latin"],
  weight: ["200", "300", "400", "500", "600", "700", "800"], // You can set this to any custom variable name, e.g., --font-poppins
  display: "swap",
});

export const metadata = {
  title: "OneBuild Website",
  description: "OneBuild Website",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <link rel="manifest" href="/site.webmanifest" />
      <link rel="apple-touch-icon" sizes="76x76" href="/512.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/512.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/512.png" />
      <link rel="manifest" href="/site.webmanifest" />
      <link rel="mask-icon" href="/512.png" color="#35B729" />
      <link rel="icon" type="image/png" sizes="16x16" href="/512.png" />
      <link rel="icon" href="/512.png" />
      <link rel="apple-touch-icon" href="/512.png" />
      <link rel="preload" as="image" href="/home/<USER>" />

      <body
        cz-shortcut-listen="true"
        className={`${inter.variable} ${bricolageGrotesque.variable} antialiased`}
      >
        <SmoothScroll>
          <HeaderSection />
          <main className="xl:tw-mt-4 lg:tw-mt-[102px] tw-mt-[94px]">
            {children}
          </main>
          <FooterSection />
        </SmoothScroll>
      </body>
    </html>
  );
}
