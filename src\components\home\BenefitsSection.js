"use client";
import React from "react";
import { Container, Row, Col } from "reactstrap";
import ClientSlider from "@/common/Slider/ClientSlider";
import FillButton from "../buttons/FillButton";

import clientImg2 from "/public/client/c2.png";
import clientImg1 from "/public/client/c1.png";
import clientImg3 from "/public/client/c3.png";

export const BenefitsSection = ({ className, data }) => {
  const listData = [
    "Diverse Industry Experience",
    "Standard Project Management",
    "AI-Driven Solutions",
    "Creative and Innovative Approach",
    "Continuous Upskilling",
    "Cutting-Edge R&D",
  ];

  const clients = [
    {
      name: "<PERSON><PERSON><PERSON>",
      title: "Founder and CEO of CAARD",
      image: clientImg2,
      url: "https://cdn.tsttechnology.in/Dhairya_Shah_TST_Testomonial_dfaeb720cc.mp4",
    },
    {
      name: "<PERSON>",
      title: "Co-founder of CAARD",
      image: clientImg3,
      url: "https://cdn.tsttechnology.in/Whats_App_Video_2025_02_25_at_11_22_58_464437f7e6.mp4",
    },
    {
      name: "Hridesh Kapur",
      title: "Director of engineering at Stockpe",
      image: clientImg1,
      url: "https://cdn.tsttechnology.in/TST_Testimonial_c2c47b99c0.mp4",
    },
  ];

  return (
    <Container
      tag="section"
      className={`tw-bg-primary_color tw-w-full tw-overflow-hidden tw-px-20  tw-max-md:tw-max-w-full tw-max-md:tw-px-5 ${className}`}
    >
      <Row className="gy-5">
        <Col lg={6} xl={7}>
          <div className="tw-flex tw-w-full tw-h-full tw-flex-col tw-self-stretch tw-items-stretch tw-justify-center tw-my-auto tw-max-md:tw-max-w-full tw-max-md:tw-mt-10">
            <div className="tw-flextw-flex-col lg:tw-text-start tw-text-center">
              <h2 className="tw-text-secondary_color md:tw-text-5xl tw-text-[30px] tw-font-bold tw-font-bricolageGrotesque">
                {data?.greenTitle}
              </h2>
              <h3 className="tw-text-[#E8E8E8] md:tw-text-4xl tw-text-[26px] tw-font-bold ">
                {data?.blackTitle}
              </h3>
              <p className="tw-text-primary_gray tw-text-sm tw-mt-3.5 xl:tw-w-[80%] lg:tw-w-full tw-w-[90%] lg:tw-mx-0 tw-mx-auto">
                At TST Technology, we blend deep industry knowledge with
                innovative tech solutions to deliver real business value. Our
                foundation is built on strategic project management, AI-driven
                development, and continuous upskilling.
                <br />
                <br />
                We design scalable, secure, and future-ready solutions tailored
                to your goals. From creative problem-solving to cutting-edge
                R&D, we help turn your ideas into powerful digital products.
              </p>
            </div>
            <div className="lg:tw-hidden tw-my-5">
              <ClientSlider bgColorClassName="tw-bg-primary_color" data={clients} />
            </div>
            <div className="tw-grid tw-grid-flow-row tw-grid-cols-12 xl:tw-mt-10 tw-mt-3 xl:tw-gap-6 tw-gap-3">
              {listData?.map((i, index) => {
                return (
                  <div
                    className="group tw-relative md:tw-col-span-6 tw-col-span-12"
                    key={index}
                  >
                    <div className="tw-text-center xl:tw-text-base tw-text-sm tw-font-medium tw-text-[#A4A4A4] hover:tw-text-[#E8E8E8] tw-relative tw-z-10 tw-self-stretch tw-flex-1 tw-shrink tw-basis-auto tw-bg-[#1b1b1b] tw-shadow-[1px_2px_10px_rgba(0,0,0,0.07)] tw-grow tw-px-5 tw-py-[18px] tw-rounded-[10px] custom-border-gradient tw-border tw-border-[#FFFFFF1F]">
                      {i}
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="tw-flex  tw-justify-center tw-mt-10">
              <FillButton
                title={"Transform Your Idea with Us"}
                className={
                  "tw-w-fit tw-rounded-[12px]  tw-p-2.5 tw-bg-primary_bg"
                }
              />
            </div>
          </div>
        </Col>
        <Col lg={6} xl={5} className="lg:tw-block tw-hidden">
          <ClientSlider data={clients} bgColorClassName="tw-bg-primary_color" />
        </Col>
      </Row>
    </Container>
  );
};
