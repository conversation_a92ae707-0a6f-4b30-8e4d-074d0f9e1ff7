import { HappeningCard } from "@/components/card/HappeningCard";
import React from "react";
import { Col, Container, Row } from "reactstrap";

import img1 from "/public/aboutPage/Journey/happening/img1.png";
import img2 from "/public/aboutPage/Journey/happening/img2.png";
import img3 from "/public/aboutPage/Journey/happening/img3.png";
import img4 from "/public/aboutPage/Journey/happening/img4.png";
import img5 from "/public/aboutPage/Journey/happening/img5.png";
import img6 from "/public/aboutPage/Journey/happening/img6.png";
import img7 from "/public/aboutPage/Journey/happening/img7.png";
import img8 from "/public/aboutPage/Journey/happening/img8.png";
import img9 from "/public/aboutPage/Journey/happening/img9.png";
import img10 from "/public/aboutPage/Journey/happening/img10.png";
import img11 from "/public/aboutPage/Journey/happening/img11.png";
import img12 from "/public/aboutPage/Journey/happening/img12.png";
import img13 from "/public/aboutPage/Journey/happening/img13.png";
import img14 from "/public/aboutPage/Journey/happening/img14.png";
import img15 from "/public/aboutPage/Journey/happening/img15.png";
import img16 from "/public/aboutPage/Journey/happening/img16.png";
import img17 from "/public/aboutPage/Journey/happening/img17.png";
import img18 from "/public/aboutPage/Journey/happening/img18.png";
import img19 from "/public/aboutPage/Journey/happening/img19.png";
import img20 from "/public/aboutPage/Journey/happening/img20.png";

export const HappeningSection = ({ className }) => {
  const happenings = [
    {
      image: img1,
      tags: ["networking"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img2,
      tags: ["podcasts"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img3,
      tags: ["networking", "events"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img4,
      tags: ["networking", "events"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img5,
      tags: ["nirma", "talks"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img6,
      tags: ["newoffice", "suratoffice"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img7,
      tags: ["networking", "events"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img8,
      tags: ["networking"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img9,
      tags: ["networking"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img10,
      tags: ["sessions"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img11,
      tags: ["awards"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img12,
      tags: ["networking"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img13,
      tags: ["events", "esesions"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img14,
      tags: ["events"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img15,
      tags: ["events"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img16,
      tags: ["events"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img17,
      tags: ["esessions", "events"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img18,
      tags: ["meetings", "networking"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img19,
      tags: ["meetings", "networking"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
    {
      image: img20,
      tags: ["podcasts", "session"],
      title: "Daxesh Italiya meets MBA Chaiwala, Prafull Billore, in Gurgaon",
    },
  ];

  return (
    <section className={`${className}`}>
      <Container>
        <Row className="g-3 g-xl-4">
          {happenings.map((resource, index) => (
            <Col key={index} xs="12" sm="6" lg="4">
              <HappeningCard
                image={resource.image}
                tags={resource.tags}
                title={resource.title}
              />
            </Col>
          ))}
        </Row>
      </Container>
    </section>
  );
};
