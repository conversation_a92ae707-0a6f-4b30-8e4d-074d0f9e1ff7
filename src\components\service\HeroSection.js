
import React from 'react';
import { Container } from 'reactstrap';
import ProjectClientSlider from '@/common/Slider/ProjectClientSlider';
import StateCard2 from '../card/StateCard2';
import FillButton from '../buttons/FillButton';

const HeroSection = ({ title, subTitle, btnText = "", states, titleClassName = "", className = "" }) => {

    return (
        <section className={`${className} tw-bg-no-repeat tw-bg-cover tw-bg-top tw-shadow-home_hero_section lg:tw-pb-[.9375rem] tw-pb-2`}>
            {/* tw-bg-service-bg-img  tw-bg-no-repeat tw-bg-cover tw-bg-top  */}
            <div className='  tw-mx-2 '>
                <Container>
                    <div className='tw-flex tw-flex-col tw-items-center tw-justify-center xl:tw-py-[65px] lg:tw-py-[45px] md:tw-py-[50px] tw-py-[40px] tw-text-center lg:tw-gap-[60px] tw-gap-[50px]'>
                        <div className='tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px]'>
                            <h1 className={`${titleClassName} tw-bg-[linear-gradient(90.84deg,#FFFFFF_23.48%,#E2DDED_34.83%,#977FCB_76.78%)] tw-text-transparent tw-bg-clip-text tw-mb-0 xl:tw-text-[70px] lg:tw-text-[55px] md:tw-text-[40px] tw-text-[34px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold md:tw-w-[90%]`}>{title}</h1>
                            <p className='tw-mb-0 tw-text-[22px] lg:tw-text-xl md:tw-text-lg tw-text-base tw-text-primary_gray lg:tw-w-[65%] md:tw-w-[80%] tw-w-[94%] md:tw-leading-[140%] tw-leading-[120%]'>{subTitle}</p>
                        </div>
                        <div className="tw-p-2.5 tw-border tw-border-white/30 tw-rounded-[1.25rem]" >
                            <FillButton title={btnText} className={'tw-bg-white !tw-text-[#433878] tw-rounded-[12px] xl:tw-py-2.5 xl:tw-px-5 tw-py-2 tw-px-7 lg:!tw-px-10 tw-border-none'} />
                        </div>
                        {/* <FillButton title={btnText} className={'tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-5 tw-py-[8px] tw-px-4'} /> */}
                        {states?.length > 0 &&
                            <div className='tw-flex md:tw-justify-center tw-justify-evenly tw-items-center lg:tw-gap-x-[50px] md:tw-gap-x-[20px] tw-gap-x-[10px] '>
                                {states.map((stat, index) => (
                                    <StateCard2 key={index} data={stat} />
                                ))}
                            </div>}
                    </div>
                </Container>
                <div className='tw-w-full -tw-bottom-0 tw-z-10 tw-h-20 tw-bg-[linear-gradient(180deg,_rgba(18,18,18,0)_3.8%,#121212_100%)]'>
                    <ProjectClientSlider />
                </div>

            </div>
        </section>
    );
};

export default HeroSection;

; 
