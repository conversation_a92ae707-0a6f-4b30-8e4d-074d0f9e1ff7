import { Container } from "reactstrap";
import memberOneImg from "../../../public/aboutPage/memberOne.png";
import memberTwoImg from "../../../public/aboutPage/memberTwo.png";
import { LinkedinIcon, TwitterIcon } from "@/utils/icons";
import Image from "next/image";
import Link from "next/link";

const MeetOurTeam = ({
    className = "",
    title = "",
    description = ""
}) => {
    const memberData = [
        {
            name: "<PERSON>",
            description: "Our CFO brings strategic vision and financial expertise",
            role: "Chief Financial Officer",
            image: memberOneImg,
            socialHandles: [
                {
                    platform: "twitter",
                    link: "/"
                },
                {
                    platform: "linkedin",
                    link: "/about"
                },
            ]
        },
        {
            name: "<PERSON>",
            description: "Our CEO leads with vision and dedication, driving innovation.",
            role: "Chief Executive Officer",
            image: memberTwoImg,
            socialHandles: [
                {
                    platform: "twitter",
                    link: "/"
                },
                {
                    platform: "linkedin",
                    link: "/about"
                },
            ]
        },
        {
            name: "<PERSON>",
            description: "Our CEO leads with vision and dedication, driving innovation.",
            role: "Chief Executive Officer",
            image: memberTwoImg,
            socialHandles: [
                {
                    platform: "twitter",
                    link: "/"
                },
                {
                    platform: "linkedin",
                    link: "/about"
                },
            ]
        },
        {
            name: "Christina osetti",
            description: "Our CFO brings strategic vision and financial expertise",
            role: "Chief Financial Officer",
            image: memberOneImg,
            socialHandles: [
                {
                    platform: "twitter",
                    link: "/"
                },
                {
                    platform: "linkedin",
                    link: "/about"
                },
            ]
        },

    ]

    const socialHandlesIcon = {
        twitter: <TwitterIcon fill="#fff" />,
        linkedin: <div className="tw-border-1.5 tw-rounded-full tw-border-white"><LinkedinIcon fill="#fff" /></div>
    }
    return (
        <section className={`${className} tw-bg-primary_color`}>
            <Container>
                <div className="tw-w-full">
                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center  tw-gap-4 ">
                        <h1 className="tw-mb-0 xl:tw-text-[2.75rem] lg:tw-text-[40px] md:tw-text-[35px] tw-text-[30px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold md:tw-w-full tw-text-white">
                            {title}
                        </h1>
                        <p className={`tw-mb-0 md:tw-text-lg tw-text-sm tw-text-white/80  md:tw-leading-[140%] tw-leading-[120%] tw-max-w-3xl tw-mx-auto`}>
                            {description}
                        </p>
                    </div>
                    <div className="tw-my-5 tw-grid tw-grid-cols-1 lg:tw-grid-cols-2 tw-gap-10">
                        {memberData.map((member) => (<div key={member.name} className="tw-bg-[#1b1b1b] tw-border tw-border-white/40 tw-rounded-3xl tw-p-5 tw-relative tw-overflow-hidden">
                            <div className="tw-max-w-[18rem]">
                                <div className="tw-bg-[#ffffff14] tw-text-white tw-rounded-full tw-text-xs tw-py-1.5 tw-px-3 tw-inline-block">
                                    {member?.role}
                                </div>
                                <div className="tw-pt-10">
                                    <p className="tw-text-white tw-font-medium tw-text-lg tw-mb-2">
                                        {member?.name}
                                    </p>
                                    <p className="tw-text-white/40 tw-text-base">
                                        {member?.description}
                                    </p>
                                </div>
                                <div className="tw-flex tw-gap-3 tw-items-center">
                                    {member?.socialHandles?.map(ele => <Link href={ele.link} key={ele.link} className="tw-p-4 tw-border tw-rounded-full tw-border-[#FFFFFF14]">
                                        {/* <TwitterIcon fill="#fff" /> */}
                                        {socialHandlesIcon[ele.platform]}
                                    </Link>)}
                                    {/* <div className="tw-p-4 tw-border tw-rounded-full tw-border-[#FFFFFF14]">
                                        <div className="tw-border-1.5 tw-rounded-full tw-border-white"><LinkedinIcon fill="#fff" /></div>
                                    </div> */}
                                </div>
                            </div>
                            <div className="tw-relative -tw-bottom-8 tw-mx-auto  md:tw-absolute tw-right-0 md:-tw-bottom-2 tw-w-[270px] tw-h-[240px] ">
                                <Image src={member?.image} alt="memberOneImg" fill className=" " objectFit="contain" />
                            </div>
                        </div>))}

                    </div>

                </div>
            </Container>
        </section>
    );
}

export default MeetOurTeam;