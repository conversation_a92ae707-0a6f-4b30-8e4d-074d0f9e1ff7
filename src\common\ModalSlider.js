"use client";
import React, { useRef, useState } from "react";
import Slider from "react-slick";
import Image from "next/image";
import { LeftArrowIcon, RightArrowIcon } from "@/utils/icons";

export default function ModalSlider({ isOpenModal, setIsOpenModal }) {
    const sliderRef = useRef(null);
    const [count, setCount] = useState(0);
    const clients = [
        "https://cdn.tsttechnology.in/Whats_App_Image_2025_05_28_at_10_54_53_6adb33ac4e.jpeg",
        "https://cdn.tsttechnology.in/Whats_App_Image_2025_05_28_at_10_54_52_82f256979f.jpeg",
        "https://cdn.tsttechnology.in/Whats_App_Image_2025_05_28_at_10_54_52_82f256979f.jpeg",
        "https://cdn.tsttechnology.in/Whats_App_Image_2025_05_28_at_10_54_53_1_3ab5f448eb.jpeg",
    ];
    const settings = {
        dots: false,
        pauseOnHover: true,
        arrows: false,
        infinite: false,
        speed: 500,
        swipeToSlide: true,
        beforeChange: (oldIndex, newIndex) => setCount(newIndex),
    };

    const goToPrev = () => {
        if (count > 0) {
            sliderRef.current?.slickPrev();
        }
    };

    const goToNext = () => {
        if (count < clients.length - 1) {
            sliderRef.current?.slickNext();
        }
    };

    return (
        <>
            {isOpenModal && (
                <div className="tw-fixed tw-inset-0 tw-z-50 tw-bg-black/80 tw-flex tw-justify-center tw-items-center tw-w-full ">
                    {/* Close Icon */}
                    <button
                        onClick={() => setIsOpenModal(false)}
                        className="tw-absolute tw-top-4 tw-right-4 tw-text-white tw-text-6xl tw-font-light"
                    >
                        &times;
                    </button>

                    {/* Slick Slider */}
                    <div className="tw-relative tw-w-full tw-max-w-[90vw] tw-max-h-[90vh] tw-overflow-hidden">
                        <Slider {...settings} ref={sliderRef}>
                            {clients.map((src, idx) => (
                                <div key={idx} className="!tw-flex !tw-justify-center">
                                    <Image
                                        src={src}
                                        alt={`Slide ${idx}`}
                                        width={400}
                                        height={400}
                                        className="tw-object-contain tw-max-h-[90vh] tw-max-w-[90vw]"
                                    />
                                </div>
                            ))}
                        </Slider>
                    </div>

                    {/* Left Arrow */}
                    <button
                        className={`tw-absolute tw-left-5 tw-top-1/2 tw-z-50 tw-rounded-full tw-p-[10px] ${count === 0 ? "tw-bg-[#e0fbd7]" : "tw-bg-primary_green"
                            }`}
                        onClick={goToPrev}
                        disabled={count === 0}
                        aria-label="Previous"

                    >
                        <LeftArrowIcon
                            className={`${count === 0 ? "tw-fill-primary_green" : "tw-fill-white"
                                }`}
                        />
                    </button>

                    {/* Right Arrow */}
                    <button
                        className={`tw-absolute tw-right-5 tw-top-1/2 tw-z-50 tw-rounded-full tw-p-[10px] ${count === clients.length - 1
                            ? "tw-bg-[#e0fbd7]"
                            : "tw-bg-primary_green"
                            }`}
                        onClick={goToNext}
                        disabled={count === clients.length - 1}
                        aria-label="Next"
                    >
                        <RightArrowIcon
                            className={`tw-w-6 tw-h-6 ${count === clients.length - 1
                                ? "tw-fill-primary_green"
                                : "tw-fill-white"
                                }`}
                        />
                    </button>
                </div>
            )}
        </>
    );
}
