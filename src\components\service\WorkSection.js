"use client"
import React from "react";
import { Col, Container, Row } from "reactstrap";
import { WorkCard } from "../card/WorkCard";
import projectImg from "../../../public/workicons/productLogo.png";
import { RightArrowIcon } from "@/utils/icons";
import Slider from "react-slick";
import TextButton from "../buttons/TextButton";

export const WorkSection = ({ className }) => {

  const projects = [{
    title: "Fond",
    image: projectImg,
  }, {
    title: "Fond",
    image: projectImg,
  }, {
    title: "Fond",
    image: projectImg,
  }, {
    title: "Fond",
    image: projectImg,
  }, {
    title: "Fond",
    image: projectImg,
  }, {
    title: "Fond",
    image: projectImg,
  }, {
    title: "Fond",
    image: projectImg,
  }];

  const settings = {
    dots: false,
    arrows: false,
    infinite: false,
    speed: 500,
    slidesToShow: 3.9, // Default for >2560px
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2000,
    speed: 1000,
    swipeToSlide: true,
    responsive: [
      {
        breakpoint: 2560, // ≤2560px
        settings: {
          slidesToShow: 2.7,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 1920, // ≤1920px
        settings: {
          slidesToShow: 2.3,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 1440, // ≤1440px
        settings: {
          slidesToShow: 2.2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 1280, // ≤1280px
        settings: {
          slidesToShow: 1.9,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 1024, // ≤1024px
        settings: {
          slidesToShow: 1.5,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 768, // ≤768px
        settings: {
          slidesToShow: 1.15,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 480, // ≤480px
        settings: {
          slidesToShow: 1.05,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 380, // ≤480px
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };


  return (
    <Container fluid tag='section' className={`tw-bg-[#F7F9FB] tw-flex tw-flex-col tw-gap-[40px] lg:tw-gap-[50px] ${className} `}>
      <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
        <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
          <span>Our</span>
          <span className="tw-text-primary_green"> Work</span>
        </div>
        <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-leading-[120%]">
          Explore how we have helped businesses like yours grow through smart,
          scalable technology.
        </div>
      </div>


      <Slider {...settings}>
        {projects.map((projects, idx) => (
          <div key={idx} className="lg:tw-p-3 tw-p-2">
            <WorkCard title={projects.title} image={projects.image} />
          </div>
        ))}
      </Slider>
      <TextButton blackTitle='Explore' greenTitle='More Work' className='lg:tw-text-[20px]' />


    </Container>
  );
};
