
import React from 'react';
import { Container } from 'reactstrap';
import FillButton from '../buttons/FillButton';
import ProjectClientSlider from '@/common/Slider/ProjectClientSlider';
import ReadOnlyRating from '@/common/ReadOnlyRating';

const HeroSection = () => {
    return (
        <section>
            <div className="tw-bg-aboutHeroBg tw-bg-no-repeat tw-bg-cover lg:tw-pb-[.9375rem] tw-pb-2">
                {/*tw-bg-service-bg-img  tw-bg-no-repeat tw-bg-cover tw-bg-top*/}
                <div className=' lg:tw-rounded-[1.25rem] tw-rounded-[.9375rem] lg:tw-mx-[.9375rem] tw-mx-2 '>
                    <Container>
                        <div className='tw-flex tw-flex-col tw-items-center tw-justify-center xl:tw-py-[120px] lg:tw-py-[100px] md:tw-py-[130px] tw-py-[60px] tw-text-center lg:tw-gap-[60px] tw-gap-[50px]'>
                            <div className='tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px]'>
                                {/* <div className='tw-rounded-full tw-bg-white/50 tw-py-[12px] tw-px-[15px] tw-flex tw-items-center tw-justify-center tw-gap-2'>
                                <ReadOnlyRating rating={3.5} />
                                <p className='md:tw-text-sm tw-text-[12px] tw-mb-0 tw-mt-0.5'>Rated 5/5 from over 700 views</p>
                            </div> */}
                                <h1 className='tw-text-white  tw-mb-0 xl:tw-text-[70px] lg:tw-text-[55px] md:tw-text-[40px] tw-text-[34px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold md:tw-w-[100%]'>We didn&apos;t start <span className='tw-bg-[linear-gradient(92.09deg,#E5DFF2_46%,#BCB3DD_55.48%,#7E71BD_69.67%)] tw-bg-clip-text tw-text-transparent'>OneBuild</span> because the world needed another tech agency.</h1>
                                <p className='tw-mb-0 tw-text-[22px] lg:tw-text-xl md:tw-text-lg tw-text-base tw-text-[#D2D2D2] lg:tw-w-[60%] md:tw-w-3/4 tw-w-[90%] md:tw-leading-[140%] tw-leading-[120%]'>We craft impactful digital experiences through design, strategy, and technology helping businesses grow with confidence.</p>
                            </div>
                            <div className="tw-p-2.5 tw-border tw-border-white/30 tw-rounded-[1.25rem]" >
                                <FillButton title={`Learn About Our Culture & Value`} className={'tw-bg-white !tw-text-[#433878] tw-rounded-[12px] xl:tw-py-2.5 xl:tw-px-5 tw-py-2 tw-px-7 lg:!tw-px-10 tw-border-none'} />
                            </div>
                        </div>
                    </Container>
                    <ProjectClientSlider />
                </div>
            </div>
        </section>
    );
};

export default HeroSection;


