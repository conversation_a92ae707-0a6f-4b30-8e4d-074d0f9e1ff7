import React from "react";
import cooIcon from "/public/aboutPage/leaders/coo.png";
import ceoIcon from "/public/aboutPage/leaders/ceo.png";
import ctoIcon from "/public/aboutPage/leaders/cto.png";
import { Col, Container, Row } from "reactstrap";
import Image from "next/image";
import FillButton from "../buttons/FillButton";
import { OutlineEmailIcon, OutlineLinkedInIcon } from "@/utils/icons";

export const LeaderSection = ({ className }) => {
  const leaders = [
    {
      image: cooIcon,
      name: "<PERSON><PERSON>",
      designation: "COO",
    },
    {
      image: ceoIcon,
      name: "<PERSON><PERSON> Makawa<PERSON>",
      designation: "CEO",
    },
    {
      image: ctoIcon,
      name: "<PERSON><PERSON>h Italiya",
      designation: "CTO",
    },
  ];

  return (
    <section className={`${className}`}>
      <Container className="tw-flex tw-flex-col tw-gap-[40px] lg:tw-gap-[70px]">
        {/* Title */}
        <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
          <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            Meet
            <span className="tw-text-primary_green"> Our Leaders</span>
          </div>
          <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-w-full tw-leading-[140%]">
            Led by vision, driven by collaboration — meet the people shaping
            tomorrow&apos;s digital solutions.
          </div>
        </div>

        {/* Leader grid */}
        <Row className="justify-content-center g-3 g-sm-4 g-lg-5 g-xl-6">
          {leaders.map((value, index) => (
            <Col
              key={index}
              xs="6"
              sm="6"
              md="4"
              lg="4"
              xl="4"
              className="tw-group tw-overflow-hidden"
            >
              <div className="tw-relative tw-bg-leaders-bg-img tw-bg-no-repeat tw-bg-cover tw-flex tw-flex-col tw-items-center tw-p-[20px] sm:tw-p-[40px_25px_30px] md:tw-p-[30px_20px_20px] lg:tw-p-[50px_35px_30px] xl:tw-p-[60px_40px_35px] 2xl:tw-p-[70px_50px_45px] lg:tw-rounded-[30px] tw-rounded-[20px] tw-h-full tw-border tw-border-[#DFE4E8]">
                <div className="tw-relative tw-aspect-[1] tw-w-full tw-rounded-full group-hover:tw-scale-75 tw-transition-transform tw-origin-top tw-duration-700 lg:tw-mb-[30px] tw-mb-5">
                  <Image
                    src={value.image}
                    fill
                    className="tw-object-contain"
                    alt={`${value.designation} image`}
                  />
                </div>
                <div className="group-hover:-tw-translate-y-10 tw-transition-transform tw-origin-top tw-duration-700 tw-flex tw-flex-col tw-items-center tw-gap-2.5 lg:tw-gap-3.5">
                  <h3 className="tw-font-bricolageGrotesque tw-text-center tw-font-medium tw-text-[20px] lg:tw-text-[24px] xl:tw-text-[30px] 2xl:tw-text-[34px] tw-leading-[1.2] tw-text-primary_black tw-mb-0 tw-px-2 md:tw-whitespace-nowrap md:tw-overflow-hidden md:tw-text-ellipsis">
                    {value.name}
                  </h3>
                  <p className="tw-font-inter tw-font-normal tw-text-center tw-text-[14px] sm:tw-text-[16px] lg:tw-text-[18px] xl:tw-text-[20px] tw-leading-[1.2] tw-text-primary_gray tw-mb-0 -tw-tracking-tighter">
                    {value.designation}
                  </p>
                </div>
                {/* Add in hover effect  */}
                <div className="tw-absolute tw-left-1/2 -tw-translate-x-1/2 tw-bottom-[-12px] tw-opacity-0  group-hover:xl:tw-bottom-7 group-hover:tw-bottom-5 group-hover:tw-opacity-100 tw-transition-all tw-duration-700 tw-origin-top tw-flex tw-justify-between tw-items-center tw-gap-[35px] lg:tw-gap-12">
                  <OutlineEmailIcon className="tw-aspect-square tw-w-5  xl:tw-w-[30px] tw-mt-0.5" />
                  <OutlineLinkedInIcon className="tw-aspect-square tw-w-5  xl:tw-w-[30px]" />
                </div>
              </div>
            </Col>
          ))}
        </Row>
        <div className="tw-flex tw-justify-center">
          <FillButton
            title={"Join the Team"}
            className={
              "tw-w-fit tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-10 lg:tw-py-[8px] lg:tw-px-4 tw-px-10 tw-py-[13px]"
            }
          />
        </div>


      </Container>
    </section>
  );
};
