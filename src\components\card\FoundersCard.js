import React from "react";

import { FillLinkedinIcon } from "@/utils/icons";
import Image from "next/image";

export const FoundersCard = ({ founder }) => {
  return (
    <div className="tw-relative tw-grid tw-grid-cols-1 lg:tw-grid-cols-[3fr_7fr] tw-items-start tw-gap-5 md:tw-gap-[40px] xl:tw-gap-[70px] tw-p-[0_20px_20px_20px] md:tw-p-[0_40px_40px_40px]  lg:tw-p-[40px_40px_40px_0] xl:tw-p-[70px_70px_70px_0]">
      <div className="tw-absolute tw-bg-[#F7F9FB] tw-border tw-border-[#DFE4E8] tw-rounded-[20px] md:tw-rounded-[24px] xl:tw-rounded-[30px] tw-h-[calc(100%-175px)] md:tw-h-[calc(100%-198px)] lg:tw-h-full tw-w-full lg:tw-w-[calc(100%-152px)] xl:tw-w-[calc(100%-175px)] tw-mt-[175px] md:tw-mt-[198px] lg:tw-mt-0 lg:tw-ml-[152px] xl:tw-ml-[175px] tw-z-0" />

      <div className="tw-relative tw-aspect-[350/450] tw-w-[270px] md:tw-w-[310px] xl:tw-w-[350px] tw-z-10 tw-m-auto tw-inset-0">
        <Image
          fill
          src={founder.imageUrl}
          alt={founder.name}
          className="tw-object-cover tw-rounded-[20px] md:tw-rounded-[30px]"
        />
      </div>

      <div className=" tw-flex tw-flex-col tw-gap-5 md:tw-gap-10 tw-z-10">
        <div className="tw-flex tw-flex-col tw-gap-[15px]">
          <div className="tw-flex tw-justify-between tw-items-center">
            <span className="tw-bg-[#35B72926] tw-text-[14px] md:tw-text-[18px] tw-font-medium tw-text-primary_green tw-h-full tw-p-[12px] tw-rounded-[10px]">
              {founder.role}
            </span>
            <FillLinkedinIcon className="tw-aspect-square tw-w-8 md:tw-w-10" />
          </div>
          <h2 className="tw-mb-[5px] tw-font-bricolageGrotesque tw-text-[26px] md:tw-text-[32px] xl:tw-text-[36px] tw-text-left tw-font-semibold tw-text-primary_black tw-leading-[1.2]">
            {founder.name}
          </h2>
          <p className="tw-font-inter tw-font-normal tw-text-[16px] md:tw-text-[18px] xl:tw-text-[20px] tw-leading-[1.2] tw-text-primary_gray tw-mb-0">
            {founder.description}
          </p>
        </div>

        <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 xl:tw-grid-cols-3 tw-gap-4">
          {founder.certifications.map((cert, index) => (
            <div
              key={index}
              className="tw-flex tw-items-center tw-gap-2 md:tw-gap-3 lg:tw-gap-[15px] tw-p-[15px] tw-rounded-[10px] tw-shadow-founder_certification_card tw-bg-white"
            >
              <div className="tw-relative tw-aspect-square tw-w-8 md:tw-w-10">
                {cert?.iconPath ? (
                  <Image
                    src={cert.iconPath}
                    alt="icon"
                    fill
                    className="object-contain"
                  />
                ) : (
                  cert.icon
                )}
              </div>
              <span className="tw-font-inter tw-font-normal tw-text-[14px] md:tw-text-[16px] tw-leading-[1.2] tw-text-black">
                {cert.text}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
