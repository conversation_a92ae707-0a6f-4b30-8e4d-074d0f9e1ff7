"use client";
import { BlogCard } from "@/components/card/BlogCard";
import Image from "next/image";
import Slider from "react-slick";

const BlogsSlider = ({ blogs }) => {
  const settings = {
    dots: true,
    arrows: false,
    infinite: false,
    speed: 500,
    slidesToShow: 3, // default for >1024px
    slidesToScroll: 3,
    swipeToSlide: true,
    responsive: [
      {
        breakpoint: 1024, // applies when width <= 1024px
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 590, // applies when width <= 450px
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <>
      <div className="">
        <Slider {...settings}>
          {blogs.map((blog, idx) => (
            <div key={idx} className=" lg:tw-p-3 tw-p-2">
              <BlogCard
                key={idx}
                image={blog.image}
                category={blog.category}
                readTime={blog.readTime}
                date={blog.date}
                title={blog.title}
                author={blog.author}
              />
            </div>
          ))}
        </Slider>
      </div>
    </>
  );
};

export default BlogsSlider;
