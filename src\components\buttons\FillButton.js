"use client";
import Link from "next/link";
import React, { forwardRef } from "react";

const FillButton = forwardRef(({ title, className = "tw-bg-primary_bg", onClick = () => { }, isLink = false, href = '#' }, ref) => {
  return (
    <>
      {isLink ? (
        <Link
          href={href}
          ref={ref}
          className={`tw-font-inter  tw-no-underline ripple-button tw-relative tw-inline-block tw-border-[.0938rem] tw-border-secondary_color tw-text-white  tw-text-nowrap tw-font-medium ${className}`}
        >
          {title}
        </Link>
      ) : (
        <button
          type="button"
          ref={ref}
          onClick={onClick}
          // tw-border-[#FFFFFF40]
          className={`tw-font-inter  ripple-button tw-relative tw-inline-block tw-border-[.0938rem] tw-border-secondary_color tw-text-white  tw-text-nowrap tw-font-medium ${className}`}
        >
          {title}
        </button>
      )}
    </>
  );
});

FillButton.displayName = "FillButton"; // required for forwardRef

export default FillButton;
