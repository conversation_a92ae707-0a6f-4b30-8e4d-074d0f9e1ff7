"use client"
import React, { memo } from 'react';
import { FillStarIcon, HalfFillStarIcon, OutlineStarIcon } from '@/utils/icons';

const ReadOnlyRating = ({ rating ,width='20',height='20'}) => {
  const starCount = 5;
  const fullStars = Math?.floor(rating);
  const hasHalfStar = rating % 1 !== 0;
  return (
    <div className="tw-flex md:tw-gap-x-[10px] tw-gap-x-[5px]">
      {[...Array(starCount)].map((_, idx) =>  hasHalfStar && idx === fullStars ? <HalfFillStarIcon key={idx} className='tw-w-[19px] tw-h-[19px]' /> :  idx < fullStars ? <FillStarIcon key={idx} className="tw-w-[19px] tw-h-[19px]" /> : <OutlineStarIcon key={idx} className='tw-w-[19px] tw-h-[19px]' />)}
    </div>
  );
};

export default memo(ReadOnlyRating);