"use client"
import React, { useRef, useState } from "react";
import { Container, Row, Col } from "reactstrap";
import { LeftArrowIcon, RightArrowIcon } from "@/utils/icons";
import Slider from "react-slick";
import { TeamTestimonialCard } from "@/components/card/TeamTestimonialCard";

export const TeamTestimonial = ({ className }) => {
    const sliderRef = useRef(null);
    const [count, setCount] = useState(0);

    const testimonials = [
        {
            avatar:
                "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/575fa746ac097c0d2f00a113e71baa0e130b0b4b?placeholderIfAbsent=true",
            testimonial:
                "Being at TST Technology has shaped my journey as a Flutter developer. The projects are challenging, and I love the freedom to innovate. Every app we build helps me grow and stay ahead in tech.",
            name: "<PERSON>",
            position: "Chief Executive Officer",
        },
        {
            avatar:
                "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/575fa746ac097c0d2f00a113e71baa0e130b0b4b?placeholderIfAbsent=true",
            testimonial:
                "Being at TST Technology has shaped my journey as a Flutter developer. The projects are challenging, and I love the freedom to innovate. Every app we build helps me grow and stay ahead in tech.",
            name: "Daniel Rosewell",
            position: "Chief Executive Officer",
        },
        {
            avatar:
                "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/575fa746ac097c0d2f00a113e71baa0e130b0b4b?placeholderIfAbsent=true",
            testimonial:
                "Being at TST Technology has shaped my journey as a Flutter developer. The projects are challenging, and I love the freedom to innovate. Every app we build helps me grow and stay ahead in tech.",
            name: "Daniel Rosewell",
            position: "Chief Executive Officer",
        },
        {
            avatar:
                "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/fbf9e4f15f29e1259e49f1cee6a4897835f73208?placeholderIfAbsent=true",
            companyLogo:
                "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/7f82927891ef2b536dbeab73a0ee895e28365eb8?placeholderIfAbsent=true",
            testimonial:
                "Being at TST Technology has shaped my journey as a Flutter developer. The projects are challenging, and I love the freedom to innovate. Every app we build helps me grow and stay ahead in tech.",
            name: "Sophia Mendell",
            position: "Chief Executive Officer",
        },
        {
            avatar:
                "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/81534a220104b5285741b936c3d9b1eeb59a6dfe?placeholderIfAbsent=true",
            testimonial:
                "Being at TST Technology has shaped my journey as a Flutter developer. The projects are challenging, and I love the freedom to innovate. Every app we build helps me grow and stay ahead in tech.",
            name: "Shirley Shetia",
            position: "Chief Executive Officer",
        },
    ];

    const settings = {
        dots: false,
        pauseOnHover: true,
        arrows: false,
        infinite: true,
        autoplay: true,
        autoplaySpeed: 2000,
        speed: 1000,
        slidesToShow: 3,
        slidesToScroll: 1,
        swipeToSlide: true,
        responsive: [
            {
                breakpoint: 1920,
                settings: {
                    slidesToShow: 4,
                    slidesToScroll: 1,
                },
            },
            {
                breakpoint: 1440,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                },
            },
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                },
            },
            {
                breakpoint: 600,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                },
            },
            {
                breakpoint: 480,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                },
            },
        ],
        beforeChange: (oldIndex, newIndex) => setCount(newIndex),
    };

    const goToPrev = () => {
        if (count > 0) {
            sliderRef.current?.slickPrev();
        }
    };

    const goToNext = () => {
        if (count < testimonials.length - 1) {
            sliderRef.current?.slickNext();
        }
    };
    return (
        <section className={`tw-bg-teamBg tw-bg-no-repeat tw-bg-center ${className}`}>
            <Container className="" fluid>
                <h2 className="tw-text-primary_black tw-font-bricolageGrotesque md:tw-text-4xl tw-text-[26px] tw-font-bold tw-leading-none tw-text-center tw-self-center ">
                    <span>
                        What {""}
                    </span>
                    <span className="tw-text-primary_green tw-font-bricolageGrotesque">
                        Our Team Says
                    </span>
                </h2>
                <p className="tw-text-primary_gray tw-text-sm  tw-text-center tw-mx-auto  tw-mt-2.5 xl:tw-w-[55%] lg:tw-w-3/4">
                    Hear directly from our team members about their experience working at TST Technology.
                </p>
                <div className="tw-mt-[57px] tw-max-md:tw-mt-10 leftRightBlur">
                    <Row>
                        <Slider {...settings} ref={sliderRef}>
                            {testimonials.map((testimonial, index) => (
                                <Col md={4} key={index} className="tw-mb-5 tw-px-3">
                                    <TeamTestimonialCard
                                        avatar={testimonial.avatar}
                                        testimonial={testimonial.testimonial}
                                        name={testimonial.name}
                                        position={testimonial.position}
                                    />
                                </Col>
                            ))}
                        </Slider>
                    </Row>
                </div>
                <div className="tw-flex tw-justify-center tw-items-center tw-gap-5 tw-mt-5">
                    <button
                        className={`tw-rounded-full tw-p-[10px] ${count === 0 ? 'tw-bg-[#e0fbd7]' : 'tw-bg-primary_green'}`}
                        onClick={goToPrev}
                        disabled={count === 0}
                        aria-label="Previous"
                    >
                        <LeftArrowIcon className={`${count === 0 ? 'tw-fill-primary_green' : 'tw-fill-white'}`} />
                    </button>
                    <button
                        className={`tw-rounded-full tw-p-[10px] ${count === testimonials.length - 1 ? 'tw-bg-[#e0fbd7]' : 'tw-bg-primary_green'}`}
                        onClick={goToNext}
                        disabled={count === testimonials.length - 1}
                        aria-label='Next'
                    >
                        <RightArrowIcon className={`tw-w-6 tw-h-6  ${count === testimonials.length - 1 ? 'tw-fill-primary_green' : 'tw-fill-white'}`} />
                    </button>
                </div>
            </Container>
        </section>

    );
};
