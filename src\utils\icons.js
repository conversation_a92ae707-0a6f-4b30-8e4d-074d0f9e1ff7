export const MenuIcon = (svgProps) => {
  return (
    <svg
      width={22}
      height={22}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M2.69922 11.0555H18.8992"
        stroke="white"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.69922 16.4539H18.8992"
        stroke="white"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.69922 5.65508H18.8992"
        stroke="white"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const DownArrowIcon = () => {
  return (
    <svg
      width={24}
      height={25}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.0041 16.2437C11.8725 16.2444 11.742 16.2192 11.6202 16.1694C11.4983 16.1197 11.3875 16.0463 11.2941 15.9537L5.29409 9.95365C5.10579 9.76535 5 9.50996 5 9.24365C5 8.97735 5.10579 8.72196 5.29409 8.53365C5.4824 8.34535 5.73779 8.23956 6.00409 8.23956C6.27039 8.23956 6.52579 8.34535 6.71409 8.53365L12.0041 13.8337L17.2941 8.54365C17.4854 8.37983 17.7315 8.29422 17.9831 8.30394C18.2348 8.31366 18.4736 8.418 18.6517 8.59609C18.8298 8.77419 18.9341 9.01293 18.9438 9.2646C18.9535 9.51628 18.8679 9.76235 18.7041 9.95365L12.7041 15.9537C12.5178 16.1384 12.2664 16.2425 12.0041 16.2437Z"
        fill="#838387"
      />
    </svg>
  );
};

export const PlayIcon = ({
  size = 90,
  fill = "white",
  mainFill = "#977fcb"
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 90 90"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <foreignObject x={-5} y={-5} width={100} height={100}>
        <div
          xmlns="http://www.w3.org/1999/xhtml"
          style={{
            backdropFilter: "blur(2.5px)",
            clipPath: "url(#bgblur_0_5095_41310_clip_path)",
            height: "100%",
            width: "100%",
          }}
        />
      </foreignObject>
      <circle
        data-figma-bg-blur-radius={5}
        cx={45}
        cy={45}
        r={45}
        fill={fill}
        fillOpacity="0.2"
      />
      <circle cx={45} cy={45} r={30} fill={mainFill} />
      <path
        d="M51.6635 43.0132L40.6635 36.8072C40.3042 36.6019 39.897 36.4954 39.4833 36.4986C39.0696 36.5018 38.664 36.6145 38.308 36.8252C37.9611 37.0236 37.6729 37.3103 37.4727 37.6561C37.2724 38.0019 37.1673 38.3946 37.168 38.7942V51.2062C37.1673 51.6058 37.2724 51.9985 37.4727 52.3443C37.6729 52.6902 37.9611 52.9768 38.308 53.1752C38.6641 53.3859 39.0696 53.4985 39.4833 53.5017C39.897 53.5048 40.3042 53.3984 40.6635 53.1932L51.6635 46.9872C52.0182 46.7907 52.3139 46.5028 52.5198 46.1534C52.7257 45.8039 52.8343 45.4058 52.8343 45.0002C52.8343 44.5947 52.7257 44.1965 52.5198 43.8471C52.3139 43.4977 52.0182 43.2097 51.6635 43.0132Z"
        fill={"#fff"}
      />
      <defs>
        <clipPath id="bgblur_0_5095_41310_clip_path" transform="translate(5 5)">
          <circle cx={45} cy={45} r={45} />
        </clipPath>
      </defs>
    </svg>
  );
};

export const RightArrowIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path d="M15.9999 11.9998C16.0007 12.1314 15.9755 12.2619 15.9257 12.3837C15.8759 12.5056 15.8026 12.6164 15.7099 12.7098L9.70994 18.7098C9.52164 18.8981 9.26624 19.0039 8.99994 19.0039C8.73364 19.0039 8.47825 18.8981 8.28994 18.7098C8.10164 18.5215 7.99585 18.2661 7.99585 17.9998C7.99585 17.7335 8.10164 17.4781 8.28994 17.2898L13.5899 11.9998L8.29994 6.70981C8.13611 6.51851 8.05051 6.27244 8.06023 6.02076C8.06995 5.76909 8.17428 5.53035 8.35238 5.35225C8.53047 5.17416 8.76921 5.06983 9.02089 5.0601C9.27256 5.05038 9.51864 5.13599 9.70994 5.29981L15.7099 11.2998C15.8947 11.4861 15.9988 11.7375 15.9999 11.9998Z" />
    </svg>
  );
};

export const TopRightArrowIcon = (svgProps) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" {...svgProps} viewBox="0 0 33 34" fill="none">
      <path d="M1.91406 31.5834L31.0807 2.41675M31.0807 2.41675H1.91406M31.0807 2.41675V31.5834" stroke="#35B729" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export const QuotesIcon = (svgProps) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52" {...svgProps}>
      <path
        d="M22.5423 25.9841V40.8561C22.5423 42.61 21.1164 44.0354 19.3526 44.0354H4.48058C3.63764 44.0347 2.8294 43.6996 2.23325 43.1036C1.6371 42.5077 1.30175 41.6995 1.30078 40.8566V22.4756C1.30078 18 3.16498 11.8796 12.0856 8.18188C12.8734 7.87621 13.7503 7.8955 14.524 8.23553C15.2976 8.57555 15.9048 9.20853 16.2123 9.99564C16.5105 10.7614 16.5017 11.6126 16.1879 12.3721C15.874 13.1315 15.2793 13.7406 14.5275 14.0724C8.38838 16.6142 7.67078 20.0836 7.67078 22.4756V22.7944H19.3531C19.7721 22.7939 20.1871 22.8761 20.5743 23.0362C20.9615 23.1963 21.3133 23.4313 21.6096 23.7276C21.9059 24.0238 22.1408 24.3757 22.301 24.7629C22.4611 25.1501 22.5427 25.5651 22.5423 25.9841ZM50.7008 25.9841V40.8561C50.7 41.6991 50.3647 42.5074 49.7685 43.1034C49.1723 43.6995 48.364 44.0347 47.521 44.0354H32.649C30.8851 44.0354 29.4593 42.6106 29.4593 40.8561V22.4756C29.4593 18 31.3334 11.8796 40.2545 8.18188C41.8894 7.54384 43.7437 8.36128 44.3812 9.99564C44.999 11.5905 44.2512 13.3751 42.6964 14.0724C36.5568 16.6142 35.8392 20.0836 35.8392 22.4756V22.7944H47.5215C49.276 22.7944 50.7008 24.2197 50.7008 25.9841Z"
        fill="url(#paint0_linear_6192_7689)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_6192_7689"
          x1="26.001"
          y1="7.96484"
          x2="26.001"
          y2="44.0354"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#35B729" />
          <stop offset="1" stopColor="#35B729" stopOpacity="0.1" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const ListIcons = (svgProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 9 11"
      {...svgProps}
      fill="none"
    >
      <path d="M1.48185e-07 0.5L9 5.5L0 10.5L1.48185e-07 0.5Z" />
    </svg>
  );
};
export const LeftArrowIcon = (svgProps) => {
  return (
    <svg
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path d="M8.00006 11.9998C7.9993 12.1314 8.02452 12.2619 8.07429 12.3837C8.12405 12.5056 8.19738 12.6164 8.29006 12.7098L14.2901 18.7098C14.4784 18.8981 14.7338 19.0039 15.0001 19.0039C15.2664 19.0039 15.5218 18.8981 15.7101 18.7098C15.8984 18.5215 16.0042 18.2661 16.0042 17.9998C16.0042 17.7335 15.8984 17.4781 15.7101 17.2898L10.4101 11.9998L15.7001 6.70981C15.8639 6.51851 15.9495 6.27244 15.9398 6.02076C15.93 5.76909 15.8257 5.53035 15.6476 5.35225C15.4695 5.17416 15.2308 5.06983 14.9791 5.0601C14.7274 5.05038 14.4814 5.13599 14.2901 5.29981L8.29006 11.2998C8.10531 11.4861 8.00116 11.7375 8.00006 11.9998Z" />
    </svg>
  );
};

export const I1 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M42.7488 16.1039L24.0042 8.17955C23.8599 8.11779 23.7046 8.08594 23.5477 8.08594C23.3908 8.08594 23.2355 8.11779 23.0912 8.17955L4.25393 16.0754C4.04189 16.1689 3.86151 16.3218 3.73463 16.5157C3.60775 16.7097 3.53982 16.9362 3.53907 17.1679C3.53831 17.3997 3.60477 17.6266 3.73038 17.8214C3.85599 18.0161 4.03538 18.1702 4.2468 18.2651C5.3602 18.7423 11.8773 21.4762 12.7917 21.8742L23.0912 26.218C23.3848 26.3351 23.7125 26.3326 24.0042 26.2109C27.3137 24.8143 35.8544 21.2037 39.0327 19.8557C39.0255 20.7387 39.0384 28.1838 39.0327 29.2636C37.6161 29.621 37.0405 32.0361 37.8344 33.3435L36.7859 38.051C36.7526 38.1923 36.7642 38.3404 36.8192 38.4746C36.8742 38.6089 36.9698 38.7227 37.0926 38.8C37.2145 38.8798 37.3581 38.9198 37.5037 38.9147C37.6493 38.9096 37.7897 38.8596 37.9057 38.7714C38.4128 38.3969 39.023 38.1877 39.6532 38.1723C40.3238 38.1624 40.9787 38.3756 41.5148 38.7786C41.6355 38.8638 41.7794 38.9099 41.9271 38.9106C42.0748 38.9113 42.2191 38.8665 42.3405 38.7825C42.462 38.6984 42.5546 38.579 42.606 38.4406C42.6573 38.3021 42.6648 38.1511 42.6275 38.0082L41.2937 33.3221C41.9799 32.1452 41.6068 30.0318 40.4592 29.4419C40.4656 28.3528 40.4549 20.1139 40.4592 19.2565L42.7488 18.2865C42.9594 18.1923 43.1384 18.0391 43.2639 17.8455C43.3894 17.6518 43.4562 17.426 43.4562 17.1952C43.4562 16.9644 43.3894 16.7386 43.2639 16.5449C43.1384 16.3513 42.9594 16.1981 42.7488 16.1039Z"
        fill="white"
      />
      <path
        d="M22.5369 27.5309C21.6781 27.1686 11.8964 23.0345 11.3672 22.8162V29.7634C11.3721 30.1364 11.4674 30.5026 11.6451 30.8306C11.8227 31.1586 12.0774 31.4386 12.3872 31.6464C15.523 33.7782 19.2031 34.9687 22.9934 35.0772C27.1467 35.1597 31.2259 33.97 34.6838 31.6678C35.0013 31.459 35.2629 31.1756 35.4456 30.8424C35.6284 30.5092 35.7268 30.1363 35.7323 29.7563V22.8091C35.2373 23.0238 25.4021 27.1757 24.5626 27.5309C23.9138 27.7982 23.1857 27.7982 22.5369 27.5309Z"
        fill="white"
      />
    </svg>
  );
};
export const I2 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_5095_47342)">
        <path
          d="M21.6487 3.77045V4.76385C23.0271 4.6705 23.7651 4.78231 23.8417 4.79479C24.0602 4.83046 24.2666 4.91885 24.4432 5.0523C24.6199 5.18575 24.7613 5.36024 24.8552 5.56066L26.7923 9.68912C27.0344 9.74492 27.2565 9.86612 27.4345 10.0395C27.6682 10.2672 27.9479 10.4455 28.2648 10.5228C28.8977 10.6771 29.5255 10.5013 29.9693 10.0697C30.4273 9.62465 31.1259 9.51151 31.6657 9.85281C32.4375 10.3403 32.5203 11.3733 31.915 11.9786C31.0611 12.8329 29.9254 13.3035 28.7172 13.3035C28.6398 13.3034 28.5623 13.3014 28.485 13.2975L31.089 18.848C31.1985 19.0812 31.3029 19.3168 31.402 19.5546C34.3342 18.63 37.6692 19.3167 39.9995 21.6206C40.5206 22.1353 40.5783 22.9761 40.0914 23.5236C39.9637 23.668 39.8066 23.7836 39.6308 23.8626C39.4549 23.9417 39.2642 23.9825 39.0714 23.9822C38.7228 23.9822 38.3738 23.8492 38.108 23.5831C36.5286 22.0041 34.2611 21.5238 32.262 22.1398C33.0202 25.1628 32.9696 28.2827 32.1871 31.2082C32.5577 31.3308 32.9328 31.4393 33.3116 31.5335C33.7539 31.6435 34.2163 31.644 34.6588 31.5349C41.4734 29.8492 46.3263 23.7207 46.3263 16.6204V3.77045C46.3263 3.13413 45.9334 2.5637 45.3379 2.33974C43.4611 1.63459 39.8338 0.675781 33.9881 0.675781C28.1418 0.675781 24.5141 1.63494 22.637 2.34018C22.0414 2.56379 21.6486 3.13422 21.6486 3.77054L21.6487 3.77045ZM36.7139 10.0516C37.2462 9.51945 38.1088 9.51945 38.6413 10.0516C38.8077 10.219 39.0056 10.3517 39.2236 10.442C39.4417 10.5323 39.6754 10.5785 39.9114 10.5779C40.1475 10.5785 40.3813 10.5324 40.5994 10.442C40.8175 10.3517 41.0155 10.219 41.1819 10.0516C41.7141 9.51918 42.5771 9.51918 43.1093 10.0516C43.6414 10.5838 43.6414 11.4465 43.1093 11.9786C42.255 12.8329 41.1196 13.3035 39.9114 13.3035C38.7036 13.3035 37.5679 12.8329 36.7136 11.9786C36.1818 11.4465 36.1818 10.5838 36.7139 10.0516Z"
          fill="white"
        />
        <path
          d="M28.6202 20.0057L23.1332 8.31067C22.8807 7.77215 22.3213 7.44797 21.7282 7.49291C19.7468 7.64269 16.0305 8.29979 10.6775 10.8113C5.38475 13.2944 2.50798 15.7034 1.10838 17.139C0.664371 17.5946 0.550784 18.2779 0.821022 18.8539L6.27883 30.4873C9.2945 36.915 16.2916 40.4023 23.1774 39.0336C23.6224 38.9452 24.0391 38.7496 24.3914 38.4638C29.8454 34.0435 31.6359 26.4337 28.6202 20.0057ZM17.5931 19.1339C18.4405 16.7887 21.0379 15.5697 23.3832 16.4174C24.0909 16.6731 24.4572 17.4543 24.2016 18.1623C23.946 18.8699 23.1648 19.2366 22.4568 18.9807C21.5251 18.6443 20.4932 19.1284 20.1565 20.06C19.9559 20.6152 19.4327 20.9603 18.8748 20.9603C18.7209 20.9603 18.5645 20.9342 18.4116 20.8788C17.7039 20.6228 17.3372 19.8416 17.5931 19.134V19.1339ZM12.3228 23.7353C12.1009 23.6545 11.8653 23.6185 11.6294 23.6294C11.3936 23.6402 11.1623 23.6978 10.9488 23.7986C10.7349 23.8983 10.5428 24.0394 10.3838 24.2138C10.2248 24.3882 10.1019 24.5924 10.0224 24.8146C9.82178 25.3698 9.29834 25.7149 8.74039 25.7149C8.58677 25.7149 8.43047 25.6884 8.27757 25.6334C7.56948 25.3778 7.20313 24.5965 7.45874 23.8886C7.8694 22.7525 8.69759 21.8442 9.79111 21.3312C10.8851 20.8182 12.1128 20.7615 13.2488 21.1721C13.9568 21.4277 14.3232 22.2089 14.0676 22.9169C13.8116 23.6246 13.0304 23.991 12.3228 23.7353ZM16.8987 32.6435C15.9104 32.6432 14.9298 32.4708 14.0007 32.1339C13.2927 31.8783 12.9264 31.0971 13.182 30.3891C13.4379 29.6814 14.2187 29.3147 14.9268 29.5706C17.9487 30.6625 21.2956 29.0921 22.3875 26.0705C22.6431 25.3625 23.4243 24.9961 24.1323 25.2517C24.84 25.5076 25.2064 26.2888 24.9508 26.9969C23.6939 30.4751 20.4006 32.6435 16.8987 32.6435Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_5095_47342">
          <rect
            width="45.65"
            height="45.65"
            fill="white"
            transform="translate(0.675781 0.675781)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
export const I3 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M40.2815 13.0166L32.8628 15.3611L31.126 24.3161C31.126 24.3161 30.2031 29.5051 30.054 29.3482L26.5447 33.3724C26.4189 33.5129 26.2427 33.5981 26.0545 33.6095C25.8662 33.621 25.681 33.5578 25.5391 33.4336C25.3971 33.3095 25.3098 33.1344 25.296 32.9463C25.2822 32.7582 25.3432 32.5723 25.4656 32.4288L28.8464 28.5551L29.5034 25.1671L25.5012 26.0101L19.962 34.5229C19.903 34.6107 19.8258 34.6848 19.7358 34.7403C19.6457 34.7957 19.5449 34.8313 19.4399 34.8446L8.87649 35.9951L5.02344 43.4715H43.1688V13.0166H40.2815Z"
        fill="white"
      />
      <path
        d="M22.386 15.1962C22.2945 15.2913 22.1939 15.3773 22.0857 15.4529C22.5166 15.8531 22.9337 16.268 23.3361 16.6969C24.8228 18.2887 26.1139 20.0525 27.182 21.9508C27.2681 22.0958 27.4024 22.2061 27.5614 22.2623C27.7205 22.3185 27.8942 22.3171 28.0524 22.2584C28.2105 22.1997 28.343 22.0873 28.4268 21.9409C28.5106 21.7945 28.5403 21.6233 28.5108 21.4572C27.883 18.361 26.4982 15.4685 24.4802 13.0378C23.9038 13.7611 23.0137 14.5121 22.386 15.1962ZM10.0993 4.86811C12.5155 6.22398 14.7091 7.94276 16.6036 9.96437C17.3226 9.17978 18.2334 8.29391 19.0123 7.56995C16.5827 5.55158 13.6895 4.16871 10.5929 3.54571C10.4277 3.51638 10.2574 3.5459 10.1116 3.62914C9.9659 3.71237 9.85395 3.84406 9.79526 4.0013C9.73658 4.15854 9.73486 4.33137 9.79042 4.48974C9.84598 4.64811 9.95529 4.78199 10.0993 4.86811ZM17.8682 10.6862C17.6004 10.9547 17.4501 11.3184 17.4501 11.6976C17.4501 12.0768 17.6004 12.4405 17.8682 12.709C18.8761 13.602 19.6678 15.0828 20.9709 14.4601C21.2919 14.3652 21.799 13.7169 22.0501 13.5022C22.911 12.652 24.3468 11.5835 23.1934 10.3353L21.7141 8.86381C21.5828 8.72982 21.4259 8.62348 21.2529 8.55108C21.0798 8.47869 20.8939 8.44169 20.7063 8.44227C20.0151 8.38521 19.4937 9.08421 19.0551 9.50717C18.7655 9.78535 18.1585 10.4002 17.8682 10.6862ZM9.80619 24.0734C12.3354 21.6983 16.0865 18.1006 18.7762 15.5535C18.4281 15.3574 17.1778 14.0043 16.8611 13.7169C16.5976 13.4567 16.3865 13.1485 16.2391 12.8089L4.42462 24.6162C4.13818 24.9109 3.94542 25.2838 3.87066 25.6879C3.7959 26.092 3.84248 26.5092 4.00452 26.8869C4.16657 27.2645 4.43683 27.5857 4.78121 27.81C5.12559 28.0342 5.52866 28.1514 5.93959 28.1469C7.96954 28.1469 9.41318 25.0314 9.80619 24.0734Z"
        fill="white"
      />
    </svg>
  );
};
export const I4 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M42.5 4.50098L39.327 8.02653L36.173 4.50098L33 8.02653L29.827 4.50098L26.673 8.02653L23.5 4.50098L20.327 8.02653L17.173 4.50098L14 8.02653L10.827 4.50098L7.673 8.02653L4.5 4.50098L4.5 38.2788C4.5 40.601 6.21 42.501 8.3 42.501H38.7C40.79 42.501 42.5 40.601 42.5 38.2788V4.50098ZM21.6 38.2788H8.3V25.6121H21.6V38.2788ZM38.7 38.2788H25.4V34.0565H38.7V38.2788ZM38.7 29.8343H25.4V25.6121H38.7V29.8343ZM38.7 21.3899H8.3V15.0565H38.7V21.3899Z"
        fill="white"
      />
    </svg>
  );
};
export const I5 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M17.4209 35.5249C16.2555 36.5887 14.7346 37.1784 13.1566 37.1784C11.5787 37.1784 10.0578 36.5887 8.8924 35.5249C6.9352 36.6561 3.93094 39.9664 5.53863 42.3587C5.78487 42.7026 6.10944 42.9829 6.48549 43.1765C6.86154 43.3701 7.27828 43.4714 7.70125 43.4721H18.6049C19.0278 43.4715 19.4445 43.3703 19.8206 43.1769C20.1966 42.9834 20.5212 42.7032 20.7675 42.3594C22.3731 39.97 19.3681 36.6561 17.4209 35.5249Z"
        fill="white"
      />
      <path
        d="M18.0281 30.8948C17.8062 24.4234 8.47889 24.4384 8.26562 30.8948C8.48032 37.3228 17.7927 37.3798 18.0281 30.8948ZM28.9603 30.8948C29.1935 37.3763 38.5059 37.3271 38.7227 30.8948C38.5109 24.4412 29.1835 24.4213 28.9603 30.8948Z"
        fill="white"
      />
      <path
        d="M38.107 35.5239C36.9427 36.5903 35.4212 37.1818 33.8423 37.1818C32.2635 37.1818 30.742 36.5903 29.5777 35.5239C27.6327 36.6551 24.6141 39.9668 26.2254 42.3577C26.4714 42.7013 26.7957 42.9815 27.1713 43.175C27.547 43.3686 27.9633 43.4701 28.3859 43.4711H39.2903C39.7132 43.4705 40.1299 43.3693 40.5059 43.1759C40.882 42.9824 41.2066 42.7022 41.4529 42.3584C43.0577 39.9661 40.0592 36.6566 38.107 35.5239ZM21.1413 16.4384H26.3238L30.918 20.1181C31.0225 20.1993 31.1477 20.2498 31.2794 20.2637C31.411 20.2777 31.544 20.2547 31.6633 20.1973C31.7826 20.1399 31.8835 20.0503 31.9547 19.9387C32.0259 19.827 32.0646 19.6977 32.0663 19.5653V16.4384H33.9172C34.4432 16.4369 34.9473 16.2273 35.3192 15.8553C35.6911 15.4834 35.9007 14.9794 35.9023 14.4534V5.51333C35.9019 4.98699 35.6926 4.48231 35.3204 4.11013C34.9483 3.73795 34.4436 3.5287 33.9172 3.52832H21.1413C20.6149 3.5287 20.1102 3.73795 19.7381 4.11013C19.3659 4.48231 19.1566 4.98699 19.1562 5.51333V14.4534C19.1578 14.9794 19.3674 15.4834 19.7393 15.8553C20.1112 16.2273 20.6153 16.4369 21.1413 16.4384Z"
        fill="white"
      />
      <path
        d="M17.7403 14.4536V9.97998H12.8759C12.4022 9.98055 11.9482 10.169 11.6133 10.504C11.2785 10.8389 11.0902 11.2931 11.0898 11.7667V19.2888C11.0902 19.7625 11.2786 20.2168 11.6136 20.5518C11.9486 20.8868 12.4028 21.0751 12.8766 21.0755H14.3159V23.599C14.3187 23.7318 14.3582 23.8612 14.4301 23.9729C14.5021 24.0845 14.6036 24.174 14.7233 24.2314C14.8431 24.2888 14.9765 24.3118 15.1085 24.2978C15.2406 24.2839 15.3662 24.2336 15.4714 24.1525L19.3066 21.0755H23.6312C24.1049 21.0751 24.5592 20.8868 24.8941 20.5518C25.2291 20.2168 25.4175 19.7625 25.4179 19.2888V17.8566H21.1426C20.2402 17.8562 19.375 17.4975 18.737 16.8594C18.099 16.2213 17.7405 15.3559 17.7403 14.4536Z"
        fill="white"
      />
    </svg>
  );
};
export const I6 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M36.8159 33.9619C36.8253 33.8479 36.8253 33.7334 36.8159 33.6195C36.8159 32.6106 36.4151 31.643 35.7017 30.9296C34.9883 30.2162 34.0207 29.8154 33.0118 29.8154C32.0029 29.8154 31.0353 30.2162 30.3219 30.9296C29.6085 31.643 29.2077 32.6106 29.2077 33.6195C29.1983 33.7334 29.1983 33.8479 29.2077 33.9619C29.2077 34.9708 29.6085 35.9384 30.3219 36.6518C31.0353 37.3652 32.0029 37.7659 33.0118 37.7659C34.0207 37.7659 34.9883 37.3652 35.7017 36.6518C36.4151 35.9384 36.8159 34.9708 36.8159 33.9619ZM21.5996 33.9619C21.609 33.8479 21.609 33.7334 21.5996 33.6195C21.5996 32.6106 21.1988 31.643 20.4854 30.9296C19.772 30.2162 18.8044 29.8154 17.7955 29.8154C16.7866 29.8154 15.819 30.2162 15.1056 30.9296C14.3922 31.643 13.9915 32.6106 13.9915 33.6195C13.982 33.7334 13.982 33.8479 13.9915 33.9619C13.9915 34.9708 14.3922 35.9384 15.1056 36.6518C15.819 37.3652 16.7866 37.7659 17.7955 37.7659C18.8044 37.7659 19.772 37.3652 20.4854 36.6518C21.1988 35.9384 21.5996 34.9708 21.5996 33.9619Z"
        fill="white"
      />
      <path
        d="M7.33438 24.9269H4.48125C4.21933 24.9263 3.96243 24.9987 3.73944 25.1362C3.54567 25.2556 3.3833 25.4197 3.26589 25.6147C3.14848 25.8097 3.07945 26.03 3.06456 26.2572C3.04966 26.4843 3.08934 26.7117 3.18027 26.9204C3.27121 27.1291 3.41076 27.313 3.58727 27.4567C3.83622 27.6693 4.15392 27.7842 4.48125 27.78H9.23646C9.61481 27.78 9.97766 27.9303 10.2452 28.1979C10.5127 28.4654 10.663 28.8283 10.663 29.2066C10.663 29.585 10.5127 29.9478 10.2452 30.2153C9.97766 30.4829 9.61481 30.6332 9.23646 30.6332H7.33438C6.95603 30.6332 6.59318 30.7835 6.32564 31.051C6.05811 31.3185 5.90781 31.6814 5.90781 32.0597C5.90781 32.4381 6.05811 32.8009 6.32564 33.0685C6.59318 33.336 6.95603 33.4863 7.33438 33.4863H12.5841C12.666 32.519 13.0223 31.5951 13.6113 30.8234C14.1035 30.1791 14.7363 29.6557 15.4615 29.2931C16.1867 28.9305 16.9851 28.7383 17.7958 28.7311C19.1025 28.7261 20.3634 29.2121 21.3286 30.0928C22.2939 30.9735 22.8931 32.1847 23.0075 33.4863H27.8008C27.9124 32.1809 28.5098 30.965 29.4749 30.0789C30.44 29.1929 31.7024 28.7012 33.0125 28.7012C34.3226 28.7012 35.5851 29.1929 36.5501 30.0789C37.5152 30.965 38.1126 32.1809 38.2242 33.4863H40.811C41.256 33.4953 41.6901 33.3479 42.0376 33.0699C42.3852 32.7918 42.6242 32.4007 42.7131 31.9646L43.9685 25.6878C43.9587 25.6439 43.9587 25.5984 43.9685 25.5546C43.9955 25.3909 43.9955 25.2238 43.9685 25.0601L40.3926 14.5225C40.1977 13.9629 39.8324 13.4784 39.3479 13.1373C38.8634 12.7961 38.2842 12.6153 37.6916 12.6204H34.2679L34.4771 11.0988C34.5282 10.6956 34.4932 10.2862 34.3744 9.8975C34.2556 9.50886 34.0556 9.14985 33.7877 8.84422C33.5198 8.53859 33.1902 8.29329 32.8204 8.12452C32.4507 7.95576 32.0494 7.86739 31.643 7.86523H10.1875C9.80915 7.86523 9.4463 8.01553 9.17877 8.28307C8.91124 8.5506 8.76094 8.91345 8.76094 9.2918C8.76094 9.67015 8.91124 10.033 9.17877 10.3005C9.4463 10.5681 9.80915 10.7184 10.1875 10.7184H12.5651C12.9435 10.7184 13.3063 10.8687 13.5738 11.1362C13.8414 11.4037 13.9917 11.7666 13.9917 12.1449C13.9917 12.5233 13.8414 12.8861 13.5738 13.1537C13.3063 13.4212 12.9435 13.5715 12.5651 13.5715H4.48125C4.10444 13.5764 3.74444 13.7283 3.47797 13.9948C3.2115 14.2612 3.05962 14.6212 3.05469 14.998C3.05416 15.3585 3.19197 15.7055 3.43969 15.9674C3.68742 16.2293 4.02619 16.3861 4.38615 16.4056H9.23646C9.61481 16.4056 9.97766 16.5559 10.2452 16.8234C10.5127 17.091 10.663 17.4538 10.663 17.8322C10.663 18.2105 10.5127 18.5734 10.2452 18.8409C9.97766 19.1084 9.61481 19.2587 9.23646 19.2587H6.079C5.76188 19.328 5.47786 19.5033 5.27383 19.7557C5.0698 20.0082 4.95796 20.3226 4.95677 20.6472C4.95677 21.0256 5.10707 21.3884 5.3746 21.656C5.64214 21.9235 6.00499 22.0738 6.38334 22.0738H7.33438C7.71272 22.0738 8.07558 22.2241 8.34311 22.4916C8.61064 22.7592 8.76094 23.122 8.76094 23.5004C8.76094 23.8787 8.61064 24.2416 8.34311 24.5091C8.07558 24.7766 7.71272 24.9269 7.33438 24.9269ZM33.9635 15.4165H37.4253C37.5252 15.418 37.6222 15.4497 37.7037 15.5074C37.7852 15.5651 37.8473 15.6462 37.8818 15.7399L40.6208 23.9759H32.8223L33.9635 15.4165Z"
        fill="white"
      />
    </svg>
  );
};
export const I7 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_5095_47330)">
        <path
          d="M33.3562 5.88686C32.1324 4.73425 30.5652 4.18066 28.9859 4.18066C27.2081 4.18066 25.414 4.88211 24.0757 6.2209L23.5007 6.79589L22.9257 6.22068C21.5875 4.88244 19.7934 4.18066 18.0156 4.18066C16.436 4.18066 14.8695 4.73425 13.6452 5.88686C10.9528 8.42196 10.9047 12.6611 13.5011 15.2574L23.5008 25.257L33.5005 15.2574C36.0968 12.6609 36.0487 8.42185 33.3562 5.88686ZM19.9958 42.8207L19.9881 32.9128C19.9881 28.1278 17.5371 25.3225 14.2907 24.2658C13.3846 23.9916 12.5728 23.2711 11.8178 22.5158C10.4461 21.0872 8.97965 19.1334 7.08004 19.1576L7.07828 19.1504C6.45861 19.1585 5.83905 19.3824 5.30183 19.9197C4.04196 21.1795 4.55098 23.1044 5.63751 24.1909L9.16693 27.72C9.49627 28.0494 9.68128 28.4961 9.68128 28.9619C9.68128 29.4277 9.49627 29.8744 9.16693 30.2038L4.39565 25.4328C3.56643 24.6037 3.00296 23.4828 2.84939 22.3578C2.65817 20.9564 3.08782 19.6495 4.05942 18.6779C4.7871 17.9502 5.67988 17.5395 6.65368 17.4356L5.94103 14.5573C5.88702 3.8755 0.67622 5.18047 0.67622 7.69328L0.675781 26.4457C0.675781 27.6104 1.12695 28.7081 1.94673 29.5364C10.6719 38.3424 9.4576 35.5054 9.4576 42.8207H19.9958ZM46.3253 7.69339C46.3253 5.18069 41.1145 3.87561 41.0605 14.5575L40.3479 17.4358C41.3217 17.5397 42.2145 17.9503 42.9422 18.6781C43.9137 19.6497 44.3435 20.9566 44.1522 22.3581C43.9987 23.483 43.4352 24.6039 42.6059 25.433L37.8346 30.2041C37.5053 29.8745 37.3201 29.428 37.3201 28.9621C37.3201 28.4964 37.5053 28.0496 37.8346 27.7202L41.3641 24.1911C42.4506 23.1046 42.9596 21.1796 41.6997 19.9199C41.1625 19.3827 40.5428 19.1587 39.9233 19.1506L39.9215 19.1578C38.0219 19.1336 36.5555 21.0874 35.1837 22.516C34.4286 23.2714 33.6168 23.9917 32.7109 24.266C29.4645 25.3226 27.0135 28.128 27.0135 32.9129L27.0058 42.8208H37.544C37.544 35.5058 36.3297 38.3426 45.0548 29.5365C45.8747 28.7081 46.3258 27.6104 46.3258 26.4458L46.3253 7.69339Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_5095_47330">
          <rect
            width="45.65"
            height="45.65"
            fill="white"
            transform="translate(0.675781 0.675781)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
export const I8 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_5095_47354)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.8349 9.49408H12.3569V12.3403C12.3569 12.7606 12.0163 13.1012 11.5959 13.1012C11.1755 13.1012 10.8349 12.7607 10.8349 12.3403V9.49408ZM12.3569 9.49391V8.88075C12.3569 5.19531 15.3545 2.19677 19.0408 2.19677C22.7258 2.19677 25.7239 5.19531 25.7239 8.88075V9.49391H12.3569ZM25.7239 9.49408H27.2454V12.3403C27.2465 12.4409 27.2277 12.5407 27.1899 12.6339C27.1522 12.7272 27.0963 12.812 27.0256 12.8835C26.9548 12.9551 26.8706 13.0118 26.7777 13.0506C26.6849 13.0893 26.5853 13.1093 26.4847 13.1093C26.3841 13.1093 26.2845 13.0893 26.1916 13.0506C26.0988 13.0118 26.0146 12.9551 25.9438 12.8835C25.8731 12.812 25.8172 12.7272 25.7795 12.6339C25.7417 12.5407 25.7228 12.4409 25.7239 12.3403V9.49408ZM27.2454 9.49391V8.88075C27.2454 4.35534 23.5649 0.674805 19.0408 0.674805C14.5154 0.674805 10.8349 4.35534 10.8349 8.88066V9.49382H5.99635L3.70983 36.6853C3.66989 37.1618 3.80809 37.5638 4.13111 37.9151C4.45459 38.2669 4.84359 38.4379 5.32105 38.4379H11.6093L13.4484 16.5566C13.4821 16.1626 13.811 15.8593 14.2067 15.8593H32.0234V9.49391H27.2454ZM34.6339 25.2098V20.2285C34.6339 19.8081 34.9745 19.4675 35.3949 19.4675C35.8153 19.4675 36.1558 19.8081 36.1558 20.2285V25.2098C36.1558 29.7352 32.4753 33.4153 27.9503 33.4153C23.4258 33.4153 19.7453 29.7352 19.7453 25.2098V20.2285C19.7453 20.1286 19.7649 20.0296 19.8032 19.9373C19.8414 19.8449 19.8975 19.761 19.9681 19.6904C20.0388 19.6197 20.1227 19.5637 20.215 19.5254C20.3074 19.4872 20.4063 19.4675 20.5063 19.4675C20.6062 19.4675 20.7052 19.4872 20.7975 19.5254C20.8899 19.5637 20.9738 19.6197 21.0444 19.6904C21.1151 19.761 21.1711 19.8449 21.2094 19.9373C21.2476 20.0296 21.2673 20.1286 21.2673 20.2285V25.2098C21.2673 28.8961 24.2649 31.8938 27.9503 31.8938C31.6362 31.8939 34.6339 28.8962 34.6339 25.2098ZM40.9944 17.3807L43.2809 44.5731C43.3208 45.0487 43.1831 45.4511 42.8596 45.803C42.5361 46.1538 42.1471 46.3247 41.6697 46.3247H14.2315C13.7541 46.3247 13.3651 46.1538 13.0416 45.803C12.7181 45.4512 12.5804 45.0488 12.6203 44.5731L14.9068 17.3807H40.9944Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_5095_47354">
          <rect
            width="45.65"
            height="45.65"
            fill="white"
            transform="translate(0.671875 0.674805)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
export const I9 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M40.7981 5.11437C40.56 4.93751 40.2833 4.81976 39.9908 4.77082C39.6983 4.72187 39.3984 4.74314 39.1157 4.83286C36.565 5.62698 33.2934 6.15767 29.902 6.3241C25.9637 6.52287 22.1244 6.20522 19.3645 5.46245C16.6169 4.72445 13.0524 4.39539 9.23776 4.50475V3.52708C9.23776 3.02262 9.03736 2.53882 8.68065 2.18211C8.32394 1.8254 7.84014 1.625 7.33568 1.625C6.83121 1.625 6.34741 1.8254 5.9907 2.18211C5.63399 2.53882 5.43359 3.02262 5.43359 3.52708V43.4708C5.43359 43.9753 5.63399 44.4591 5.9907 44.8158C6.34741 45.1725 6.83121 45.3729 7.33568 45.3729C7.84014 45.3729 8.32394 45.1725 8.68065 44.8158C9.03736 44.4591 9.23776 43.9753 9.23776 43.4708V29.2328C12.8546 29.1253 16.324 29.4401 18.8709 30.1239C21.2741 30.7697 24.3023 31.1083 27.5757 31.1083C28.3699 31.1083 29.1783 31.0883 29.9952 31.0483C33.8422 30.8581 37.5018 30.2276 40.2978 29.2708C40.672 29.1418 40.9962 28.8987 41.225 28.5757C41.4537 28.2528 41.5755 27.8663 41.5732 27.4705V6.6484C41.5727 6.35019 41.5024 6.05623 41.368 5.79003C41.2335 5.52383 41.0387 5.29277 40.799 5.11532L40.7981 5.11437ZM29.902 29.1501C28.0863 29.2426 26.2666 29.2219 24.4535 29.0882V21.9602C22.5134 21.8175 20.6874 21.5417 19.1181 21.1233C18.4049 20.9331 17.644 20.7714 16.8451 20.6477V27.7634C14.9716 27.4629 12.8745 27.3041 10.6586 27.3041C10.1898 27.3041 9.71518 27.3107 9.23681 27.325V20.2046C11.8826 20.1285 14.5265 20.2578 16.8451 20.6477V13.9895C14.5265 13.5986 11.8826 13.4712 9.23681 13.5473V6.40303C11.9682 6.31839 14.615 6.48292 16.8451 6.86429V13.9895C17.644 14.1131 18.4049 14.2748 19.1181 14.465C20.6874 14.8835 22.5134 15.1593 24.4535 15.3019V8.17007C26.2053 8.29466 28.0722 8.31748 29.9942 8.22428C30.6845 8.18903 31.3738 8.13861 32.0618 8.07306V15.2068C34.8056 14.95 37.4685 14.4546 39.6749 13.7337L39.6797 20.39C37.4723 21.1119 34.8075 21.6083 32.0618 21.8651V28.9893C31.3514 29.0569 30.6324 29.1139 29.902 29.1501Z"
        fill="white"
      />
      <path
        d="M29.95 22.0173C30.6538 21.9793 31.367 21.9317 32.0613 21.8651V15.208C31.367 15.2746 30.6538 15.3221 29.95 15.3602C28.0955 15.4553 26.2315 15.4363 24.4531 15.3031V21.9602C26.2315 22.0934 28.0955 22.1124 29.95 22.0173Z"
        fill="white"
      />
    </svg>
  );
};

export const I11 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.2492 11.1833V10.3203C17.2492 7.43588 8.28239 7.43588 8.28239 10.3203V11.0298C11.4976 9.2197 14.0346 9.48291 17.2492 11.1833ZM38.7093 11.0298V10.3203C38.7093 7.43588 29.7424 7.43588 29.7424 10.3203V11.1833C32.9572 9.483 35.4942 9.2197 38.7093 11.0298ZM17.2845 12.4221C20.872 14.4498 26.1198 14.4498 29.7072 12.4221C32.9252 10.6033 35.2896 10.1046 38.8201 12.3429C42.8472 14.8961 45.9899 24.9498 45.345 31.7397C44.5829 39.7654 37.5746 40.9114 32.6392 35.5777C30.0916 32.8245 27.2984 31.3362 23.4959 31.2969C19.6934 31.3363 16.9002 32.8245 14.3526 35.5777C9.41714 40.9114 2.40888 39.7654 1.64674 31.7397C1.00184 24.9497 4.14456 14.896 8.17166 12.3429C11.7021 10.1046 14.0666 10.6033 17.2845 12.4221ZM14.3462 21.5942V18.3504H11.1023V21.5942H7.85835V24.8382H11.1023V28.0821H14.3462V24.8382H17.59V21.5942H14.3462ZM33.7589 17.8419C32.8458 17.8419 32.1055 18.5821 32.1055 19.4952C32.1055 20.4083 32.8458 21.1486 33.7589 21.1486C34.672 21.1486 35.4123 20.4083 35.4123 19.4952C35.4123 18.5821 34.672 17.8419 33.7589 17.8419ZM31.6914 23.2162C31.6914 22.303 30.9511 21.5628 30.038 21.5628C29.1249 21.5628 28.3846 22.3031 28.3846 23.2162C28.3846 24.1292 29.1249 24.8695 30.038 24.8695C30.9511 24.8695 31.6914 24.1292 31.6914 23.2162ZM33.7589 25.2837C32.8458 25.2837 32.1055 26.024 32.1055 26.9371C32.1055 27.8502 32.8458 28.5905 33.7589 28.5905C34.672 28.5905 35.4123 27.8502 35.4123 26.9371C35.4123 26.024 34.672 25.2837 33.7589 25.2837ZM39.1332 23.2162C39.1332 22.303 38.3929 21.5628 37.4799 21.5628C36.5668 21.5628 35.8265 22.3031 35.8265 23.2162C35.8265 24.1292 36.5668 24.8695 37.4799 24.8695C38.3929 24.8695 39.1332 24.1292 39.1332 23.2162Z"
        fill="white"
      />
    </svg>
  );
};
export const I12 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_5095_47385)">
        <path
          d="M39.6367 7.36003C35.3256 3.04914 29.5936 0.674805 23.4969 0.674805C17.4002 0.674805 11.6682 3.04905 7.3571 7.36003C3.04621 11.6711 0.671875 17.4031 0.671875 23.4998C0.671875 29.5965 3.04612 35.3285 7.3571 39.6396C11.6682 43.9505 17.4002 46.3248 23.4969 46.3248C29.5936 46.3248 35.3256 43.9506 39.6367 39.6396C43.9475 35.3285 46.3219 29.5965 46.3219 23.4998C46.3219 17.4031 43.9476 11.6711 39.6367 7.36003ZM23.4969 41.5102C13.566 41.5102 5.48652 33.4307 5.48652 23.4998C5.48652 13.5689 13.566 5.48945 23.4969 5.48945C33.4278 5.48945 41.5072 13.5689 41.5072 23.4998C41.5072 33.4307 33.4278 41.5102 23.4969 41.5102Z"
          fill="white"
        />
        <path
          d="M23.4992 8.16406C15.0435 8.16406 8.16406 15.0435 8.16406 23.4992C8.16406 31.955 15.0435 38.8344 23.4992 38.8344C31.955 38.8344 38.8344 31.955 38.8344 23.4992C38.8344 15.0435 31.955 8.16406 23.4992 8.16406ZM29.2527 28.9709C28.9278 29.5822 28.49 30.077 27.939 30.4543C27.3877 30.8318 26.7539 31.1076 26.0368 31.2808C25.6443 31.3758 25.2413 31.4436 24.8282 31.4869V34.6039H22.1535V31.428C21.4936 31.3375 20.8422 31.1936 20.2056 30.9976C19.0278 30.6353 17.9707 30.1223 17.0349 29.4578L18.4165 26.7629C18.5524 26.8987 18.7976 27.0763 19.1524 27.2951C19.507 27.5141 19.926 27.7332 20.4094 27.9519C20.8924 28.1709 21.4284 28.3559 22.0173 28.5067C22.6059 28.6578 23.2095 28.7332 23.8287 28.7332C25.5499 28.7332 26.4104 28.1822 26.4104 27.0798C26.4104 26.7326 26.3119 26.4383 26.1162 26.1966C25.9197 25.9553 25.6404 25.7439 25.2782 25.5627C24.916 25.3813 24.4779 25.2155 23.9649 25.0643C23.3918 24.8963 22.818 24.7303 22.2437 24.5664C21.4736 24.355 20.8057 24.1248 20.2394 23.8754C19.6735 23.6264 19.2012 23.3321 18.8241 22.9922C18.4465 22.6525 18.1634 22.2601 17.9749 21.8146C17.7862 21.3695 17.6917 20.8374 17.6917 20.2181C17.6917 19.4029 17.8426 18.6781 18.1448 18.0442C18.4465 17.41 18.8654 16.8817 19.4014 16.459C19.9375 16.0364 20.5601 15.7154 21.2699 15.4962C21.5597 15.4077 21.8548 15.3376 22.1535 15.2863V12.3714H24.8282V15.2478C25.4802 15.3321 26.0984 15.4826 26.6821 15.6999C27.6332 16.0552 28.4635 16.4738 29.1733 16.9568L27.792 19.4934C27.6862 19.3878 27.4936 19.2443 27.2143 19.0628C26.9349 18.8817 26.5951 18.7044 26.1952 18.5306C25.795 18.3572 25.3611 18.2103 24.8934 18.089C24.4273 17.9687 23.9479 17.9079 23.4665 17.908C22.6059 17.908 21.964 18.0664 21.5416 18.3837C21.1188 18.7007 20.9074 19.146 20.9074 19.7196C20.9074 20.0519 20.9868 20.3277 21.1453 20.5465C21.3037 20.7656 21.5336 20.9578 21.8359 21.1239C22.1378 21.2901 22.5188 21.4408 22.9796 21.5766C23.44 21.7125 23.9722 21.8563 24.5761 22.0072C25.3611 22.2185 26.0744 22.4449 26.7162 22.6863C27.3577 22.9279 27.9011 23.2299 28.3465 23.5921C28.7916 23.9546 29.1353 24.3887 29.3771 24.8943C29.6184 25.4003 29.7392 26.023 29.7392 26.7625C29.7396 27.6234 29.5769 28.3593 29.2527 28.9709Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_5095_47385">
          <rect
            width="45.65"
            height="45.65"
            fill="white"
            transform="translate(0.671875 0.674805)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const I15 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 46"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <mask
        id="mask0_5095_47316"
        style={{ maskType: "luminance" }}
        maskUnits="userSpaceOnUse"
        x={0}
        y={0}
        {...svgProps}
      >
        <path
          d="M0.675781 0.0908203H46.3258V45.7408H0.675781V0.0908203Z"
          fill="white"
        />
      </mask>
      <g mask="url(#mask0_5095_47316)">
        <path
          d="M23.5011 15.3071C24.51 15.3071 25.4776 14.9063 26.191 14.1929C26.9044 13.4795 27.3052 12.512 27.3052 11.503C27.3052 10.4941 26.9044 9.52656 26.191 8.81316C25.4776 8.09976 24.51 7.69897 23.5011 7.69897C22.4922 7.69897 21.5247 8.09976 20.8113 8.81316C20.0979 9.52656 19.6971 10.4941 19.6971 11.503C19.6971 12.512 20.0979 13.4795 20.8113 14.1929C21.5247 14.9063 22.4922 15.3071 23.5011 15.3071ZM40.6195 28.5073C40.6195 27.5562 39.9347 26.7384 39.0027 26.6433C35.4269 26.2438 32.3646 24.494 29.9681 21.6409L27.4193 18.5976C26.6966 17.7037 25.6314 17.2092 24.5092 17.2092H22.5121C21.3899 17.2092 20.3247 17.7037 19.602 18.5786L17.0532 21.6219C14.6757 24.4749 11.5944 26.2248 8.01856 26.6242C7.06755 26.7384 6.38281 27.5562 6.38281 28.5073C6.38281 29.6485 7.39089 30.5424 8.53211 30.4093C12.9068 29.8957 16.7489 27.7655 19.6971 24.3418V28.6214L12.5454 31.4744C11.3091 31.969 10.339 33.0531 10.2059 34.3845C10.1581 34.858 10.21 35.3362 10.3583 35.7883C10.5066 36.2404 10.7481 36.6564 11.0671 37.0095C11.3861 37.3625 11.7756 37.6448 12.2104 37.8381C12.6452 38.0313 13.1157 38.1313 13.5915 38.1316H17.795V37.1805C17.795 35.9194 18.296 34.7099 19.1878 33.8182C20.0795 32.9264 21.289 32.4254 22.5501 32.4254H28.2562C28.7888 32.4254 29.2072 32.8439 29.2072 33.3765C29.2072 33.909 28.7888 34.3275 28.2562 34.3275H22.5501C20.9714 34.3275 19.6971 35.6019 19.6971 37.1805V38.1316H33.2015C34.8183 38.1316 36.3399 37.1045 36.7203 35.5258C37.1197 33.833 36.2067 32.1782 34.6661 31.5695L27.3052 28.6214V24.3418C30.2724 27.7655 34.0955 29.8767 38.4702 30.4093C39.6114 30.5234 40.6195 29.6485 40.6195 28.5073Z"
          fill="white"
        />
      </g>
    </svg>
  );
};
export const I13 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_5095_47306)">
        <path
          d="M30.6632 25.3426L35.2285 26.2487C35.2758 28.488 37.1295 30.265 39.3688 30.2177C41.608 30.1704 43.385 28.3167 43.3377 26.0774C43.2904 23.8381 41.4367 22.0612 39.1975 22.1085C38.4271 22.1247 37.6773 22.3601 37.0359 22.7871C36.3945 23.214 35.888 23.8149 35.5757 24.5193L30.892 23.5896C30.8871 21.8757 30.2201 20.2299 29.0302 18.9963L35.0925 10.4324C37.6495 11.6482 40.7079 10.5609 41.9237 8.00394C43.1395 5.44695 42.0522 2.38855 39.4952 1.17273C36.9383 -0.0430923 33.8799 1.04418 32.664 3.60117C31.736 5.55312 32.1316 7.87737 33.6535 9.41214L27.6498 17.8932C24.8888 16.235 21.3366 16.7923 19.2158 19.2162L11.334 13.0951C12.3686 11.1037 11.593 8.65055 9.60161 7.61597C7.61019 6.5814 5.15705 7.357 4.12248 9.34842C3.08791 11.3398 3.86351 13.793 5.85492 14.8275C6.54032 15.1836 7.31302 15.337 8.08247 15.2697C8.85191 15.2024 9.58623 14.9171 10.1994 14.4474L18.2332 20.6865C16.8983 23.4137 17.5786 26.6987 19.8869 28.6713L15.0045 35.5743C12.4198 34.0057 9.05268 34.8295 7.48406 37.4144C5.91545 39.9991 6.73923 43.3662 9.32411 44.9348C11.9089 46.5035 15.276 45.6797 16.8446 43.0948C18.0636 41.086 17.8648 38.5232 16.3505 36.7265L21.3646 29.6353C22.2127 30.0386 23.1365 30.258 24.0754 30.279C25.0143 30.3001 25.947 30.1224 26.8124 29.7576L32.7617 38.5462C31.012 40.3961 31.0932 43.3143 32.9432 45.064C34.7931 46.8138 37.7113 46.7325 39.4611 44.8826C41.2108 43.0326 41.1296 40.1144 39.2796 38.3647C38.6128 37.734 37.7728 37.3166 36.8674 37.1661C35.9619 37.0156 35.0322 37.1388 34.1971 37.52L28.3336 28.858C29.4691 27.9683 30.2864 26.7351 30.6632 25.3426Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_5095_47306">
          <rect
            width="45.65"
            height="45.65"
            fill="white"
            transform="translate(0.675781 0.674805)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
export const I14 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M30.348 5.24006V6.00088C30.348 9.78251 27.2823 12.8482 23.5007 12.8482C19.7191 12.8482 16.6534 9.78251 16.6534 6.00088V5.24006C16.6534 5.03828 16.7335 4.84477 16.8762 4.70209C17.0189 4.55941 17.2124 4.47925 17.4142 4.47925H29.5872C29.789 4.47925 29.9825 4.55941 30.1252 4.70209C30.2679 4.84477 30.348 5.03828 30.348 5.24006ZM8.25816 21.978C8.07704 21.978 7.90187 22.0427 7.76415 22.1603C7.62643 22.2779 7.5352 22.4408 7.50685 22.6197L6.52654 28.8013C6.50933 28.91 6.51587 29.021 6.54571 29.1269C6.57556 29.2327 6.62799 29.3308 6.6994 29.4145C6.77081 29.4981 6.8595 29.5652 6.95936 29.6113C7.05922 29.6574 7.16788 29.6812 7.27785 29.6812H14.8909C15.0775 29.6813 15.2576 29.6127 15.397 29.4887C15.5363 29.3646 15.6253 29.1936 15.6468 29.0083L16.3654 22.8267C16.3778 22.72 16.3675 22.612 16.3352 22.5097C16.3028 22.4073 16.2492 22.3129 16.1778 22.2328C16.1064 22.1526 16.0189 22.0885 15.9209 22.0446C15.823 22.0007 15.7169 21.978 15.6095 21.978H8.25816ZM6.69697 31.821C6.51585 31.8211 6.34068 31.8857 6.20296 32.0033C6.06524 32.121 5.97401 32.2839 5.94566 32.4628L4.48984 41.6401C4.47263 41.7487 4.47917 41.8598 4.50901 41.9656C4.53885 42.0714 4.59129 42.1696 4.6627 42.2532C4.7341 42.3368 4.8228 42.404 4.92266 42.45C5.02252 42.4961 5.13118 42.52 5.24115 42.52H13.3986C13.5851 42.5199 13.7651 42.4513 13.9044 42.3273C14.0437 42.2032 14.1325 42.0323 14.1541 41.847L15.2211 32.6697C15.2335 32.5631 15.2232 32.455 15.1909 32.3527C15.1586 32.2503 15.1049 32.156 15.0336 32.0758C14.9622 31.9957 14.8746 31.9315 14.7767 31.8876C14.6787 31.8437 14.5726 31.821 14.4653 31.821H6.69697ZM30.5839 41.6713C30.5963 41.7779 30.586 41.886 30.5536 41.9884C30.5213 42.0908 30.4676 42.1852 30.3962 42.2653C30.3247 42.3455 30.2372 42.4096 30.1392 42.4535C30.0412 42.4974 29.935 42.52 29.8276 42.52H17.084C16.9767 42.5199 16.8706 42.4972 16.7727 42.4533C16.6748 42.4093 16.5873 42.3452 16.5159 42.265C16.4446 42.1849 16.391 42.0905 16.3587 41.9882C16.3264 41.8859 16.3161 41.7779 16.3285 41.6713L17.3955 32.494C17.4171 32.3087 17.5059 32.1378 17.6452 32.0137C17.7845 31.8897 17.9645 31.8211 18.151 31.821H28.7606C28.9472 31.821 29.1273 31.8895 29.2666 32.0136C29.406 32.1377 29.4949 32.3086 29.5165 32.494L30.5839 41.6713ZM29.0912 28.8325C29.1036 28.9392 29.0933 29.0472 29.0609 29.1496C29.0286 29.2519 28.975 29.3463 28.9036 29.4264C28.8322 29.5066 28.7447 29.5707 28.6467 29.6146C28.5488 29.6586 28.4426 29.6812 28.3353 29.6812H18.5771C18.4698 29.6812 18.3637 29.6585 18.2658 29.6145C18.1679 29.5706 18.0804 29.5064 18.009 29.4263C17.9377 29.3461 17.8841 29.2518 17.8518 29.1495C17.8195 29.0471 17.8092 28.9391 17.8216 28.8325L18.5402 22.6509C18.5617 22.4656 18.6506 22.2947 18.7899 22.1707C18.9291 22.0466 19.1092 21.978 19.2957 21.978H27.6167C27.8033 21.9779 27.9834 22.0465 28.1228 22.1705C28.2621 22.2946 28.351 22.4656 28.3726 22.6509L29.0912 28.8325ZM33.5142 42.52C33.3276 42.52 33.1475 42.4515 33.0081 42.3274C32.8687 42.2033 32.7798 42.0324 32.7583 41.847L31.6912 32.6697C31.6788 32.5631 31.6892 32.455 31.7215 32.3527C31.7538 32.2503 31.8074 32.156 31.8788 32.0758C31.9502 31.9957 32.0378 31.9315 32.1357 31.8876C32.2336 31.8437 32.3398 31.821 32.4471 31.821H40.2154C40.3965 31.8211 40.5717 31.8857 40.7094 32.0033C40.8471 32.121 40.9384 32.2839 40.9667 32.4628L42.4225 41.6401C42.4397 41.7487 42.4332 41.8598 42.4034 41.9656C42.3735 42.0714 42.3211 42.1696 42.2497 42.2532C42.1783 42.3368 42.0896 42.404 41.9897 42.45C41.8899 42.4961 41.7812 42.52 41.6712 42.52H33.5142ZM31.2656 29.0083C31.2871 29.1937 31.3761 29.3647 31.5155 29.4888C31.655 29.6129 31.8352 29.6814 32.0218 29.6812H39.6349C39.7449 29.6813 39.8536 29.6574 39.9535 29.6114C40.0534 29.5654 40.1422 29.4982 40.2136 29.4146C40.2851 29.331 40.3375 29.2328 40.3674 29.127C40.3973 29.0211 40.4038 28.91 40.3866 28.8013L39.4059 22.6197C39.3775 22.4408 39.2863 22.2778 39.1485 22.1602C39.0107 22.0425 38.8354 21.9779 38.6542 21.978H31.3029C31.1955 21.978 31.0894 22.0007 30.9914 22.0446C30.8935 22.0885 30.8059 22.1526 30.7346 22.2328C30.6632 22.3129 30.6095 22.4073 30.5772 22.5097C30.5449 22.612 30.5346 22.72 30.547 22.8267L31.2656 29.0083ZM14.8616 4.47925C14.7883 4.72618 14.7511 4.98246 14.7513 5.24006V6.00088C14.7513 6.12831 14.754 6.25499 14.7593 6.38128H11.7081C11.4558 6.38128 11.214 6.28109 11.0356 6.10274C10.8573 5.92439 10.7571 5.68249 10.7571 5.43027C10.7571 5.17804 10.8573 4.93615 11.0356 4.75779C11.214 4.57944 11.4558 4.47925 11.7081 4.47925H14.8616ZM32.2501 5.24006V6.00088C32.2501 6.12831 32.2474 6.25499 32.2421 6.38128H35.2933C35.5455 6.38128 35.7874 6.28109 35.9658 6.10274C36.1441 5.92439 36.2443 5.68249 36.2443 5.43027C36.2443 5.17804 36.1441 4.93615 35.9658 4.75779C35.7874 4.57944 35.5455 4.47925 35.2933 4.47925H32.1397C32.2116 4.72043 32.2501 4.97568 32.2501 5.24006ZM14.6631 13.3983L16.6355 11.4255C17.0302 11.9242 17.4777 12.3786 17.9703 12.7809L16.0078 14.743C15.8285 14.9163 15.5882 15.0121 15.3389 15.0099C15.0895 15.0078 14.851 14.9078 14.6747 14.7314C14.4983 14.5551 14.3983 14.3166 14.3961 14.0672C14.394 13.8179 14.4898 13.5776 14.6631 13.3983ZM22.5497 14.6993V17.7935C22.5497 18.0457 22.6499 18.2876 22.8282 18.466C23.0066 18.6443 23.2485 18.7445 23.5007 18.7445C23.7529 18.7445 23.9948 18.6443 24.1732 18.466C24.3515 18.2876 24.4517 18.0457 24.4517 17.7935V14.6993C23.8195 14.7676 23.1818 14.7676 22.5497 14.6993ZM31.2785 14.743L29.1863 12.6512C29.6696 12.2376 30.1068 11.7728 30.4899 11.265L32.6232 13.3983C32.7965 13.5776 32.8923 13.8179 32.8902 14.0672C32.888 14.3166 32.788 14.5551 32.6117 14.7314C32.4353 14.9078 32.1968 15.0078 31.9475 15.0099C31.6981 15.0121 31.4579 14.9163 31.2785 14.743Z"
        fill="white"
      />
    </svg>
  );
};

export const DecorativeLinesIcon = ({ fill = "#35B729", className }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 100 11">
      <path
        d="M2.13235 5.64398C2.91529 5.64398 3.73552 5.58639 4.51846 5.5288C4.854 5.5288 5.15227 5.4712 5.48781 5.4712C6.90456 5.35602 8.32132 5.24084 9.73807 5.12566C11.4531 5.01047 13.1308 4.8377 14.8458 4.72252C17.2692 4.49215 19.6926 4.31937 22.116 4.089C22.6752 4.03141 23.2345 4.03141 23.7937 3.97382C25.2105 3.85864 26.6272 3.74346 28.044 3.68587C29.4607 3.57068 30.8775 3.4555 32.2942 3.39791C32.8535 3.34032 33.4127 3.28273 33.972 3.28273C36.2089 3.16754 38.4832 3.05236 40.7202 2.93717C42.0996 2.87958 43.4791 2.82199 44.8959 2.70681C45.4551 2.70681 45.9771 2.64921 46.5363 2.64921C48.6987 2.59162 50.8984 2.53403 53.0608 2.47644C55.2232 2.41885 57.3483 2.36126 59.5107 2.30367C60.07 2.30367 60.6292 2.30367 61.2258 2.30367C62.6425 2.30367 64.022 2.30367 65.4387 2.30367C67.6384 2.30367 69.8008 2.30367 72.0005 2.24607C72.7089 2.24607 73.4173 2.24607 74.1257 2.24607C75.617 2.24607 77.1083 2.24607 78.5996 2.24607C78.7114 2.24607 78.8606 2.24607 78.9724 2.24607C75.617 2.30367 72.2242 2.36126 68.8688 2.47644C67.452 2.53403 66.0725 2.53403 64.6558 2.59162C64.0593 2.59162 63.4255 2.59162 62.8289 2.64921C60.7784 2.70681 58.7651 2.82199 56.7145 2.87958C54.3657 2.99476 52.0169 3.05236 49.6681 3.16754C49.2579 3.16754 48.8851 3.22513 48.475 3.22513C47.2074 3.34031 45.9771 3.39791 44.7094 3.51309C42.1742 3.68586 39.639 3.85864 37.1037 4.03141C36.7309 4.03141 36.3581 4.08901 35.9852 4.1466C34.7549 4.26178 33.4873 4.43456 32.2569 4.54974C30.02 4.78011 27.8203 5.01047 25.5833 5.24084C24.9868 5.29843 24.353 5.41361 23.7564 5.4712C22.3397 5.64398 20.9229 5.81675 19.5062 6.04712C17.7166 6.27749 15.8897 6.50786 14.1002 6.73822C11.6022 7.08377 9.06697 7.42932 6.56902 7.77487C5.97249 7.83246 5.37597 7.94765 4.74216 8.00524C3.66095 8.17801 2.57975 8.35079 1.49854 8.52356C1.38669 8.52356 1.27485 8.63875 1.27485 8.86911C1.27485 9.04189 1.38669 9.21467 1.49854 9.21467C1.90865 9.21467 2.28148 9.27225 2.6916 9.27225C2.65431 9.44503 2.61703 9.61781 2.61703 9.79058C2.61703 10.4241 2.95258 11 3.39997 11C6.49445 10.712 9.55165 10.3665 12.6461 10.1361C15.3305 9.96335 18.0149 9.73298 20.6992 9.56021C23.6446 9.32984 26.6272 9.09948 29.5726 8.92671C30.5046 8.86911 31.4367 8.81152 32.3688 8.69633C32.6298 8.69633 32.8908 8.63874 33.189 8.63874C37.9612 8.46597 42.7334 8.2356 47.5057 8.06283C50.0409 7.94764 52.6134 7.83246 55.1486 7.77487C56.0807 7.71728 56.9755 7.71728 57.9076 7.65969C62.7916 7.54451 67.6757 7.42932 72.5598 7.31414C74.6476 7.25655 76.7355 7.19895 78.8233 7.14136C80.6129 7.08377 82.4025 7.08377 84.192 6.96859C85.6834 6.91099 87.1374 6.79581 88.6287 6.73822C89.3744 6.68063 90.12 6.68063 90.8284 6.62304C92.5061 6.45026 94.1839 6.27749 95.8616 6.10471C95.7497 6.33508 95.7497 6.68063 95.787 6.911C95.8243 7.19895 95.9362 7.42932 96.1226 7.5445C96.2717 7.65969 96.4954 7.77487 96.6445 7.65969C97.0174 7.42932 97.3902 7.19895 97.7257 6.96859C97.7257 6.96859 97.7257 6.96859 97.6885 6.96859C97.7257 6.96859 97.7257 6.911 97.763 6.911C97.8003 6.911 97.8376 6.8534 97.8376 6.8534H97.8003C97.9867 6.73822 98.1731 6.62304 98.3968 6.45026C98.5833 6.33508 98.8069 6.1623 98.9934 6.04712C99.217 5.87435 99.4035 5.70157 99.6272 5.5288C99.8509 5.35602 100 4.89529 100 4.49215C100 4.26178 99.9627 4.08901 99.8882 3.85864C99.8136 3.62828 99.6272 3.34032 99.4408 3.28273C99.2543 3.22513 99.0679 3.16754 98.8815 3.16754C98.8442 3.16754 98.8069 3.16754 98.7697 3.16754C98.6578 3.16754 98.5087 3.16754 98.3968 3.22513C98.0986 3.28272 97.8376 3.34032 97.5393 3.34032C97.3156 3.34032 97.0919 3.39791 96.8309 3.39791C96.1971 3.4555 95.6006 3.51309 94.9668 3.62827C94.8177 3.62827 94.6313 3.68587 94.4821 3.68587C94.5567 3.57068 94.594 3.4555 94.594 3.34032C94.6312 3.22513 94.6313 3.10995 94.6313 2.99477C94.6313 2.93718 94.6313 2.82199 94.6685 2.7644C94.6685 2.64922 94.6685 2.53403 94.6313 2.47644C94.6313 2.47644 94.6685 2.47644 94.6685 2.41885C94.7804 2.30367 94.8922 2.18848 94.9668 1.95812C95.0414 1.78534 95.0786 1.55498 95.0786 1.32461C95.0786 1.09424 95.0414 0.921469 94.9668 0.691102C94.9295 0.633511 94.8922 0.518326 94.855 0.460735C94.7431 0.28796 94.6312 0.230368 94.5194 0.172777C94.2957 0.0575934 94.0347 0 93.7737 0C93.55 0 93.3636 0 93.1399 0C92.8417 0 92.5434 0 92.2451 0C91.835 0 91.3876 0 90.9775 0C89.859 0 88.7406 0 87.6221 0C86.5781 0 85.4969 0 84.453 0C83.4837 0 82.5516 0 81.5822 0C77.7421 0 73.9392 0.0575943 70.0991 0.115186C67.2283 0.172778 64.3575 0.230367 61.4867 0.230367C60.2191 0.230367 58.9142 0.287957 57.6466 0.345549C54.7758 0.460732 51.905 0.518324 49.0343 0.633507C48.214 0.633507 47.3938 0.691102 46.5736 0.691102C46.0889 0.691102 45.6415 0.748693 45.1568 0.748693C42.3233 0.921468 39.4898 1.09424 36.6563 1.26702C35.7988 1.32461 34.9413 1.3822 34.0465 1.43979C33.5618 1.43979 33.0772 1.49739 32.5925 1.55498C29.759 1.78534 26.9628 2.01571 24.1293 2.24607C22.6752 2.36126 21.2212 2.47644 19.7672 2.64921C17.2692 2.87958 14.7713 3.10995 12.3106 3.39791C10.1482 3.62827 7.98577 3.80105 5.82336 3.97382C5.48781 4.03141 5.15227 4.03141 4.77944 4.089C4.22019 4.1466 3.66095 4.1466 3.10171 4.20419C2.61703 4.26178 2.13235 4.26178 1.61039 4.26178C1.57311 4.08901 1.42398 3.91623 1.31213 3.91623C0.976581 3.97382 0.678319 4.03141 0.342772 4.089C0.193641 4.089 0.0445089 4.20419 0.00722599 4.49215C-0.0300569 4.78011 0.0817916 5.06806 0.230923 5.12566C0.380055 5.18325 0.491904 5.24084 0.641035 5.29843C0.790167 5.35602 0.902016 5.35602 1.05115 5.35602C1.42398 5.58639 1.75952 5.58639 2.13235 5.64398ZM89.3744 3.74346C89.8218 3.74346 90.3064 3.74346 90.7538 3.74346C90.7911 3.91623 90.8657 4.03142 90.9402 4.1466C90.7538 4.1466 90.5674 4.20419 90.4183 4.20419C90.0827 4.20419 89.7472 4.26178 89.4116 4.26178C87.9203 4.31937 86.4663 4.43456 84.975 4.49215C84.3039 4.54974 83.6328 4.60733 82.9617 4.60733C81.9178 4.60733 80.8366 4.66492 79.7927 4.66492C77.3693 4.72251 74.9832 4.78011 72.5598 4.8377C67.8621 4.95288 63.2018 5.06806 58.5041 5.18325C55.1486 5.24084 51.7932 5.41362 48.4377 5.58639C43.4045 5.81676 38.3341 5.98953 33.3009 6.2199C32.2942 6.27749 31.2876 6.33508 30.281 6.45026C27.4102 6.68063 24.5394 6.85341 21.6686 7.08377C18.8724 7.31414 16.0389 7.48691 13.2427 7.71728C12.9817 7.71728 12.7207 7.77487 12.4597 7.77487C13.2054 7.65969 13.9883 7.6021 14.734 7.48691C17.3811 7.19895 20.0281 6.8534 22.6752 6.56545C23.6073 6.45026 24.5394 6.33508 25.4342 6.2199C25.9188 6.16231 26.4035 6.10471 26.8509 6.10471C29.6099 5.87435 32.3688 5.58639 35.1277 5.35602C35.6497 5.29843 36.2089 5.24084 36.7309 5.18325C37.5138 5.12566 38.2968 5.06807 39.0424 5.06807C41.8759 4.89529 44.7094 4.72251 47.5429 4.54974C48.1768 4.49215 48.8106 4.49215 49.4071 4.43456C49.5562 4.43456 49.7426 4.43456 49.8918 4.43456C50.3392 4.43456 50.7866 4.43455 51.1967 4.37696C54.0302 4.26178 56.9009 4.20419 59.7344 4.089C61.0393 4.03141 62.3442 3.97382 63.6492 3.97382C67.8621 3.91623 72.1124 3.85864 76.3253 3.80105C80.762 3.80105 85.0868 3.80105 89.3744 3.74346Z"
        fill={fill}
      />
    </svg>
  );
};

export const LongDecorativeLinesIcon = ({
  strokeColor = "#E9E9E9",
  width = 512,
  height = 19,
  ...svgProps
}) => {
  return <svg width={width} height={height} {...svgProps} viewBox="0 0 512 19" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10.9176 9.74012C14.9263 9.74012 19.1258 9.64402 23.1345 9.54792C24.8525 9.54792 26.3796 9.45183 28.0976 9.45183C35.3514 9.25964 42.6051 9.06745 49.8589 8.87526C58.6398 8.68307 67.2297 8.39478 76.0106 8.20259C88.4184 7.81821 100.826 7.52992 113.234 7.14554C116.097 7.04944 118.961 7.04944 121.824 6.95335C129.078 6.76116 136.331 6.56897 143.585 6.47287C150.839 6.28068 158.093 6.08849 165.346 5.99239C168.21 5.8963 171.073 5.8002 173.936 5.8002C185.39 5.60801 197.034 5.41582 208.487 5.22363C215.55 5.12753 222.613 5.03144 229.867 4.83925C232.73 4.83925 235.403 4.74315 238.266 4.74315C249.337 4.64705 260.6 4.55096 271.671 4.45487C282.743 4.35877 293.624 4.26267 304.695 4.16658C307.558 4.16658 310.422 4.16658 313.476 4.16658C320.73 4.16658 327.793 4.16658 335.046 4.16658C346.309 4.16658 357.38 4.16658 368.643 4.07048C372.27 4.07048 375.896 4.07048 379.523 4.07048C387.159 4.07048 394.794 4.07048 402.43 4.07048C403.003 4.07048 403.766 4.07048 404.339 4.07048C387.159 4.16658 369.788 4.26267 352.608 4.45487C345.354 4.55096 338.291 4.55096 331.038 4.64705C327.983 4.64705 324.738 4.64705 321.684 4.74315C311.185 4.83924 300.877 5.03144 290.378 5.12753C278.352 5.31972 266.326 5.41582 254.3 5.60801C252.201 5.60801 250.292 5.7041 248.192 5.7041C241.702 5.89629 235.403 5.99239 228.912 6.18458C215.932 6.47287 202.951 6.76116 189.971 7.04944C188.062 7.04944 186.153 7.14554 184.244 7.24164C177.945 7.43383 171.455 7.72211 165.156 7.9143C153.702 8.29869 142.44 8.68307 130.987 9.06745C127.932 9.16354 124.687 9.35574 121.633 9.45183C114.379 9.74012 107.125 10.0284 99.8717 10.4128C90.709 10.7972 81.3555 11.1816 72.1928 11.5659C59.4033 12.1425 46.4229 12.7191 33.6334 13.2957C30.5792 13.3917 27.5249 13.5839 24.2798 13.68C18.7441 13.9683 13.2083 14.2566 7.67254 14.5449C7.09987 14.5449 6.52721 14.7371 6.52721 15.1215C6.52721 15.4098 7.09987 15.698 7.67254 15.698C9.77231 15.698 11.6812 15.7941 13.781 15.7941C13.5901 16.0824 13.3992 16.3707 13.3992 16.659C13.3992 17.7161 15.1172 18.677 17.4079 18.677C33.2516 18.1965 48.9045 17.62 64.7482 17.2356C78.4922 16.9473 92.2362 16.5629 105.98 16.2746C121.06 15.8902 136.331 15.5059 151.412 15.2176C156.184 15.1215 160.956 15.0254 165.728 14.8332C167.064 14.8332 168.401 14.7371 169.928 14.7371C194.362 14.4488 218.795 14.0644 243.229 13.7761C256.209 13.5839 269.381 13.3917 282.361 13.2957C287.133 13.1996 291.715 13.1996 296.487 13.1035C321.493 12.9113 346.5 12.7191 371.506 12.5269C382.196 12.4308 392.886 12.3347 403.575 12.2386C412.738 12.1425 421.901 12.1425 431.063 11.9503C438.699 11.8542 446.143 11.662 453.779 11.5659C457.597 11.4698 461.415 11.4698 465.041 11.3737C473.631 11.0855 482.221 10.7972 490.811 10.5089C490.239 10.8933 490.239 11.4698 490.43 11.8542C490.621 12.3347 491.193 12.7191 492.148 12.9113C492.911 13.1035 494.056 13.2957 494.82 13.1035C496.729 12.7191 498.638 12.3347 500.356 11.9503C500.356 11.9503 500.356 11.9503 500.165 11.9503C500.356 11.9503 500.356 11.8542 500.547 11.8542C500.738 11.8542 500.928 11.7581 500.928 11.7581H500.738C501.692 11.5659 502.646 11.3737 503.792 11.0855C504.746 10.8933 505.892 10.605 506.846 10.4128C507.991 10.1245 508.946 9.83621 510.091 9.54792C511.236 9.25964 512 8.49088 512 7.81821C512 7.43383 511.809 7.14554 511.427 6.76116C511.046 6.37678 510.091 5.8963 509.137 5.8002C508.182 5.70411 507.228 5.60801 506.273 5.60801C506.082 5.60801 505.892 5.60801 505.701 5.60801C505.128 5.60801 504.365 5.60801 503.792 5.7041C502.265 5.8002 500.928 5.8963 499.401 5.8963C498.256 5.8963 497.111 5.99239 495.774 5.99239C492.529 6.08849 489.475 6.18459 486.23 6.37678C485.466 6.37678 484.512 6.47287 483.749 6.47287C484.13 6.28068 484.321 6.08849 484.321 5.8963C484.512 5.70411 484.512 5.51192 484.512 5.31973C484.512 5.22363 484.512 5.03144 484.703 4.93534C484.703 4.74315 484.703 4.55096 484.512 4.45487C484.512 4.45487 484.703 4.45487 484.703 4.35877C485.276 4.16658 485.848 3.97439 486.23 3.59C486.612 3.30172 486.803 2.91734 486.803 2.53295C486.803 2.14857 486.612 1.86029 486.23 1.4759C486.039 1.37981 485.848 1.18762 485.657 1.09152C485.085 0.803235 484.512 0.707139 483.939 0.611043C482.794 0.418852 481.458 0.322754 480.122 0.322754C478.976 0.322754 478.022 0.322754 476.876 0.322754C475.349 0.322754 473.822 0.322754 472.295 0.322754C470.195 0.322754 467.905 0.322754 465.805 0.322754C460.078 0.322754 454.352 0.322754 448.625 0.322754C443.28 0.322754 437.744 0.322754 432.399 0.322754C427.436 0.322754 422.664 0.322754 417.701 0.322754C398.04 0.322754 378.569 0.418854 358.907 0.514949C344.209 0.611045 329.511 0.707138 314.812 0.707138C308.322 0.707138 301.641 0.80323 295.151 0.899326C280.452 1.09152 265.754 1.18761 251.055 1.3798C246.856 1.3798 242.656 1.4759 238.457 1.4759C235.975 1.4759 233.685 1.572 231.203 1.572C216.695 1.86029 202.188 2.14857 187.68 2.43686C183.29 2.53296 178.9 2.62905 174.318 2.72514C171.837 2.72514 169.355 2.82124 166.874 2.91734C152.366 3.30172 138.049 3.6861 123.542 4.07048C116.097 4.26267 108.653 4.45486 101.208 4.74315C88.4184 5.12753 75.6288 5.51192 63.0302 5.99239C51.9587 6.37678 40.8871 6.66506 29.8156 6.95335C28.0976 7.04944 26.3796 7.04944 24.4707 7.14554C21.6074 7.24163 18.7441 7.24164 15.8807 7.33773C13.3992 7.43383 10.9176 7.43383 8.2452 7.43383C8.05431 7.14554 7.29076 6.85725 6.71809 6.85725C5.0001 6.95335 3.47299 7.04944 1.75499 7.14554C0.991441 7.14554 0.227886 7.33773 0.0369971 7.81821C-0.153891 8.29869 0.418773 8.77916 1.18233 8.87526C1.94588 8.97135 2.51855 9.06745 3.2821 9.16355C4.04566 9.25964 4.61832 9.25964 5.38188 9.25964C7.29076 9.64402 9.00876 9.64402 10.9176 9.74012ZM457.597 6.56896C459.887 6.56896 462.369 6.56896 464.66 6.56896C464.851 6.85725 465.232 7.04945 465.614 7.24164C464.66 7.24164 463.705 7.33773 462.942 7.33773C461.224 7.33773 459.506 7.43383 457.788 7.43383C450.152 7.52992 442.707 7.72211 435.072 7.81821C431.636 7.91431 428.2 8.0104 424.764 8.0104C419.419 8.0104 413.883 8.10649 408.538 8.10649C396.131 8.20259 383.914 8.29869 371.506 8.39478C347.454 8.58697 323.593 8.77916 299.541 8.97135C282.361 9.06745 265.181 9.35574 248.001 9.64403C222.231 10.0284 196.27 10.3167 170.5 10.7011C165.346 10.7972 160.192 10.8933 155.038 11.0855C140.34 11.4698 125.642 11.7581 110.943 12.1425C96.6266 12.5269 82.119 12.8152 67.8024 13.1996C66.4662 13.1996 65.13 13.2957 63.7938 13.2957C67.6115 13.1035 71.6202 13.0074 75.438 12.8152C88.991 12.3347 102.544 11.7581 116.097 11.2776C120.869 11.0855 125.642 10.8933 130.223 10.7011C132.705 10.605 135.186 10.5089 137.477 10.5089C151.602 10.1245 165.728 9.64402 179.854 9.25964C182.526 9.16355 185.39 9.06745 188.062 8.97135C192.071 8.87526 196.079 8.77917 199.897 8.77917C214.405 8.49088 228.912 8.20259 243.42 7.9143C246.665 7.81821 249.91 7.81821 252.964 7.72212C253.728 7.72212 254.682 7.72212 255.446 7.72212C257.736 7.72212 260.027 7.72211 262.127 7.62601C276.634 7.43382 291.333 7.33773 305.84 7.14554C312.521 7.04944 319.203 6.95335 325.884 6.95335C347.454 6.85725 369.215 6.76115 390.786 6.66506C413.501 6.66506 435.645 6.66506 457.597 6.56896Z" fill={strokeColor} />
  </svg>

}

export const ArchitectureIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M27.6574 21.4387H26.7841V20.336C26.7841 17.7253 24.6602 15.6015 22.0495 15.6015H16.0752V11.5238H18.6943C19.986 11.5238 21.0369 10.4729 21.0369 9.18114V3.49474C21.0369 2.20304 19.986 1.1521 18.6943 1.1521H11.6967C10.405 1.1521 9.35408 2.20298 9.35408 3.49474V9.18108C9.35408 10.4728 10.405 11.5237 11.6967 11.5237H14.3158V15.6014H7.98949C5.37879 15.6014 3.25494 17.7253 3.25494 20.336V21.4386H2.34264C1.05088 21.4387 0 22.4895 0 23.7813V26.5051C0 27.7968 1.05088 28.8477 2.34264 28.8477H5.92658C7.21828 28.8477 8.26916 27.7968 8.26916 26.5051V23.7813C8.26916 22.4895 7.21828 21.4387 5.92658 21.4387H5.01434V20.336C5.01434 18.6956 6.34898 17.3609 7.98943 17.3609H14.3157V21.4387H13.4035C12.1117 21.4387 11.0609 22.4895 11.0609 23.7813V26.5051C11.0609 27.7968 12.1117 28.8477 13.4035 28.8477H16.9874C18.2791 28.8477 19.33 27.7968 19.33 26.5051V23.7813C19.33 22.4895 18.2791 21.4387 16.9874 21.4387H16.0752V17.3609H22.0496C23.6901 17.3609 25.0247 18.6956 25.0247 20.336V21.4387H24.0734C22.7817 21.4387 21.7308 22.4895 21.7308 23.7813V26.5051C21.7308 27.7968 22.7817 28.8477 24.0734 28.8477H27.6573C28.9491 28.8477 29.9999 27.7968 29.9999 26.5051V23.7813C30 22.4895 28.9491 21.4387 27.6574 21.4387Z"
        fill="white"
      />
    </svg>
  );
};

export const LifecycleIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 30 31"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M27.9188 25.4225L25.9772 24.1006C25.8216 23.9945 25.6377 23.9376 25.4494 23.9375H24.1012C22.7044 25.596 20.9197 26.8841 18.9056 27.6875H25.4494C25.6376 27.6877 25.8215 27.6311 25.9772 27.5253L27.9188 26.2025C27.9824 26.1593 28.0344 26.1011 28.0704 26.0331C28.1064 25.9652 28.1252 25.8894 28.1252 25.8125C28.1252 25.7356 28.1064 25.6598 28.0704 25.5919C28.0344 25.5239 27.9824 25.4657 27.9188 25.4225ZM3.75 16.2894L5.66812 14.645C6.08531 10.5238 9.48188 7.28 13.6631 7.08312L12.3009 5.49313C12.228 5.40839 12.1878 5.30027 12.1878 5.18844C12.1878 5.0766 12.228 4.96849 12.3009 4.88375L13.6275 3.3125C7.36219 3.53375 2.27156 8.49687 1.875 14.7162L3.75 16.2894Z"
        fill="white"
      />
      <path
        d="M13.2741 5.1875L14.9175 7.10562C19.0387 7.52281 22.2825 10.9203 22.4794 15.1016L24.0703 13.7384C24.155 13.6655 24.2632 13.6253 24.375 13.6253C24.4868 13.6253 24.595 13.6655 24.6797 13.7384L26.25 15.065C26.0287 8.79969 21.0656 3.70906 14.8472 3.3125L13.2741 5.1875ZM3.44531 17.2625L1.89375 15.9312C1.9875 18.5937 2.9325 21.0359 4.46906 22.9991H10.215C8.89704 22.3199 7.78139 21.3053 6.9805 20.0575C6.17961 18.8098 5.72177 17.373 5.65313 15.8919L4.05469 17.2606C3.96995 17.3336 3.86183 17.3738 3.75 17.3738C3.63817 17.3738 3.53005 17.3336 3.44531 17.2606V17.2625ZM14.8509 25.8125L13.2441 23.9375H2.8125C2.56386 23.9375 2.3254 24.0363 2.14959 24.2121C1.97377 24.3879 1.875 24.6264 1.875 24.875V26.75C1.875 26.9986 1.97377 27.2371 2.14959 27.4129C2.3254 27.5887 2.56386 27.6875 2.8125 27.6875H13.245L14.8509 25.8125Z"
        fill="white"
      />
      <path
        d="M24.375 14.7117L22.4569 16.3551C22.0397 20.4764 18.6422 23.7201 14.4609 23.917L15.8241 25.5079C15.897 25.5927 15.9372 25.7008 15.9372 25.8126C15.9372 25.9244 15.897 26.0326 15.8241 26.1173L14.4975 27.6876C20.7628 27.4654 25.8131 22.5032 26.2106 16.2848L24.375 14.7117Z"
        fill="white"
      />
    </svg>
  );
};
export const TeamIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M21.5423 19.16C21.3345 17.7481 20.6275 16.4575 19.5497 15.5223C18.4718 14.587 17.0943 14.0691 15.6673 14.0625H14.3373C12.9102 14.0691 11.5328 14.587 10.4549 15.5223C9.37703 16.4575 8.67007 17.7481 8.46229 19.16L7.82479 23.6175C7.80442 23.7616 7.81788 23.9084 7.86409 24.0464C7.9103 24.1844 7.988 24.3097 8.09104 24.4125C8.34104 24.6625 9.87479 25.9375 15.0035 25.9375C20.1323 25.9375 21.6623 24.6675 21.916 24.4125C22.0191 24.3097 22.0968 24.1844 22.143 24.0464C22.1892 23.9084 22.2027 23.7616 22.1823 23.6175L21.5423 19.16ZM9.45104 14.75C8.24295 15.8637 7.45691 17.3607 7.22604 18.9875L6.76354 22.1875C3.05104 22.1625 1.92604 20.8125 1.73854 20.5375C1.66606 20.4376 1.61434 20.3242 1.58643 20.204C1.55852 20.0837 1.55498 19.9591 1.57604 19.8375L1.85104 18.2875C1.99431 17.4774 2.33081 16.7139 2.832 16.0615C3.33319 15.4092 3.98429 14.8873 4.7301 14.5402C5.4759 14.1931 6.29441 14.0309 7.11624 14.0674C7.93807 14.1039 8.73897 14.3381 9.45104 14.75ZM28.426 19.8375C28.4471 19.9591 28.4436 20.0837 28.4156 20.204C28.3877 20.3242 28.336 20.4376 28.2635 20.5375C28.076 20.8125 26.951 22.1625 23.2385 22.1875L22.776 18.9875C22.5452 17.3607 21.7591 15.8637 20.551 14.75C21.2631 14.3381 22.064 14.1039 22.8858 14.0674C23.7077 14.0309 24.5262 14.1931 25.272 14.5402C26.0178 14.8873 26.6689 15.4092 27.1701 16.0615C27.6713 16.7139 28.0078 17.4774 28.151 18.2875L28.426 19.8375ZM9.66354 12C9.34888 12.4462 8.93117 12.81 8.44588 13.0602C7.96058 13.3105 7.42205 13.4399 6.87604 13.4375C6.33136 13.4375 5.79448 13.3081 5.30963 13.0599C4.82479 12.8117 4.40587 12.4518 4.08739 12.0099C3.76891 11.5681 3.55999 11.0569 3.47785 10.5184C3.39571 9.97996 3.4427 9.4297 3.61494 8.91297C3.78718 8.39624 4.07975 7.92784 4.46853 7.54637C4.85731 7.16489 5.33118 6.88127 5.85109 6.71886C6.37099 6.55646 6.92204 6.51992 7.45884 6.61227C7.99563 6.70461 8.50279 6.9232 8.93854 7.25C8.81321 7.74013 8.75021 8.24411 8.75104 8.75C8.75199 9.89644 9.06763 11.0206 9.66354 12ZM26.5635 10C26.5639 10.4515 26.4752 10.8987 26.3025 11.3159C26.1299 11.7331 25.8767 12.1121 25.5574 12.4314C25.2382 12.7507 24.8591 13.0039 24.4419 13.1765C24.0247 13.3491 23.5775 13.4378 23.126 13.4375C22.58 13.4399 22.0415 13.3105 21.5562 13.0602C21.0709 12.81 20.6532 12.4462 20.3385 12C20.9344 11.0206 21.2501 9.89644 21.251 8.75C21.2519 8.24411 21.1889 7.74013 21.0635 7.25C21.5742 6.86697 22.1815 6.63372 22.8173 6.57639C23.4531 6.51906 24.0923 6.63991 24.6633 6.92541C25.2343 7.2109 25.7145 7.64976 26.0501 8.1928C26.3858 8.73584 26.5635 9.36162 26.5635 10Z"
        fill="white"
      />
      <path
        d="M15 13.4375C17.5888 13.4375 19.6875 11.3388 19.6875 8.75C19.6875 6.16117 17.5888 4.0625 15 4.0625C12.4112 4.0625 10.3125 6.16117 10.3125 8.75C10.3125 11.3388 12.4112 13.4375 15 13.4375Z"
        fill="white"
      />
    </svg>
  );
};
export const DevelopmentIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_5095_47412)">
        <path
          d="M17.081 1.19315e-05C18.096 1.79939e-05 18.96 0.69404 19.1825 1.68654L19.7099 4.02736C20.5793 4.39755 21.3996 4.87414 22.1523 5.44532L24.4296 4.72657C25.4046 4.42159 26.4686 4.83857 26.9599 5.71095L29.0322 9.29591C29.5284 10.1834 29.361 11.2773 28.6298 11.9649L26.8652 13.5879C26.9711 14.5263 26.9711 15.4737 26.8652 16.4121L28.622 18.0274L28.6298 18.0352C29.361 18.7227 29.5292 19.8152 29.038 20.6914L26.9531 24.2979C26.4667 25.16 25.4061 25.577 24.4325 25.2734L22.1513 24.5537C21.3985 25.1246 20.5781 25.6012 19.7089 25.9717L19.1806 28.3135C19.0765 28.7924 18.8117 29.2214 18.4296 29.5283C18.0474 29.8352 17.5711 30.0018 17.081 30H12.9238C12.4336 30.0016 11.9575 29.8351 11.5751 29.5283C11.1928 29.2215 10.9268 28.7925 10.8222 28.3135L10.2949 25.9727C9.42546 25.6025 8.60521 25.1259 7.85247 24.5547L5.57513 25.2734C4.59773 25.5747 3.53492 25.1614 3.04485 24.2891L0.972588 20.7041C0.731134 20.2785 0.638874 19.7837 0.711846 19.2998C0.784827 18.8159 1.01867 18.3707 1.37493 18.0352L3.13958 16.4121C3.03329 15.4737 3.03313 14.5264 3.1386 13.5879L1.38177 11.9727C1.02425 11.6395 0.789316 11.1957 0.713799 10.7129C0.638322 10.2299 0.72678 9.73508 0.965752 9.30861L3.05071 5.70216C3.53576 4.83977 4.59726 4.42284 5.5722 4.72657L7.85247 5.44532C8.60538 4.87438 9.42561 4.39783 10.2949 4.02736L10.8222 1.68458C10.9271 1.2061 11.193 0.778193 11.5751 0.471692C11.9575 0.165089 12.4337 -0.00162438 12.9238 1.19315e-05H17.081ZM16.1425 10.0547C15.9057 10.0004 15.6572 10.0407 15.4492 10.166C15.241 10.2915 15.0898 10.4927 15.0273 10.7276L13.1523 18.8526C13.1207 18.99 13.1201 19.133 13.1513 19.2705C13.1826 19.4081 13.245 19.5372 13.3329 19.6475C13.4209 19.7576 13.5321 19.8472 13.6591 19.9082C13.7861 19.9693 13.9254 20.0001 14.0663 20C14.278 19.9997 14.4839 19.9279 14.6493 19.7959C14.8149 19.6638 14.9312 19.4789 14.9784 19.2725L16.8534 11.1475C16.8998 10.9089 16.8511 10.6618 16.7187 10.458C16.5862 10.2542 16.3795 10.1092 16.1425 10.0547ZM10.9824 11.2725C10.7395 11.2556 10.4998 11.3337 10.3134 11.4902L7.18841 14.3027C7.09103 14.391 7.01315 14.499 6.95989 14.6192C6.90672 14.7391 6.87888 14.8688 6.87884 15C6.87884 15.1314 6.90663 15.2617 6.95989 15.3818C7.01315 15.5019 7.09107 15.6101 7.18841 15.6983L10.3134 18.5108C10.4856 18.6654 10.709 18.7503 10.9404 18.75C11.1302 18.7502 11.3161 18.6934 11.4726 18.5859C11.629 18.4785 11.7493 18.3256 11.8173 18.1484C11.8851 17.9715 11.8977 17.778 11.8534 17.5938C11.809 17.4093 11.7095 17.2421 11.5683 17.1152L9.21673 15L11.5683 12.8858C11.7438 12.7169 11.8466 12.4856 11.8554 12.2422C11.864 11.999 11.7779 11.762 11.6152 11.5811C11.4523 11.4 11.2253 11.2894 10.9824 11.2725ZM19.0165 11.251C18.8935 11.2575 18.7722 11.2877 18.6611 11.3408C18.5499 11.3939 18.4505 11.469 18.3681 11.5606C18.2017 11.7455 18.1157 11.9889 18.1288 12.2373C18.1353 12.3602 18.1657 12.4807 18.2187 12.5918C18.2718 12.703 18.3468 12.8033 18.4384 12.8858L20.789 15L18.4384 17.1152C18.2965 17.2418 18.1961 17.409 18.1513 17.5938C18.1066 17.7784 18.1194 17.9721 18.1874 18.1494C18.2555 18.3269 18.3762 18.4796 18.5331 18.5869C18.6899 18.6941 18.8755 18.7509 19.0654 18.75C19.297 18.7504 19.521 18.6656 19.6933 18.5108L22.8183 15.6983C22.9157 15.61 22.9935 15.502 23.0468 15.3818C23.1 15.2617 23.1269 15.1314 23.1269 15C23.1268 14.8688 23.0999 14.7391 23.0468 14.6192C22.9936 14.4991 22.9156 14.391 22.8183 14.3027L19.6933 11.4902C19.5084 11.3239 19.2649 11.238 19.0165 11.251Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_5095_47412">
          <rect width={30} height={30} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
export const KeyIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_5095_47432)">
        <path
          d="M29.6739 9.46148C29.6739 8.18416 29.4228 6.94631 28.9312 5.77663C28.454 4.65001 27.7723 3.6382 26.904 2.76991C26.0357 1.90162 25.0239 1.21991 23.8973 0.74271C22.7312 0.24757 21.4898 0 20.2125 0C18.9351 0 17.6973 0.251158 16.5276 0.74271C15.401 1.21991 14.3892 1.90162 13.5209 2.76991C12.6526 3.6382 11.9709 4.65001 11.4937 5.77663C10.9986 6.94273 10.751 8.18416 10.751 9.46148C10.751 11.0581 11.1528 12.6225 11.9171 14.0146L0.589849 25.3311C0.327927 25.593 0.245404 25.9913 0.406863 26.368C0.439155 26.4469 0.492974 26.5187 0.55397 26.5797L3.09067 29.1164C3.15166 29.1774 3.22342 29.2276 3.30236 29.2635C3.67909 29.425 4.07736 29.346 4.33928 29.0805L5.90722 27.5125L8.16406 29.7694C8.22505 29.8304 8.29681 29.8806 8.37571 29.9165C8.75245 30.0779 9.15075 29.999 9.41264 29.7335L11.9135 27.2327C11.9744 27.1717 12.0247 27.0999 12.0606 27.021C12.222 26.6443 12.1431 26.246 11.8776 25.9841L9.65662 23.7631L15.6557 17.7605C17.0443 18.5247 18.6122 18.9265 20.2088 18.9265C21.4862 18.9265 22.724 18.6754 23.8937 18.1838C25.0203 17.7066 26.0321 17.0249 26.9004 16.1566C27.7687 15.2884 28.4504 14.2766 28.9276 13.1499C29.4228 11.9766 29.6739 10.7388 29.6739 9.46148ZM23.5278 12.7804C22.6416 13.6666 21.4611 14.1545 20.2089 14.1545C18.9567 14.1545 17.7762 13.6666 16.89 12.7804C16.0038 11.8941 15.5123 10.7137 15.5123 9.46148C15.5123 8.20569 16.0002 7.02884 16.8865 6.14261C17.7727 5.25638 18.9532 4.76841 20.2053 4.76841C21.4575 4.76841 22.638 5.25638 23.5242 6.14261C24.4104 7.02884 24.8984 8.20928 24.8984 9.46148C24.9055 10.7137 24.4176 11.8941 23.5278 12.7804Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_5095_47432">
          <rect width={30} height={30} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const SearchIcon = (svgProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        d="M11.5 21C16.7467 21 21 16.7467 21 11.5C21 6.25329 16.7467 2 11.5 2C6.25329 2 2 6.25329 2 11.5C2 16.7467 6.25329 21 11.5 21Z"
        stroke="#212125"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22 22L20 20"
        stroke="#212125"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const EmailIcon = ({ strokeColor = "white", ...svgProps }) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...svgProps}
  >
    <path
      d="M17 20.5H7C4 20.5 2 19 2 15.5V8.5C2 5 4 3.5 7 3.5H17C20 3.5 22 5 22 8.5V15.5C22 19 20 20.5 17 20.5Z"
      stroke={strokeColor}
      strokeWidth="1.5"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 9L13.87 11.5C12.84 12.32 11.15 12.32 10.12 11.5L7 9"
      stroke={strokeColor}
      strokeWidth="1.5"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const OutlineEmailIcon = (svgProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 30 30"
      fill="none"
      {...svgProps}
    >
      <path
        d="M27.5 7.5C27.5 6.125 26.375 5 25 5H5C3.625 5 2.5 6.125 2.5 7.5M27.5 7.5V22.5C27.5 23.875 26.375 25 25 25H5C3.625 25 2.5 23.875 2.5 22.5V7.5M27.5 7.5L15 16.25L2.5 7.5"
        stroke="#F56B6B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const SendIcon = (svgProps) => {
  return (
    <svg
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M19 5L5 19"
        stroke="white"
        strokeWidth="1.5"
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19 15.27V5H8.73"
        stroke="white"
        strokeWidth="1.5"
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const InstagramIcon = (svgProps) => {
  return (
    <svg width="20" height="20" {...svgProps} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10 0C12.717 0 13.056 0.00999994 14.122 0.0599999C15.187 0.11 15.912 0.277 16.55 0.525C17.21 0.779 17.766 1.123 18.322 1.678C18.8305 2.1779 19.224 2.78259 19.475 3.45C19.722 4.087 19.89 4.813 19.94 5.878C19.987 6.944 20 7.283 20 10C20 12.717 19.99 13.056 19.94 14.122C19.89 15.187 19.722 15.912 19.475 16.55C19.2247 17.2178 18.8311 17.8226 18.322 18.322C17.822 18.8303 17.2173 19.2238 16.55 19.475C15.913 19.722 15.187 19.89 14.122 19.94C13.056 19.987 12.717 20 10 20C7.283 20 6.944 19.99 5.878 19.94C4.813 19.89 4.088 19.722 3.45 19.475C2.78233 19.2245 2.17753 18.8309 1.678 18.322C1.16941 17.8222 0.775931 17.2175 0.525 16.55C0.277 15.913 0.11 15.187 0.0599999 14.122C0.0129999 13.056 0 12.717 0 10C0 7.283 0.00999994 6.944 0.0599999 5.878C0.11 4.812 0.277 4.088 0.525 3.45C0.775236 2.78218 1.1688 2.17732 1.678 1.678C2.17767 1.16923 2.78243 0.775729 3.45 0.525C4.088 0.277 4.812 0.11 5.878 0.0599999C6.944 0.0129999 7.283 0 10 0ZM10 5C8.67392 5 7.40215 5.52678 6.46447 6.46447C5.52678 7.40215 5 8.67392 5 10C5 11.3261 5.52678 12.5979 6.46447 13.5355C7.40215 14.4732 8.67392 15 10 15C11.3261 15 12.5979 14.4732 13.5355 13.5355C14.4732 12.5979 15 11.3261 15 10C15 8.67392 14.4732 7.40215 13.5355 6.46447C12.5979 5.52678 11.3261 5 10 5ZM16.5 4.75C16.5 4.41848 16.3683 4.10054 16.1339 3.86612C15.8995 3.6317 15.5815 3.5 15.25 3.5C14.9185 3.5 14.6005 3.6317 14.3661 3.86612C14.1317 4.10054 14 4.41848 14 4.75C14 5.08152 14.1317 5.39946 14.3661 5.63388C14.6005 5.8683 14.9185 6 15.25 6C15.5815 6 15.8995 5.8683 16.1339 5.63388C16.3683 5.39946 16.5 5.08152 16.5 4.75ZM10 7C10.7956 7 11.5587 7.31607 12.1213 7.87868C12.6839 8.44129 13 9.20435 13 10C13 10.7956 12.6839 11.5587 12.1213 12.1213C11.5587 12.6839 10.7956 13 10 13C9.20435 13 8.44129 12.6839 7.87868 12.1213C7.31607 11.5587 7 10.7956 7 10C7 9.20435 7.31607 8.44129 7.87868 7.87868C8.44129 7.31607 9.20435 7 10 7Z" fill="#977FCB" />
    </svg>

  );
};

export const LinkedinIcon = ({
  fill = "#977FCB",
  ...svgProps
}) => {
  return (
    <svg
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M7.15182 20.445H3.54349V8.98334H7.15182V20.445ZM5.34516 7.39501C4.93585 7.39271 4.53641 7.26921 4.19722 7.0401C3.85804 6.81099 3.59433 6.48654 3.43937 6.1077C3.2844 5.72886 3.24514 5.3126 3.32652 4.91146C3.4079 4.51033 3.60629 4.14229 3.89664 3.85379C4.187 3.5653 4.5563 3.36928 4.95796 3.29047C5.35961 3.21166 5.7756 3.2536 6.15344 3.411C6.53128 3.56839 6.85403 3.83418 7.08095 4.17482C7.30788 4.51547 7.42881 4.9157 7.42849 5.32501C7.43235 5.59904 7.381 5.87105 7.2775 6.12481C7.174 6.37858 7.02047 6.60891 6.82606 6.80208C6.63165 6.99524 6.40034 7.14729 6.14591 7.24916C5.89149 7.35103 5.61916 7.40063 5.34516 7.39501ZM20.4518 20.455H16.8452V14.1933C16.8452 12.3467 16.0602 11.7767 15.0468 11.7767C13.9768 11.7767 12.9268 12.5833 12.9268 14.24V20.455H9.31849V8.99168H12.7885V10.58H12.8352C13.1835 9.87501 14.4035 8.67001 16.2652 8.67001C18.2785 8.67001 20.4535 9.86501 20.4535 13.365L20.4518 20.455Z"
        fill={fill}
      />
    </svg>
  );
};

export const FillLinkedinIcon = (svgProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
      viewBox="0 0 41 40"
      fill="none"
    >
      <path
        d="M37.6644 0H3.56997C2.78684 0 2.03579 0.311096 1.48204 0.864849C0.928283 1.4186 0.617188 2.16965 0.617188 2.95278V37.0472C0.617188 37.8303 0.928283 38.5814 1.48204 39.1352C2.03579 39.6889 2.78684 40 3.56997 40H37.6644C38.4475 40 39.1986 39.6889 39.7523 39.1352C40.3061 38.5814 40.6172 37.8303 40.6172 37.0472V2.95278C40.6172 2.16965 40.3061 1.4186 39.7523 0.864849C39.1986 0.311096 38.4475 0 37.6644 0ZM12.5394 34.075H6.52552V14.9722H12.5394V34.075ZM9.5283 12.325C8.84613 12.3212 8.18038 12.1153 7.61508 11.7335C7.04978 11.3516 6.61025 10.8109 6.35198 10.1795C6.09371 9.54808 6.02827 8.85432 6.16391 8.18576C6.29955 7.5172 6.63019 6.90379 7.11411 6.42297C7.59803 5.94214 8.21355 5.61544 8.88297 5.4841C9.55238 5.35276 10.2457 5.42266 10.8754 5.68498C11.5052 5.9473 12.0431 6.39028 12.4213 6.95802C12.7995 7.52576 13.0011 8.19282 13.0005 8.875C13.007 9.33172 12.9214 9.78506 12.7489 10.208C12.5764 10.6309 12.3205 11.0148 11.9965 11.3368C11.6725 11.6587 11.2869 11.9121 10.8629 12.0819C10.4389 12.2517 9.98497 12.3344 9.5283 12.325ZM34.7061 34.0917H28.695V23.6556C28.695 20.5778 27.3866 19.6278 25.6977 19.6278C23.9144 19.6278 22.1644 20.9722 22.1644 23.7333V34.0917H16.1505V14.9861H21.9339V17.6333H22.0116C22.5922 16.4583 24.6255 14.45 27.7283 14.45C31.0839 14.45 34.7089 16.4417 34.7089 22.275L34.7061 34.0917Z"
        fill="#0A66C2"
      />
    </svg>
  );
};

export const OutlineLinkedInIcon = (svgProps) => {
  return (
    <svg
      {...svgProps}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 30 30"
      fill="none"
    >
      <path
        d="M20 10C21.9891 10 23.8968 10.7902 25.3033 12.1967C26.7098 13.6032 27.5 15.5109 27.5 17.5V26.25H22.5V17.5C22.5 16.837 22.2366 16.2011 21.7678 15.7322C21.2989 15.2634 20.663 15 20 15C19.337 15 18.7011 15.2634 18.2322 15.7322C17.7634 16.2011 17.5 16.837 17.5 17.5V26.25H12.5V17.5C12.5 15.5109 13.2902 13.6032 14.6967 12.1967C16.1032 10.7902 18.0109 10 20 10Z"
        stroke="#1275B1"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.5 11.25H2.5V26.25H7.5V11.25Z"
        stroke="#1275B1"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5 7.5C6.38071 7.5 7.5 6.38071 7.5 5C7.5 3.61929 6.38071 2.5 5 2.5C3.61929 2.5 2.5 3.61929 2.5 5C2.5 6.38071 3.61929 7.5 5 7.5Z"
        stroke="#1275B1"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PintrestIcon = (svgProps) => {
  return (
    <svg
      width={23}
      height={23}
      viewBox="0 0 23 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_1432_1712)">
        <path
          d="M11.5691 22.6678C17.612 22.6678 22.5108 17.769 22.5108 11.7261C22.5108 5.68318 17.612 0.784424 11.5691 0.784424C5.5262 0.784424 0.627441 5.68318 0.627441 11.7261C0.627441 17.769 5.5262 22.6678 11.5691 22.6678Z"
          fill="none"
        />
        <path
          d="M11.5691 0.784424C5.52643 0.784424 0.627441 5.68341 0.627441 11.7261C0.627441 16.3639 3.5092 20.3264 7.57969 21.9203C7.48063 21.0558 7.39958 19.723 7.61571 18.7774C7.81383 17.9219 8.89449 13.3381 8.89449 13.3381C8.89449 13.3381 8.57029 12.6807 8.57029 11.7171C8.57029 10.1952 9.45283 9.06048 10.5515 9.06048C11.4881 9.06048 11.9383 9.76291 11.9383 10.6004C11.9383 11.537 11.344 12.9418 11.0288 14.2476C10.7676 15.3373 11.5781 16.2289 12.6498 16.2289C14.595 16.2289 16.0899 14.1756 16.0899 11.2218C16.0899 8.6012 14.2077 6.77308 11.5151 6.77308C8.39919 6.77308 6.57107 9.1055 6.57107 11.519C6.57107 12.4555 6.93129 13.4642 7.38157 14.0135C7.47162 14.1216 7.48063 14.2206 7.45361 14.3287C7.37256 14.6709 7.18344 15.4184 7.14742 15.5715C7.10239 15.7696 6.98532 15.8146 6.7782 15.7155C5.40936 15.0761 4.55384 13.0859 4.55384 11.4739C4.55384 8.02484 7.05737 4.85491 11.7853 4.85491C15.5766 4.85491 18.5304 7.55656 18.5304 11.1768C18.5304 14.9501 16.1529 17.9849 12.8569 17.9849C11.7492 17.9849 10.7046 17.4086 10.3534 16.7242C10.3534 16.7242 9.80404 18.8134 9.66896 19.3267C9.42581 20.2813 8.75941 21.4701 8.30913 22.1995C9.33576 22.5147 10.4164 22.6858 11.5511 22.6858C17.5938 22.6858 22.4928 17.7868 22.4928 11.7441C22.5108 5.68341 17.6118 0.784424 11.5691 0.784424Z"
          fill="#35B729"
        />
      </g>
      <defs>
        <clipPath id="clip0_1432_1712">
          <rect
            width="21.8834"
            height="21.8834"
            fill="white"
            transform="translate(0.627441 0.784424)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const TwitterIcon = ({
  fill = "#977FCB",
  ...svgProps
}) => {
  return (
    <svg
      width={23}
      height={24}
      viewBox="0 0 23 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_1432_1707)">
        <path
          d="M19.8423 19.8981C17.6204 16.7302 15.3994 13.5615 13.1726 10.3973C13.0791 10.2644 13.0936 10.1952 13.1916 10.083C14.8932 8.12952 16.591 6.17275 18.2895 4.21635C18.5724 3.89028 18.8551 3.56385 19.1422 3.23226C19.0756 3.18478 19.0176 3.20429 18.9634 3.20392C18.5956 3.20171 18.2274 3.20944 17.8599 3.19914C17.7315 3.19546 17.6497 3.24183 17.5652 3.33972C15.8888 5.27625 14.2075 7.20799 12.5336 9.14673C12.4138 9.2851 12.3745 9.25529 12.2827 9.12428C10.9307 7.18959 9.57312 5.25932 8.22075 3.325C8.15277 3.22784 8.08513 3.19987 7.97885 3.20024C6.40391 3.20319 4.82862 3.20208 3.25333 3.20355C3.2133 3.20355 3.16119 3.17558 3.13048 3.23999C3.57701 3.87703 4.02355 4.51444 4.47042 5.15149C6.15579 7.5543 7.84082 9.95748 9.52826 12.3584C9.59624 12.4552 9.61349 12.5104 9.52377 12.6131C7.59478 14.8264 5.67061 17.0445 3.74576 19.2614C3.5256 19.515 3.30716 19.7704 3.0625 20.0545C3.50903 20.0545 3.91519 20.046 4.32032 20.0589C4.47836 20.0637 4.58327 20.0122 4.68955 19.8889C5.86075 18.5313 7.03747 17.1788 8.21247 15.8248C8.89952 15.0332 9.58623 14.242 10.2809 13.4415C10.3109 13.4824 10.3288 13.5059 10.3458 13.5302C11.8393 15.6585 13.3335 17.786 14.8235 19.9169C14.898 20.0232 14.9733 20.0571 15.0937 20.0567C16.6631 20.0523 18.2325 20.0541 19.8023 20.0527C19.8392 20.0527 19.881 20.0714 19.9241 20.0287C19.8972 19.985 19.8717 19.9397 19.8423 19.8974V19.8981ZM15.5347 18.8908C15.444 18.8912 15.4198 18.8191 15.3815 18.7646C14.5692 17.6083 13.7586 16.4505 12.9473 15.2934C10.7909 12.219 8.6345 9.14526 6.4781 6.07081C6.08609 5.51215 5.69477 4.95313 5.30379 4.39521C5.33899 4.33227 5.39006 4.36135 5.43009 4.36135C6.09679 4.35914 6.76348 4.36282 7.43052 4.3573C7.53956 4.35656 7.60754 4.39079 7.67311 4.48463C10.4144 8.39928 13.1581 12.3121 15.9019 16.2252C16.5172 17.1026 17.1331 17.9792 17.7719 18.8894C17.003 18.8894 16.269 18.8879 15.5351 18.8912L15.5347 18.8908Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_1432_1707">
          <rect
            width="16.8616"
            height="16.8616"
            fill="white"
            transform="translate(3.0625 3.19751)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};


export const FacebookIcon = (svgProps) => {
  return <svg width="21" height="20" {...svgProps} viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10.0142 -0.000976562C4.49116 -0.000976562 0.0141602 4.47602 0.0141602 9.99902C0.0141602 14.814 3.45917 18.888 8.01517 19.804L8.01416 12.999H6.01416V9.99902H8.01416V6.99902C8.02516 5.36502 9.31516 4.01802 11.0142 3.99902H14.0142V6.99902C14.0142 6.99902 12.3102 6.99902 12.0142 6.99902C11.4952 6.99802 11.0142 7.42902 11.0142 7.99902V9.99902H14.0142L13.0142 12.999H11.0142L11.0212 19.961C16.0532 19.439 20.0142 15.169 20.0142 9.99902C20.0142 4.47602 15.5372 -0.000976562 10.0142 -0.000976562Z" fill="#977FCB" />
  </svg>

}

export const DribbleIcon = (svgProps) => {
  return (
    <svg
      width={25}
      height={24}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M19.3174 4.79004C16.7174 9.34004 11.7074 11.9501 6.48738 11.4801L3.31738 11.1901"
        stroke="#35B729"
        strokeWidth="1.5"
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.31738 19.37C8.91738 14.82 13.9274 12.21 19.1474 12.68L22.3174 12.97"
        stroke="#35B729"
        strokeWidth="1.5"
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.8174 22C18.3402 22 22.8174 17.5228 22.8174 12C22.8174 6.47715 18.3402 2 12.8174 2C7.29453 2 2.81738 6.47715 2.81738 12C2.81738 17.5228 7.29453 22 12.8174 22Z"
        stroke="#35B729"
        strokeWidth="1.5"
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.44727 3L11.6273 6.95C13.7573 9.59 15.2673 12.66 16.0673 15.95L17.2773 20.94"
        stroke="#35B729"
        strokeWidth="1.5"
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const BubleIcon = (svgProps) => {
  return (
    <svg
      width={21}
      height={12}
      viewBox="0 0 21 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_1432_1741)">
        <path
          d="M3.68979e-05 5.99233C0.0114935 2.6667 2.68089 -0.0115234 5.97658 3.72861e-05C9.25699 0.011598 11.9111 2.71295 11.8997 6.02316C11.8882 9.33722 9.21117 12.0116 5.91929 11.9962C2.64652 11.9846 -0.0114197 9.28713 3.68979e-05 5.99233Z"
          fill="#35B729"
        />
        <path
          d="M18.4492 6.33903C18.4148 7.59914 18.1513 8.8554 17.5785 9.97294C17.3494 10.42 17.0668 10.8477 16.6849 11.1753C16.1426 11.6377 15.4056 11.8381 14.7334 11.5568C13.5801 11.0789 12.9768 9.68392 12.6751 8.55097C12.5147 7.94211 12.4192 7.32169 12.3772 6.69355C12.2855 5.32939 12.4497 3.87274 12.9538 2.59336C13.3319 1.62612 14.1224 0.400683 15.2413 0.292783C15.8524 0.231126 16.4367 0.550972 16.8605 0.974864C17.2501 1.36022 17.5289 1.84577 17.7465 2.35059C18.1972 3.39105 18.4072 4.524 18.4492 5.65694C18.4568 5.88045 18.4568 6.10781 18.4492 6.33517V6.33903Z"
          fill="#35B729"
        />
        <path
          d="M21.0002 5.73807C20.9964 6.73999 20.9659 7.47602 20.8895 8.21205C20.8169 8.9057 20.7215 9.59163 20.5038 10.2583C20.435 10.4664 20.3358 10.6629 20.2403 10.8595C20.2059 10.9288 20.1524 10.9866 20.0952 11.0367C19.9577 11.1639 19.8126 11.1677 19.6789 11.0329C19.6025 10.9558 19.5376 10.8672 19.488 10.7747C19.3428 10.5126 19.255 10.2275 19.1825 9.9423C19.0297 9.345 18.9457 8.73614 18.8846 8.12342C18.7929 7.17545 18.7662 6.22362 18.7967 5.26793C18.8311 4.23518 18.9151 3.20627 19.1481 2.19664C19.2168 1.89221 19.3008 1.58778 19.4421 1.31032C19.5032 1.19086 19.5796 1.07525 19.6713 0.975061C19.8049 0.828626 19.9691 0.828626 20.1028 0.975061C20.1983 1.08296 20.2823 1.21013 20.3472 1.3373C20.5191 1.69182 20.6107 2.07333 20.6871 2.45868C20.8169 3.10223 20.8971 3.75348 20.9353 4.41244C20.9659 4.94038 20.985 5.47217 20.9964 5.73807H21.0002Z"
          fill="#35B729"
        />
      </g>
      <defs>
        <clipPath id="clip0_1432_1741">
          <rect width={21} height={12} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const BeIcon = (svgProps) => {
  return (
    <svg
      width={25}
      height={24}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M2.18262 12H9.18262C10.8826 12 12.1826 13.3 12.1826 15C12.1826 16.7 10.8826 18 9.18262 18H3.18262C2.58262 18 2.18262 17.6 2.18262 17V7C2.18262 6.4 2.58262 6 3.18262 6H8.18262C9.88262 6 11.1826 7.3 11.1826 9C11.1826 10.7 9.88262 12 8.18262 12H2.18262Z"
        stroke="#35B729"
        strokeWidth="1.5"
        strokeMiterlimit={10}
      />
      <path
        d="M14.1826 14H22.1826C22.1826 11.8 20.3826 10 18.1826 10C15.9826 10 14.1826 11.8 14.1826 14ZM14.1826 14C14.1826 16.2 15.9826 18 18.1826 18H19.8826"
        stroke="#35B729"
        strokeWidth="1.5"
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.6826 7.5H16.6826"
        stroke="#35B729"
        strokeWidth="1.5"
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PhoneIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M21.97 18.33C21.97 18.69 21.89 19.06 21.72 19.42C21.55 19.78 21.33 20.12 21.04 20.44C20.55 20.98 20.01 21.37 19.4 21.62C18.8 21.87 18.15 22 17.45 22C16.43 22 15.34 21.76 14.19 21.27C13.04 20.78 11.89 20.12 10.75 19.29C9.6 18.45 8.51 17.52 7.47 16.49C6.44 15.45 5.51 14.36 4.68 13.22C3.86 12.08 3.2 10.94 2.72 9.81C2.24 8.67 2 7.58 2 6.54C2 5.86 2.12 5.21 2.36 4.61C2.6 4 2.98 3.44 3.51 2.94C4.15 2.31 4.85 2 5.59 2C5.87 2 6.15 2.06 6.4 2.18C6.66 2.3 6.89 2.48 7.07 2.74L9.39 6.01C9.57 6.26 9.7 6.49 9.79 6.71C9.88 6.92 9.93 7.13 9.93 7.32C9.93 7.56 9.86 7.8 9.72 8.03C9.59 8.26 9.4 8.5 9.16 8.74L8.4 9.53C8.29 9.64 8.24 9.77 8.24 9.93C8.24 10.01 8.25 10.08 8.27 10.16C8.3 10.24 8.33 10.3 8.35 10.36C8.53 10.69 8.84 11.12 9.28 11.64C9.73 12.16 10.21 12.69 10.73 13.22C11.27 13.75 11.79 14.24 12.32 14.69C12.84 15.13 13.27 15.43 13.61 15.61C13.66 15.63 13.72 15.66 13.79 15.69C13.87 15.72 13.95 15.73 14.04 15.73C14.21 15.73 14.34 15.67 14.45 15.56L15.21 14.81C15.46 14.56 15.7 14.37 15.93 14.25C16.16 14.11 16.39 14.04 16.64 14.04C16.83 14.04 17.03 14.08 17.25 14.17C17.47 14.26 17.7 14.39 17.95 14.56L21.26 16.91C21.52 17.09 21.7 17.3 21.81 17.55C21.91 17.8 21.97 18.05 21.97 18.33Z"
        stroke="white"
        strokeWidth="1.5"
        strokeMiterlimit={10}
      />
    </svg>
  );
};

export const StarIcon = ({
  fill = "#FFCF55",
  ...svgProps
}) => {
  return (
    <>
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
        <path d="M10.6767 4.31208C11.0932 3.03009 12.9069 3.03009 13.3235 4.31208L14.4993 7.93108C14.6856 8.5044 15.2199 8.89257 15.8227 8.89257H19.628C20.9759 8.89257 21.5364 10.6175 20.4459 11.4098L17.3674 13.6465C16.8797 14.0008 16.6756 14.6289 16.8619 15.2022L18.0378 18.8212C18.4543 20.1032 16.987 21.1692 15.8965 20.3769L12.818 18.1403C12.3303 17.7859 11.6699 17.7859 11.1822 18.1403L8.10367 20.3769C7.01315 21.1692 5.54585 20.1032 5.96239 18.8212L7.13828 15.2022C7.32456 14.6289 7.12049 14.0008 6.63279 13.6465L3.55429 11.4098C2.46376 10.6175 3.02421 8.89257 4.37218 8.89257H8.17742C8.78025 8.89257 9.31452 8.5044 9.50081 7.93108L10.6767 4.31208Z" fill={fill} />
      </svg>

    </>
  );
};

export const NextArrowIcon = (svgProps) => {
  return (
    <svg
      width={21}
      height={20}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_5095_41108)">
        <path
          d="M4.41797 9.99984H16.0846M16.0846 9.99984L10.2513 4.1665M16.0846 9.99984L10.2513 15.8332"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_5095_41108">
          <rect
            width={20}
            height={20}
            fill="white"
            transform="translate(0.25)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const ExploreArrowIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M7 17L17 7M17 7H7M17 7V17"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PlayIcon2 = (svgProps) => {
  return (
    <svg
      width={40}
      height={41}
      viewBox="0 0 40 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx={20} cy="20.7109" r="19.5" stroke="white" />
      <path
        d="M13.9766 15.5371C13.9763 15.3536 14.0122 15.1723 14.082 15.0039L14.1631 14.8398C14.255 14.6811 14.3776 14.5431 14.5225 14.4326L14.6748 14.3311L14.6816 14.3271C14.8471 14.2293 15.0297 14.1652 15.2188 14.1387L15.4102 14.125C15.6022 14.1236 15.792 14.1604 15.9688 14.2324L16.1416 14.3164L16.1436 14.3174L25.3105 19.4893L25.3135 19.4912C25.4767 19.5816 25.6199 19.7038 25.7344 19.8496L25.8389 20.0029C25.965 20.2172 26.0322 20.4613 26.0322 20.71C26.0322 20.8964 25.9948 21.0804 25.9229 21.251L25.8389 21.417C25.7442 21.5776 25.6183 21.7167 25.4697 21.8271L25.3135 21.9277L25.3105 21.9297L16.1436 27.1016L16.1416 27.1035C15.919 27.2306 15.6665 27.2958 15.4102 27.2939L15.2188 27.2803C15.0295 27.2537 14.8462 27.1897 14.6807 27.0918H14.6816L14.6748 27.0879L14.5225 26.9873C14.4258 26.9136 14.3389 26.8277 14.2646 26.7314L14.1631 26.5791C14.0405 26.3673 13.9762 26.1265 13.9766 25.8818V15.5371Z"
        stroke="white"
      />
    </svg>
  );
};

export const RightArrowIcon2 = (svgProps) => {
  return (
    <svg
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M25.9872 17.2011L15.2284 27.9598L13.4609 26.1923L24.2184 15.4336L14.7372 15.4336V12.9336L28.4872 12.9336V26.6836H25.9872V17.2011Z"
        fill="white"
      />
    </svg>
  );
};

export const FillStarIcon = (svgProps) => {
  return (
    <svg
      {...svgProps}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_i_6933_7606)">
        <path
          d="M9.75668 1.58313L11.0767 4.24466C11.2567 4.61516 11.7367 4.97053 12.1417 5.03858L14.5342 5.43932C16.0642 5.6964 16.4242 6.81544 15.3217 7.91937L13.4617 9.79453C13.1467 10.1121 12.9742 10.7246 13.0717 11.1631L13.6042 13.4844C14.0242 15.3217 13.0567 16.0325 11.4442 15.0722L9.20168 13.7339C8.79668 13.4919 8.12918 13.4919 7.71668 13.7339L5.47416 15.0722C3.86916 16.0325 2.89416 15.3142 3.31416 13.4844L3.84666 11.1631C3.94416 10.7246 3.77166 10.1121 3.45666 9.79453L1.59666 7.91937C0.50166 6.81544 0.85416 5.6964 2.38416 5.43932L4.77666 5.03858C5.17416 4.97053 5.65416 4.61516 5.83416 4.24466L7.15418 1.58313C7.87418 0.138955 9.04418 0.138955 9.75668 1.58313Z"
          fill="url(#paint0_radial_6933_7606)"
        />
      </g>
      <defs>
        <filter
          id="filter0_i_6933_7606"
          x="-0.0400391"
          y="-1.5"
          width="16.002"
          height={17}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx={-1} dy={-2} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="arithmetic" k2={-1} k3={1} />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 0.611765 0 0 0 0 0 0 0 0 1 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_6933_7606"
          />
        </filter>
        <radialGradient
          id="paint0_radial_6933_7606"
          cx={0}
          cy={0}
          r={1}
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(5.71192 4.70763) rotate(56.0366) scale(13.0124 13.0803)"
        >
          <stop stopColor="#FDF55A" />
          <stop offset="0.16" stopColor="#FCE715" />
          <stop offset="0.36" stopColor="#FED201" />
          <stop offset="0.56" stopColor="#FFCC00" />
          <stop offset="0.705" stopColor="#F7C203" />
          <stop offset="0.865" stopColor="#FFBE00" />
          <stop offset="0.93" stopColor="#FFB400" />
          <stop offset={1} stopColor="#FE9900" />
        </radialGradient>
      </defs>
    </svg>
  );
};

export const HalfFillStarIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M8.5 0.75C8.61374 0.75 8.9478 0.848061 9.29883 1.55957L9.2998 1.56055L10.793 4.57031V4.57129C10.9615 4.9181 11.2416 5.21115 11.5273 5.42383C11.8135 5.63668 12.1739 5.82046 12.5508 5.88379L15.2617 6.33789C15.9962 6.46131 16.196 6.74473 16.2363 6.87109C16.2767 6.99786 16.2787 7.34756 15.748 7.87891L15.7461 7.88086L13.6377 10.0059C13.3477 10.2984 13.1572 10.6851 13.0527 11.0527C12.9482 11.4211 12.9074 11.8492 12.9961 12.248L12.9971 12.2529L13.6006 14.8838C13.8242 15.8627 13.5843 16.1583 13.5273 16.2002C13.4724 16.2401 13.1244 16.3813 12.2676 15.8711H12.2686L9.72656 14.3545H9.72754C9.35363 14.1311 8.90728 14.043 8.50391 14.043C8.09998 14.043 7.65532 14.1316 7.28027 14.3516L7.27539 14.3545L4.7334 15.8711C3.88285 16.38 3.5337 16.2386 3.47754 16.1973C3.41751 16.153 3.17862 15.8545 3.40137 14.8838L4.00488 12.2529L4.00586 12.248C4.09454 11.8492 4.0538 11.4211 3.94922 11.0527C3.84479 10.6851 3.65432 10.2984 3.36426 10.0059L1.25586 7.88086C0.728867 7.34957 0.728874 6.99878 0.769531 6.87012C0.804364 6.76015 0.959367 6.52919 1.48926 6.39062L1.74023 6.33789L4.45117 5.88379L4.4541 5.88281C4.82751 5.81888 5.18504 5.63492 5.46875 5.42285C5.75115 5.21167 6.02863 4.92059 6.19727 4.57617L6.19824 4.57715L7.69336 1.5625C8.04863 0.84989 8.38489 0.750004 8.5 0.75Z"
        stroke="#FFCB00"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g filter="url(#filter0_i_7160_33056)">
        <path
          d="M5.11804 16.5152L7.65957 14.9984C7.91136 14.8507 8.24698 14.7826 8.57481 14.794V0.00267335C8.01583 -0.0372995 7.44911 0.370993 7.02207 1.22755L5.52605 4.24395C5.32205 4.66385 4.77805 5.0666 4.32755 5.14372L1.61605 5.5979C-0.117954 5.88925 -0.517454 7.1575 0.723546 8.40862L2.83155 10.5338C3.18855 10.8937 3.38405 11.5878 3.27355 12.0848L2.67005 14.7156C2.19405 16.7894 3.29905 17.6035 5.11804 16.5152Z"
          fill="url(#paint0_radial_7160_33056)"
        />
      </g>
      <defs>
        <filter
          id="filter0_i_7160_33056"
          x="-0.998047"
          y={-2}
          width="9.57324"
          height="18.9984"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx={-1} dy={-2} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="arithmetic" k2={-1} k3={1} />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 0.611765 0 0 0 0 0 0 0 0 1 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_7160_33056"
          />
        </filter>
        <radialGradient
          id="paint0_radial_7160_33056"
          cx={0}
          cy={0}
          r={1}
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(5.48969 5.05698) rotate(54.3496) scale(13.7532 9.08638)"
        >
          <stop stopColor="#FDF55A" />
          <stop offset="0.16" stopColor="#FCE715" />
          <stop offset="0.36" stopColor="#FED201" />
          <stop offset="0.56" stopColor="#FFCC00" />
          <stop offset="0.705" stopColor="#F7C203" />
          <stop offset="0.865" stopColor="#FFBE00" />
          <stop offset="0.93" stopColor="#FFB400" />
          <stop offset={1} stopColor="#FE9900" />
        </radialGradient>
      </defs>
    </svg>
  );
};

export const OutlineStarIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 19 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M10.9696 2.22755L12.4656 5.24395C12.6696 5.66385 13.2136 6.0666 13.6726 6.14372L16.3841 6.5979C18.1181 6.88925 18.5261 8.1575 17.2766 9.40862L15.1686 11.5338C14.8116 11.8937 14.6161 12.5878 14.7266 13.0848L15.3301 15.7156C15.8061 17.798 14.7096 18.6035 12.8821 17.5152L10.3406 15.9984C9.88162 15.7242 9.12512 15.7242 8.65762 15.9984L6.11609 17.5152C4.29709 18.6035 3.19209 17.7894 3.66809 15.7156L4.27159 13.0848C4.38209 12.5878 4.18659 11.8937 3.82959 11.5338L1.72159 9.40862C0.480593 8.1575 0.880093 6.88925 2.61409 6.5979L5.32559 6.14372C5.77609 6.0666 6.32009 5.66385 6.52409 5.24395L8.02012 2.22755C8.83612 0.590816 10.1621 0.590816 10.9696 2.22755Z"
        stroke="#FFCB00"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const CloseIcon = ({
  size = 16,
  stroke = "#fff",
  ...svgProps
}) => {
  return <svg {...svgProps} stroke={stroke} fill={stroke} strokeWidth="0" viewBox="0 0 24 24" height={size} width={size} xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"></path></svg>
}

export const VisionIcon = ({
  fill = "#35B729",
  ...svgProps
}) => {
  return (
    <svg
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_6933_7852)">
        <path
          d="M49.8169 24.2891C47.3754 19.8703 43.8378 16.1541 39.5444 13.4981C35.1722 10.8025 30.1369 9.375 25.0005 9.375C19.8641 9.375 14.8288 10.8025 10.4565 13.4981C6.16318 16.1541 2.62558 19.8703 0.184088 24.2891C0.0633536 24.5066 0 24.7513 0 25.0001C0 25.2488 0.0633536 25.4935 0.184088 25.711C2.62558 30.1299 6.16318 33.846 10.4565 36.502C14.8288 39.1976 19.8641 40.6251 25.0005 40.6251C30.1369 40.6251 35.1722 39.1976 39.5444 36.502C43.8378 33.846 47.3754 30.1299 49.8169 25.711C49.9376 25.4935 50.001 25.2488 50.001 25.0001C50.001 24.7513 49.9376 24.5066 49.8169 24.2891ZM25.0005 37.6954C16.1284 37.6954 7.83057 32.8516 3.15479 25.0001C7.83057 17.1485 16.1284 12.3047 25.0005 12.3047C33.8726 12.3047 42.1704 17.1485 46.8462 25.0001C42.1704 32.8516 33.8726 37.6954 25.0005 37.6954Z"
          fill={fill}
        />
        <path
          d="M25.0004 16.6992C23.3587 16.6992 21.7538 17.1861 20.3887 18.0982C19.0237 19.0103 17.9597 20.3067 17.3315 21.8234C16.7032 23.3402 16.5388 25.0092 16.8591 26.6194C17.1794 28.2296 17.97 29.7087 19.1309 30.8695C20.2917 32.0304 21.7708 32.821 23.381 33.1413C24.9912 33.4616 26.6602 33.2972 28.177 32.6689C29.6937 32.0407 30.9901 30.9767 31.9022 29.6117C32.8143 28.2466 33.3012 26.6417 33.3012 25C33.2986 22.7993 32.4232 20.6895 30.8671 19.1333C29.3109 17.5772 27.2011 16.7018 25.0004 16.6992ZM25.0004 30.3711C23.9381 30.3711 22.8996 30.0561 22.0164 29.4659C21.1331 28.8757 20.4447 28.0369 20.0381 27.0554C19.6316 26.074 19.5253 24.994 19.7325 23.9522C19.9397 22.9103 20.4513 21.9532 21.2025 21.2021C21.9536 20.4509 22.9107 19.9394 23.9525 19.7321C24.9944 19.5249 26.0744 19.6312 27.0558 20.0378C28.0373 20.4443 28.8761 21.1327 29.4663 22.016C30.0565 22.8993 30.3715 23.9377 30.3715 25C30.3699 26.424 29.8036 27.7893 28.7966 28.7962C27.7897 29.8032 26.4244 30.3695 25.0004 30.3711ZM25.0004 6.83594C25.1928 6.83594 25.3832 6.79805 25.561 6.72443C25.7387 6.65082 25.9002 6.54292 26.0362 6.4069C26.1722 6.27087 26.2801 6.10939 26.3537 5.93166C26.4273 5.75394 26.4652 5.56346 26.4652 5.37109V1.46484C26.4652 1.07634 26.3109 0.703754 26.0362 0.429043C25.7615 0.154331 25.3889 0 25.0004 0C24.6119 0 24.2393 0.154331 23.9646 0.429043C23.6899 0.703754 23.5355 1.07634 23.5355 1.46484V5.37109C23.5355 5.56346 23.5734 5.75394 23.6471 5.93166C23.7207 6.10939 23.8286 6.27087 23.9646 6.4069C24.2393 6.68161 24.6119 6.83594 25.0004 6.83594ZM13.9242 7.97949C14.098 8.32707 14.4028 8.59138 14.7714 8.71426C15.1401 8.83715 15.5425 8.80855 15.89 8.63477C16.2376 8.46098 16.5019 8.15623 16.6248 7.78757C16.7477 7.4189 16.7191 7.01652 16.5453 6.66895L14.5922 2.7627C14.5061 2.59059 14.387 2.43712 14.2417 2.31105C14.0963 2.18498 13.9275 2.08877 13.745 2.02792C13.5624 1.96708 13.3697 1.94278 13.1778 1.95642C12.9858 1.97006 12.7985 2.02137 12.6264 2.10742C12.4543 2.19347 12.3008 2.31258 12.1747 2.45794C12.0487 2.60331 11.9524 2.77208 11.8916 2.95462C11.8308 3.13716 11.8065 3.3299 11.8201 3.52184C11.8337 3.71377 11.885 3.90114 11.9711 4.07324L13.9242 7.97949ZM34.1107 8.63477C34.2828 8.72087 34.4702 8.77222 34.6622 8.78589C34.8541 8.79956 35.0469 8.77527 35.2294 8.71442C35.412 8.65357 35.5808 8.55734 35.7261 8.43124C35.8715 8.30514 35.9906 8.15163 36.0766 7.97949L38.0297 4.07324C38.1157 3.90114 38.1671 3.71377 38.1807 3.52184C38.1943 3.3299 38.17 3.13716 38.1092 2.95462C38.0483 2.77208 37.9521 2.60331 37.8261 2.45794C37.7 2.31258 37.5465 2.19347 37.3744 2.10742C37.2023 2.02137 37.0149 1.97006 36.823 1.95642C36.6311 1.94278 36.4383 1.96708 36.2558 2.02792C36.0733 2.08877 35.9045 2.18498 35.7591 2.31105C35.6138 2.43712 35.4946 2.59059 35.4086 2.7627L33.4555 6.66895C33.3694 6.84104 33.318 7.02841 33.3043 7.22036C33.2907 7.4123 33.315 7.60506 33.3758 7.78762C33.4367 7.97018 33.5329 8.13895 33.659 8.28431C33.7851 8.42967 33.9386 8.54875 34.1107 8.63477ZM25.0004 43.1641C24.6119 43.1641 24.2393 43.3184 23.9646 43.5931C23.6899 43.8678 23.5355 44.2404 23.5355 44.6289V48.5352C23.5355 48.9237 23.6899 49.2962 23.9646 49.571C24.2393 49.8457 24.6119 50 25.0004 50C25.3889 50 25.7615 49.8457 26.0362 49.571C26.3109 49.2962 26.4652 48.9237 26.4652 48.5352V44.6289C26.4652 44.2404 26.3109 43.8678 26.0362 43.5931C25.7615 43.3184 25.3889 43.1641 25.0004 43.1641ZM15.89 41.3652C15.7179 41.2791 15.5306 41.2278 15.3386 41.2141C15.1467 41.2004 14.9539 41.2247 14.7714 41.2856C14.5888 41.3464 14.42 41.4427 14.2747 41.5688C14.1293 41.6949 14.0102 41.8484 13.9242 42.0205L11.9711 45.9268C11.7973 46.2743 11.7687 46.6767 11.8916 47.0454C12.0145 47.414 12.2788 47.7188 12.6264 47.8926C12.9739 48.0664 13.3763 48.095 13.745 47.9721C14.1137 47.8492 14.4184 47.5849 14.5922 47.2373L16.5453 43.3311C16.6314 43.159 16.6828 42.9716 16.6964 42.7796C16.7101 42.5877 16.6858 42.3949 16.625 42.2124C16.5641 42.0298 16.4679 41.861 16.3418 41.7157C16.2157 41.5703 16.0622 41.4512 15.89 41.3652ZM36.0766 42.0205C35.9905 41.8484 35.8714 41.6949 35.726 41.5689C35.5807 41.4428 35.4119 41.3466 35.2294 41.2857C35.0468 41.2249 34.8541 41.2006 34.6621 41.2142C34.4702 41.2279 34.2828 41.2792 34.1107 41.3652C33.9386 41.4513 33.7852 41.5704 33.6591 41.7158C33.533 41.8611 33.4368 42.0299 33.376 42.2124C33.3151 42.395 33.2908 42.5877 33.3045 42.7797C33.3181 42.9716 33.3694 43.159 33.4555 43.3311L35.4086 47.2373C35.5824 47.5849 35.8871 47.8492 36.2558 47.9721C36.6245 48.095 37.0268 48.0664 37.3744 47.8926C37.722 47.7188 37.9863 47.414 38.1092 47.0454C38.2321 46.6767 38.2035 46.2743 38.0297 45.9268L36.0766 42.0205Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_6933_7852">
          <rect width={50} height={50} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const MissionIcon = ({
  fill = "#35B729",
  ...svgProps
}) => {
  return (
    <svg
      width={51}
      height={50}
      viewBox="0 0 51 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g clipPath="url(#clip0_6933_7842)">
        <path
          d="M26.0651 17.4441C26.7533 17.4441 27.3106 16.8865 27.3106 16.1986C27.3106 15.5108 26.7533 14.9531 26.0651 14.9531C20.5625 14.9531 16.1016 19.4141 16.1016 24.9171C16.1016 30.4198 20.5625 34.8808 26.0651 34.8808C31.5683 34.8808 36.0292 30.4198 36.0292 24.9171C36.0292 24.229 35.4715 23.6717 34.7837 23.6717C34.0959 23.6717 33.5382 24.229 33.5382 24.9171C33.5382 29.0442 30.1927 32.3901 26.0651 32.3901C21.9381 32.3901 18.5922 29.0442 18.5922 24.9171C18.5922 20.7896 21.9381 17.4441 26.0651 17.4441Z"
          fill={fill}
        />
        <path
          d="M49.828 17.519C49.6116 16.8655 48.9067 16.5112 48.2528 16.7274C47.6143 16.9387 47.2587 17.6185 47.4491 18.2635C51.1737 30.1247 44.5781 42.7592 32.7171 46.4839C20.8565 50.2085 8.22184 43.613 4.49724 31.752C0.77215 19.891 7.36813 7.25634 19.2288 3.53164C23.6192 2.15301 28.3267 2.15301 32.7171 3.53164C33.3763 3.73037 34.0717 3.35732 34.2705 2.69815C34.4662 2.04883 34.1072 1.3626 33.4621 1.15284C20.2919 -2.97928 6.2653 4.34765 2.13328 17.5182C-1.99884 30.6884 5.32839 44.7151 18.4987 48.8471C31.6693 52.9792 45.6955 45.6523 49.8275 32.4817C51.3556 27.6112 51.3557 22.3896 49.8279 17.519H49.828Z"
          fill={fill}
        />
        <path
          d="M28.4375 8.89482C28.4439 8.18564 27.8992 7.59287 27.192 7.53984C26.82 7.5082 26.4478 7.47656 26.0658 7.47656C16.4356 7.47656 8.62891 15.2833 8.62891 24.9135C8.62891 34.5434 16.4356 42.3501 26.0658 42.3501C35.6957 42.3501 43.5024 34.5434 43.5024 24.9136C43.5024 24.463 43.475 24.0129 43.4197 23.5658C43.3201 22.8822 42.6854 22.4088 42.0018 22.5084C41.3182 22.6075 40.8447 23.2428 40.9439 23.9264C40.9478 23.9522 40.9523 23.9777 40.9576 24.0037C40.9848 24.3055 41.0114 24.6045 41.0114 24.9128C41.0118 33.1674 34.3205 39.8587 26.0662 39.8595C17.8116 39.8599 11.1202 33.1681 11.1199 24.9139C11.1195 16.6597 17.8104 9.96787 26.0651 9.96758H26.0654C26.3745 9.96758 26.6738 9.99424 26.9749 10.0209L27.2083 10.0404C27.8592 10.0683 28.4092 9.56318 28.4367 8.9124C28.4371 8.90625 28.4371 8.90059 28.4375 8.89482Z"
          fill={fill}
        />
        <path
          d="M33.5384 11.2181V15.6844L25.1849 24.0378C24.6901 24.5157 24.6765 25.3043 25.1544 25.799C25.6324 26.2938 26.421 26.3075 26.9156 25.8296C26.926 25.8196 26.9362 25.8094 26.9462 25.799L35.2997 17.4456H39.7658C40.0962 17.4456 40.4128 17.3144 40.6467 17.0806L48.1193 9.60752C48.6057 9.12119 48.6057 8.33272 48.1193 7.84629C48.0037 7.73067 47.8664 7.63895 47.7154 7.57639C47.5643 7.51382 47.4024 7.48163 47.2389 7.48164H43.5023V3.74512C43.5023 3.05732 42.9447 2.49961 42.2564 2.5C41.9264 2.50008 41.6098 2.63124 41.3765 2.86465L33.9034 10.3377C33.7877 10.4532 33.6959 10.5905 33.6332 10.7415C33.5706 10.8926 33.5384 11.0545 33.5384 11.2181ZM36.0294 11.7339L41.0113 6.75186V8.72715C41.0113 9.41494 41.569 9.97266 42.2568 9.97266H44.2321L39.2501 14.9546H36.0294V11.7339Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_6933_7842">
          <rect
            width={50}
            height={50}
            fill="white"
            transform="translate(0.980469)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const DiamondIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 23 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <g filter="url(#filter0_d_6933_8117)">
        <path
          d="M11.9062 6C12.9348 8.77975 15.1265 10.9714 17.9062 12C15.1265 13.0286 12.9348 15.2203 11.9062 18C10.8777 15.2203 8.686 13.0286 5.90625 12C8.686 10.9714 10.8777 8.77975 11.9062 6Z"
          fill="#fff"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_6933_8117"
          x="0.831055"
          y="0.924805"
          width="22.1504"
          height="22.1504"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="2.5376" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_6933_8117"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_6933_8117"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

export const SeeHistoryIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 136 136"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M124.447 92.3474C124.869 92.4593 125.255 92.3976 125.604 92.1622C125.952 91.9299 126.224 91.5584 126.42 91.0477C126.557 90.6887 126.621 90.3599 126.611 90.0616C126.601 89.7632 126.529 89.5103 126.396 89.303C126.262 89.0957 126.077 88.9449 125.84 88.8505C125.642 88.7747 125.453 88.7538 125.273 88.7878C125.092 88.825 124.92 88.9029 124.757 89.0217C124.598 89.1416 124.447 89.2845 124.306 89.4503C124.164 89.6161 124.033 89.7877 123.912 89.9649L123.347 90.771C123.126 91.098 122.881 91.3999 122.612 91.6765C122.342 91.9563 122.048 92.1862 121.73 92.3664C121.412 92.5496 121.07 92.6599 120.704 92.6973C120.338 92.7347 119.95 92.6746 119.538 92.517C118.981 92.3037 118.545 91.9734 118.23 91.5263C117.918 91.0803 117.746 90.5424 117.714 89.9126C117.683 89.287 117.814 88.5936 118.106 87.8321C118.389 87.0924 118.749 86.494 119.187 86.0369C119.624 85.583 120.12 85.2869 120.677 85.1488C121.233 85.0138 121.831 85.0548 122.472 85.2718L121.823 86.9664C121.482 86.8644 121.168 86.861 120.879 86.9564C120.591 87.0518 120.337 87.2226 120.119 87.469C119.899 87.7185 119.72 88.0227 119.583 88.3818C119.439 88.7563 119.369 89.1057 119.373 89.4301C119.379 89.7587 119.451 90.042 119.591 90.28C119.733 90.5192 119.94 90.6924 120.211 90.7998C120.46 90.8916 120.692 90.8971 120.908 90.8163C121.126 90.7367 121.339 90.5893 121.546 90.3738C121.754 90.1627 121.967 89.9035 122.184 89.5962L122.877 88.6211C123.378 87.915 123.92 87.4127 124.503 87.1142C125.088 86.8199 125.72 86.8032 126.401 87.0639C126.962 87.2785 127.394 87.618 127.699 88.0825C128.002 88.5501 128.17 89.0915 128.2 89.7066C128.234 90.3228 128.123 90.9637 127.869 91.6292C127.61 92.3039 127.266 92.8501 126.837 93.2676C126.409 93.6894 125.932 93.9664 125.406 94.0985C124.883 94.2319 124.352 94.2006 123.813 94.0049L124.447 92.3474Z"
        fill="#35B729"
      />
      <path
        d="M113.392 97.0465C113.796 96.3965 114.281 95.9192 114.845 95.6145C115.411 95.3144 116.021 95.1927 116.677 95.2494C117.335 95.3079 118.004 95.5481 118.682 95.9701C119.349 96.3851 119.855 96.876 120.201 97.4427C120.549 98.014 120.72 98.6165 120.716 99.2503C120.715 99.8859 120.522 100.513 120.136 101.132C119.888 101.532 119.588 101.869 119.238 102.143C118.889 102.421 118.496 102.614 118.059 102.72C117.62 102.83 117.145 102.835 116.633 102.735C116.124 102.637 115.585 102.411 115.017 102.057L114.548 101.766L117.855 96.4513L118.885 97.0922L116.49 100.942C116.784 101.121 117.084 101.22 117.389 101.238C117.697 101.258 117.986 101.197 118.256 101.057C118.525 100.92 118.751 100.705 118.933 100.413C119.127 100.1 119.222 99.7787 119.217 99.4477C119.215 99.1185 119.133 98.8116 118.97 98.527C118.809 98.247 118.584 98.0154 118.296 97.8322L117.397 97.2727C117.02 97.038 116.653 96.9056 116.297 96.8754C115.944 96.8469 115.619 96.9118 115.321 97.0702C115.026 97.2303 114.776 97.4763 114.569 97.8084C114.431 98.0307 114.336 98.2511 114.286 98.4698C114.239 98.6901 114.236 98.905 114.278 99.1143C114.319 99.3236 114.41 99.5189 114.551 99.7003L113.503 101.027C113.182 100.702 112.959 100.326 112.836 99.8976C112.713 99.4738 112.696 99.0181 112.785 98.5307C112.876 98.045 113.078 97.5503 113.392 97.0465Z"
        fill="#35B729"
      />
      <path
        d="M107.282 104.874C107.799 104.309 108.363 103.929 108.974 103.733C109.585 103.542 110.207 103.535 110.841 103.711C111.478 103.89 112.091 104.249 112.68 104.788C113.259 105.318 113.667 105.894 113.903 106.515C114.139 107.14 114.197 107.764 114.077 108.386C113.959 109.011 113.654 109.592 113.161 110.13C112.843 110.477 112.487 110.753 112.093 110.958C111.699 111.167 111.277 111.284 110.827 111.309C110.376 111.336 109.908 111.253 109.423 111.061C108.941 110.871 108.453 110.55 107.959 110.098L107.552 109.725L111.779 105.109L112.674 105.928L109.612 109.272C109.868 109.503 110.145 109.655 110.441 109.728C110.74 109.804 111.035 109.798 111.327 109.711C111.617 109.625 111.878 109.455 112.111 109.201C112.359 108.93 112.511 108.631 112.567 108.305C112.626 107.981 112.601 107.664 112.494 107.354C112.387 107.049 112.208 106.78 111.959 106.547L111.178 105.832C110.85 105.532 110.514 105.335 110.17 105.24C109.828 105.147 109.496 105.151 109.174 105.252C108.855 105.355 108.564 105.551 108.3 105.839C108.123 106.032 107.99 106.232 107.9 106.437C107.813 106.645 107.771 106.856 107.773 107.069C107.775 107.283 107.829 107.492 107.934 107.696L106.66 108.807C106.404 108.429 106.255 108.018 106.212 107.574C106.169 107.135 106.236 106.684 106.413 106.221C106.592 105.761 106.882 105.312 107.282 104.874Z"
        fill="#35B729"
      />
      <path
        d="M91.7803 122.374C91.2516 121.413 90.9787 120.49 90.9618 119.607C90.9436 118.728 91.1439 117.937 91.5628 117.233C91.9787 116.532 92.5773 115.966 93.3585 115.536C94.1397 115.107 94.9394 114.903 95.7576 114.926C96.5746 114.953 97.3508 115.209 98.0863 115.693C98.8205 116.182 99.4512 116.906 99.9783 117.864C100.507 118.825 100.781 119.745 100.799 120.624C100.816 121.508 100.616 122.3 100.2 123.002C99.7814 123.705 99.1813 124.272 98.4002 124.701C97.619 125.131 96.8207 125.334 96.0054 125.309C95.1872 125.287 94.4109 125.031 93.6767 124.542C92.9412 124.058 92.3091 123.335 91.7803 122.374ZM93.4051 121.48C93.7773 122.157 94.1972 122.669 94.6648 123.017C95.1311 123.369 95.6156 123.563 96.1183 123.596C96.6226 123.633 97.1173 123.518 97.6022 123.251C98.0872 122.984 98.4492 122.628 98.6884 122.182C98.9291 121.74 99.0268 121.226 98.9816 120.642C98.9351 120.063 98.7258 119.434 98.3535 118.758C97.9813 118.081 97.5621 117.567 97.0958 117.214C96.6282 116.866 96.1423 116.674 95.638 116.637C95.1352 116.604 94.6414 116.72 94.1564 116.987C93.6715 117.254 93.3086 117.608 93.0679 118.051C92.8288 118.496 92.7325 119.009 92.779 119.589C92.8242 120.173 93.0329 120.803 93.4051 121.48Z"
        fill="#35B729"
      />
      <path
        d="M82.8519 123.281L84.158 127.509L82.4385 128.04L80.1843 120.744L81.8516 120.229L82.2522 121.525L82.3282 121.502C82.3667 121.043 82.5342 120.625 82.8308 120.249C83.1242 119.874 83.5449 119.601 84.0927 119.432C84.5709 119.284 85.0264 119.26 85.4593 119.359C85.89 119.461 86.2714 119.685 86.6034 120.03C86.9365 120.378 87.193 120.844 87.373 121.426L88.8083 126.072L87.0888 126.603L85.7357 122.224C85.5928 121.761 85.3527 121.433 85.0152 121.239C84.6777 121.045 84.3031 121.011 83.8915 121.139C83.6381 121.217 83.4118 121.354 83.2124 121.551C83.0131 121.748 82.8751 121.992 82.7984 122.283C82.7196 122.578 82.7374 122.911 82.8519 123.281Z"
        fill="#35B729"
      />
      <path
        d="M76.8197 121.277L77.835 128.846L76.1055 129.078L75.9363 127.816L75.8575 127.827C75.7781 128.282 75.5867 128.651 75.2834 128.932C74.9772 129.217 74.6073 129.389 74.1737 129.447C74.0752 129.46 73.9645 129.47 73.8416 129.477C73.7159 129.487 73.6109 129.489 73.5265 129.484L73.3064 127.843C73.3855 127.859 73.5085 127.866 73.6754 127.864C73.8394 127.865 73.997 127.856 74.1481 127.835C74.4733 127.792 74.7562 127.682 74.9968 127.506C75.2345 127.333 75.4107 127.114 75.5255 126.848C75.6403 126.582 75.6761 126.288 75.6329 125.966L75.036 121.516L76.8197 121.277Z"
        fill="#35B729"
      />
      <path
        d="M56.711 131.171L54.902 130.897L55.9717 123.819C56.0731 123.17 56.2951 122.632 56.638 122.205C56.9803 121.78 57.4147 121.479 57.9411 121.301C58.4638 121.125 59.0479 121.086 59.6935 121.183C60.2834 121.272 60.7985 121.457 61.2387 121.738C61.6751 122.022 61.9995 122.393 62.2119 122.851C62.4243 123.309 62.4837 123.847 62.3901 124.467L60.5762 124.193C60.614 123.92 60.5904 123.677 60.5055 123.463C60.4172 123.248 60.2778 123.073 60.0871 122.937C59.8965 122.801 59.6668 122.713 59.3981 122.672C59.1064 122.628 58.8499 122.651 58.6283 122.742C58.4063 122.836 58.2246 122.996 58.0831 123.223C57.9383 123.449 57.8375 123.739 57.7807 124.092L56.711 131.171Z"
        fill="#35B729"
      />
      <path
        d="M50.0695 118.735C50.7753 118.975 51.3341 119.339 51.7458 119.826C52.1576 120.313 52.411 120.885 52.506 121.54C52.598 122.195 52.5167 122.896 52.2621 123.642C52.0075 124.389 51.6433 124.995 51.1695 125.46C50.6926 125.924 50.1424 126.223 49.5189 126.357C48.8954 126.492 48.2307 126.438 47.5249 126.198C46.819 125.957 46.2602 125.593 45.8485 125.106C45.4368 124.619 45.1855 124.047 45.0946 123.389C45.0006 122.73 45.0809 122.027 45.3355 121.28C45.59 120.534 45.9553 119.93 46.4311 119.469C46.9039 119.007 47.452 118.709 48.0755 118.575C48.699 118.441 49.3637 118.494 50.0695 118.735ZM49.5948 120.096C49.2121 119.965 48.8563 119.961 48.5274 120.084C48.1974 120.21 47.9087 120.43 47.6613 120.745C47.4107 121.058 47.2106 121.435 47.0608 121.874C46.91 122.316 46.8379 122.738 46.8446 123.14C46.8471 123.543 46.9406 123.896 47.1251 124.197C47.3096 124.498 47.5932 124.713 47.9759 124.844C48.3681 124.978 48.7307 124.982 49.0638 124.858C49.3938 124.732 49.6846 124.511 49.9362 124.194C50.1836 123.879 50.3827 123.5 50.5335 123.058C50.6833 122.619 50.7564 122.199 50.7528 121.799C50.7462 121.397 50.6506 121.046 50.4661 120.745C50.2774 120.446 49.9869 120.23 49.5948 120.096Z"
        fill="#35B729"
      />
      <path
        d="M38.0618 116.724L35.8887 120.578L34.321 119.695L38.0714 113.043L39.5915 113.9L38.9249 115.082L38.9942 115.121C39.3543 114.833 39.7729 114.668 40.25 114.625C40.7241 114.58 41.2109 114.699 41.7104 114.98C42.1464 115.226 42.4773 115.54 42.7031 115.922C42.9244 116.306 43.0239 116.737 43.0016 117.215C42.9777 117.696 42.8159 118.202 42.5164 118.733L40.1285 122.969L38.5607 122.085L40.812 118.092C41.0496 117.671 41.123 117.271 41.032 116.892C40.941 116.514 40.7079 116.218 40.3325 116.007C40.1016 115.877 39.8461 115.807 39.5661 115.797C39.286 115.788 39.0141 115.855 38.7502 115.999C38.4817 116.145 38.2523 116.386 38.0618 116.724Z"
        fill="#35B729"
      />
      <path
        d="M35.3774 110.985L30.5775 116.924L29.2203 115.827L30.0203 114.837L29.9584 114.787C29.5731 115.043 29.1739 115.157 28.761 115.13C28.3434 115.104 27.9645 114.953 27.6242 114.678C27.5469 114.615 27.4636 114.542 27.3745 114.457C27.2807 114.373 27.2068 114.298 27.1528 114.233L28.1934 112.945C28.236 113.014 28.3155 113.108 28.4319 113.228C28.5436 113.348 28.6588 113.456 28.7774 113.551C29.0326 113.758 29.3068 113.888 29.6 113.941C29.8886 113.996 30.169 113.973 30.4412 113.873C30.7135 113.774 30.9517 113.598 31.1558 113.345L33.9776 109.853L35.3774 110.985Z"
        fill="#35B729"
      />
      <path
        d="M24.8835 107.4L28.2332 104.433L29.4263 105.781L23.7095 110.844L22.5691 109.556L23.5405 108.696L23.4812 108.629C23.0446 108.781 22.6046 108.796 22.1611 108.675C21.7155 108.552 21.3037 108.277 20.9257 107.85C20.5763 107.456 20.3563 107.037 20.2658 106.595C20.173 106.15 20.2152 105.706 20.3925 105.261C20.5675 104.814 20.8845 104.389 21.3432 103.987L24.9832 100.764L26.1764 102.111L22.7448 105.15C22.3627 105.488 22.1516 105.852 22.1115 106.242C22.0693 106.629 22.2019 106.997 22.5096 107.344C22.7183 107.58 22.9561 107.743 23.2229 107.835C23.4851 107.926 23.7591 107.938 24.0449 107.87C24.3285 107.801 24.608 107.644 24.8835 107.4Z"
        fill="#35B729"
      />
      <path
        d="M21.5275 95.0671C21.9212 95.7238 22.1247 96.3727 22.1381 97.0139C22.1469 97.6539 21.9765 98.2527 21.6267 98.8102C21.274 99.3693 20.7551 99.8543 20.07 100.265C19.3963 100.669 18.7283 100.895 18.0661 100.945C17.3993 100.993 16.7849 100.87 16.2229 100.577C15.658 100.286 15.1881 99.8276 14.8132 99.2022C14.5712 98.7985 14.4083 98.3784 14.3243 97.9418C14.2358 97.5041 14.2439 97.0664 14.3487 96.6288C14.4518 96.1883 14.6645 95.7632 14.987 95.3535C15.3066 94.9455 15.7535 94.5694 16.3277 94.2252L16.8011 93.9415L20.0193 99.31L18.9788 99.9337L16.6476 96.0448C16.3537 96.2249 16.129 96.4465 15.9738 96.7096C15.8156 96.9744 15.7371 97.2592 15.7381 97.5638C15.7374 97.8657 15.8257 98.1644 16.0029 98.46C16.1921 98.7756 16.435 99.0067 16.7316 99.1535C17.0254 99.302 17.336 99.3689 17.6635 99.3542C17.9863 99.3385 18.295 99.2442 18.5895 99.0715L19.4978 98.5271C19.8787 98.2987 20.1639 98.0331 20.3533 97.7302C20.5398 97.429 20.6306 97.1099 20.6256 96.7728C20.6178 96.4375 20.5134 96.1021 20.3123 95.7666C20.1777 95.542 20.0245 95.3575 19.8529 95.2131C19.6785 95.0704 19.4887 94.9697 19.2835 94.911C19.0783 94.8524 18.8629 94.8444 18.6372 94.8869L17.9353 93.3485C18.3707 93.2111 18.8073 93.1851 19.2449 93.2706C19.678 93.3549 20.0912 93.5477 20.4844 93.8492C20.8748 94.1523 21.2225 94.5583 21.5275 95.0671Z"
        fill="#35B729"
      />
      <path
        d="M20.6821 87.236C20.7674 87.4659 20.8277 87.6857 20.863 87.8953C20.9003 88.1006 20.917 88.2694 20.913 88.4017L19.4533 88.4978C19.4337 88.2258 19.3866 87.994 19.3121 87.8025C19.2375 87.611 19.1141 87.4641 18.9418 87.3619C18.7715 87.2554 18.5321 87.201 18.2237 87.1988L17.7833 87.1978L11.4377 92.5054L10.7737 90.7155L15.5685 87.0596L15.5409 86.985L9.52002 87.3362L8.85428 85.5416L17.9443 85.4306C18.373 85.4236 18.7634 85.4802 19.1156 85.6006C19.4709 85.7197 19.7794 85.9129 20.0411 86.1799C20.3047 86.4427 20.5184 86.7947 20.6821 87.236Z"
        fill="#35B729"
      />
      <path
        d="M11.5435 43.7208C11.1213 43.6095 10.7357 43.6717 10.3869 43.9076C10.0392 44.1404 9.76787 44.5122 9.57302 45.0232C9.43604 45.3825 9.37273 45.7113 9.3831 46.0096C9.39348 46.308 9.46559 46.5608 9.59944 46.7679C9.73329 46.975 9.91886 47.1256 10.1561 47.2196C10.3543 47.2952 10.5433 47.3158 10.7231 47.2815C10.9041 47.2441 11.0758 47.1659 11.2382 47.0469C11.3976 46.9268 11.5479 46.7837 11.6893 46.6177C11.8306 46.4516 11.9617 46.2799 12.0826 46.1026L12.6456 45.2957C12.8662 44.9683 13.1109 44.6661 13.3798 44.3891C13.6498 44.1089 13.9433 43.8786 14.2605 43.698C14.5788 43.5144 14.9208 43.4036 15.2865 43.3656C15.6521 43.3277 16.0409 43.3873 16.4527 43.5443C17.0102 43.7569 17.4467 44.0865 17.7623 44.5332C18.0749 44.9787 18.2476 45.5164 18.2806 46.1462C18.3117 46.7717 18.1819 47.4653 17.8914 48.2272C17.6092 48.9673 17.2496 49.5662 16.8126 50.0239C16.3767 50.4785 15.8805 50.7752 15.3238 50.9141C14.7683 51.0499 14.17 51.0097 13.5288 50.7936L14.1753 49.0981C14.5161 49.1996 14.8307 49.2026 15.1191 49.1068C15.4075 49.011 15.6608 48.8398 15.879 48.5931C16.0984 48.3433 16.2766 48.0388 16.4136 47.6796C16.5565 47.3049 16.6259 46.9553 16.6219 46.631C16.6159 46.3024 16.543 46.0192 16.4031 45.7814C16.2601 45.5424 16.053 45.3694 15.7816 45.2624C15.5327 45.171 15.3005 45.1659 15.0852 45.2469C14.8667 45.3268 14.6543 45.4746 14.4479 45.6903C14.2396 45.9017 14.0271 46.1612 13.8105 46.4688L13.119 47.4449C12.619 48.1516 12.0777 48.6547 11.4954 48.9541C10.9111 49.2491 10.2783 49.2668 9.59694 49.007C9.03641 48.7932 8.60342 48.4543 8.29797 47.9902C7.99371 47.523 7.82582 46.9819 7.79431 46.3669C7.75971 45.7506 7.86935 45.1096 8.12325 44.4438C8.38069 43.7687 8.72393 43.222 9.15296 42.8039C9.58008 42.3815 10.0566 42.1038 10.5826 41.971C11.1055 41.8369 11.6366 41.8674 12.1759 42.0624L11.5435 43.7208Z"
        fill="#35B729"
      />
      <path
        d="M22.5939 39.0165C22.1903 39.6671 21.7065 40.1451 21.1426 40.4506C20.5777 40.7514 19.9673 40.874 19.3115 40.8182C18.6529 40.7607 17.9843 40.5214 17.3055 40.1003C16.638 39.6862 16.1308 39.1961 15.7839 38.6298C15.4359 38.059 15.2635 37.4567 15.2667 36.8229C15.267 36.1874 15.4593 35.5598 15.8437 34.9402C16.0918 34.5402 16.3907 34.203 16.7404 33.9285C17.0891 33.6494 17.4819 33.4563 17.919 33.3491C18.3577 33.2391 18.8331 33.2336 19.3449 33.3327C19.854 33.4301 20.393 33.6552 20.9619 34.0082L21.4308 34.2991L18.1313 39.618L17.1004 38.9785L19.4906 35.1256C19.1959 34.9467 18.8961 34.8484 18.5911 34.8308C18.2833 34.8115 17.9941 34.872 17.7237 35.0124C17.4551 35.15 17.2299 35.3652 17.0482 35.6581C16.8543 35.9707 16.76 36.2925 16.7653 36.6235C16.7678 36.9526 16.8505 37.2594 17.0134 37.5438C17.1753 37.8236 17.4004 38.0549 17.6887 38.2377L18.5886 38.7959C18.966 39.0301 19.3327 39.162 19.6887 39.1917C20.0418 39.2197 20.3671 39.1543 20.6645 38.9956C20.9591 38.8351 21.2095 38.5886 21.4156 38.2563C21.5537 38.0338 21.6476 37.8132 21.6975 37.5945C21.7445 37.3741 21.7471 37.1592 21.7053 36.95C21.6635 36.7407 21.572 36.5455 21.4309 36.3644L22.4773 35.036C22.7988 35.3602 23.0217 35.7365 23.1461 36.1647C23.2695 36.5883 23.2871 37.0439 23.1992 37.5315C23.1084 38.0173 22.9067 38.5123 22.5939 39.0165Z"
        fill="#35B729"
      />
      <path
        d="M28.6875 31.1731C28.1713 31.7385 27.6079 32.1195 26.9975 32.3161C26.3868 32.5081 25.7643 32.5164 25.13 32.3411C24.4932 32.1635 23.8799 31.8055 23.29 31.2668C22.71 30.7372 22.3014 30.1622 22.0645 29.5418C21.8273 28.9168 21.7685 28.2931 21.888 27.6707C22.0051 27.046 22.3095 26.4644 22.8012 25.926C23.1186 25.5784 23.4743 25.3019 23.8685 25.0963C24.2625 24.886 24.6841 24.7684 25.1334 24.7433C25.585 24.7157 26.0532 24.7977 26.5382 24.9892C27.0206 25.1784 27.5091 25.4988 28.0035 25.9502L28.411 26.3223L24.1903 30.9445L23.2945 30.1265L26.3519 26.7783C26.0952 26.5483 25.8185 26.3966 25.5219 26.3233C25.2229 26.2477 24.9275 26.2541 24.6359 26.3424C24.3466 26.4283 24.0857 26.5985 23.8533 26.853C23.6052 27.1247 23.4534 27.4237 23.3978 27.7499C23.3398 28.074 23.3647 28.3907 23.4726 28.7002C23.5803 29.005 23.7591 29.2737 24.0089 29.5064L24.7909 30.2204C25.1189 30.5199 25.4551 30.7169 25.7995 30.8116C26.1416 30.904 26.4733 30.8995 26.7948 30.7981C27.1139 30.6944 27.4053 30.4982 27.669 30.2094C27.8455 30.016 27.9784 29.8165 28.0676 29.6107C28.1544 29.4026 28.1964 29.1919 28.1938 28.9785C28.1911 28.7652 28.1371 28.5565 28.0316 28.3525L29.3042 27.239C29.5607 27.6168 29.7107 28.0276 29.7543 28.4713C29.7977 28.9104 29.7314 29.3615 29.5553 29.8246C29.3768 30.2855 29.0876 30.735 28.6875 31.1731Z"
        fill="#35B729"
      />
      <path
        d="M44.1691 13.6616C44.6992 14.6221 44.9733 15.544 44.9915 16.4274C45.0109 17.3064 44.8117 18.0977 44.3938 18.8016C43.9788 19.5038 43.381 20.0704 42.6005 20.5012C41.8199 20.932 41.0205 21.1366 40.2022 21.115C39.3852 21.0889 38.6087 20.8342 37.8725 20.3509C37.1376 19.8631 36.5059 19.1404 35.9774 18.1828C35.4473 17.2223 35.1725 16.3026 35.153 15.4237C35.1349 14.5403 35.3333 13.7474 35.7483 13.0452C36.1662 12.3413 36.7654 11.774 37.546 11.3432C38.3266 10.9124 39.1245 10.7086 39.9399 10.7318C40.7582 10.7534 41.5348 11.0081 42.2696 11.4959C43.0058 11.9792 43.639 12.7011 44.1691 13.6616ZM42.5455 14.5577C42.1724 13.8816 41.7518 13.3698 41.2837 13.0224C40.8169 12.6706 40.3321 12.4782 39.8294 12.4452C39.325 12.4094 38.8306 12.5252 38.346 12.7926C37.8614 13.0601 37.4998 13.4167 37.2613 13.8626C37.0212 14.3055 36.9242 14.819 36.9702 15.4031C37.0175 15.9827 37.2278 16.6106 37.6009 17.2867C37.9741 17.9628 38.3941 18.4768 38.8608 18.8287C39.3289 19.1761 39.8151 19.3677 40.3195 19.4035C40.8222 19.4365 41.3159 19.3192 41.8005 19.0517C42.2851 18.7843 42.6474 18.4291 42.8875 17.9861C43.126 17.5403 43.2216 17.0276 43.1743 16.448C43.1283 15.8639 42.9187 15.2338 42.5455 14.5577Z"
        fill="#35B729"
      />
      <path
        d="M53.0976 12.7405L51.7856 8.51475L53.5044 7.98108L55.7688 15.274L54.1022 15.7914L53.6998 14.4952L53.6238 14.5188C53.586 14.9783 53.419 15.3962 53.1229 15.7727C52.83 16.1483 52.4098 16.421 51.8622 16.5911C51.3842 16.7395 50.9287 16.7646 50.4957 16.6666C50.0648 16.5644 49.6832 16.341 49.3506 15.9966C49.0171 15.649 48.7599 15.184 48.5791 14.6015L47.1373 9.95801L48.8561 9.42434L50.2153 13.802C50.3588 14.2641 50.5994 14.592 50.9372 14.7856C51.2749 14.9792 51.6495 15.0121 52.061 14.8843C52.3143 14.8057 52.5404 14.6678 52.7395 14.4706C52.9385 14.2735 53.0762 14.0295 53.1525 13.7385C53.2309 13.4435 53.2126 13.1108 53.0976 12.7405Z"
        fill="#35B729"
      />
      <path
        d="M59.1253 14.7205L58.0994 7.1534L59.8286 6.91896L59.9996 8.18015L60.0784 8.16947C60.1571 7.71395 60.3479 7.34524 60.6509 7.06335C60.9567 6.77773 61.3263 6.60553 61.7599 6.54676C61.8584 6.5334 61.9691 6.52341 62.0919 6.51679C62.2176 6.50644 62.3227 6.50391 62.407 6.5092L62.6294 8.14973C62.5503 8.13369 62.4273 8.12696 62.2604 8.12951C62.0964 8.12834 61.9388 8.138 61.7877 8.15848C61.4626 8.20256 61.1798 8.3128 60.9395 8.48921C60.702 8.66188 60.5261 8.8814 60.4117 9.14776C60.2973 9.41412 60.2619 9.70824 60.3056 10.0301L60.9087 14.4788L59.1253 14.7205Z"
        fill="#35B729"
      />
      <path
        d="M79.225 4.80509L81.0344 5.07593L79.9746 12.1561C79.8741 12.8047 79.6528 13.3429 79.3106 13.771C78.9688 14.1957 78.5349 14.4977 78.0087 14.677C77.4863 14.8535 76.9022 14.8934 76.2565 14.7968C75.6664 14.7084 75.1511 14.5241 74.7105 14.2436C74.2737 13.9604 73.9487 13.59 73.7357 13.1325C73.5227 12.675 73.4625 12.1365 73.5553 11.517L75.3696 11.7886C75.3321 12.0611 75.356 12.3043 75.4413 12.5182C75.5299 12.7325 75.6696 12.9076 75.8604 13.0434C76.0512 13.1792 76.281 13.2672 76.5498 13.3074C76.8415 13.3511 77.0981 13.3275 77.3194 13.2366C77.5413 13.1425 77.7229 12.982 77.8641 12.7551C78.0085 12.5288 78.1089 12.2388 78.1652 11.8853L79.225 4.80509Z"
        fill="#35B729"
      />
      <path
        d="M85.8847 17.2408C85.1786 17.0012 84.6193 16.6381 84.2069 16.1516C83.7945 15.6651 83.5403 15.0941 83.4443 14.4385C83.3514 13.7839 83.4318 13.0832 83.6853 12.3362C83.9388 11.5892 84.3021 10.9828 84.7753 10.5169C85.2516 10.052 85.8014 9.75209 86.4247 9.61714C87.048 9.48219 87.7128 9.53455 88.4189 9.77423C89.1251 10.0139 89.6844 10.377 90.0968 10.8635C90.5092 11.3499 90.7613 11.922 90.8531 12.5797C90.948 13.2384 90.8687 13.9413 90.6152 14.6883C90.3617 15.4352 89.9973 16.0396 89.5221 16.5013C89.05 16.9641 88.5023 17.263 87.879 17.3979C87.2557 17.5329 86.5909 17.4805 85.8847 17.2408ZM86.3575 15.8788C86.7404 16.0087 87.0962 16.0122 87.425 15.8893C87.7548 15.7632 88.0432 15.5426 88.2901 15.2274C88.5403 14.9133 88.7399 14.5365 88.889 14.0971C89.0392 13.6546 89.1107 13.2326 89.1035 12.8311C89.1004 12.4276 89.0064 12.0754 88.8215 11.7747C88.6366 11.4739 88.3527 11.2585 87.9698 11.1286C87.5774 10.9954 87.2148 10.9913 86.8819 11.1163C86.5521 11.2424 86.2616 11.4641 86.0104 11.7813C85.7634 12.0965 85.5648 12.4754 85.4146 12.9179C85.2655 13.3573 85.193 13.7772 85.1971 14.1776C85.2043 14.5791 85.3004 14.9302 85.4854 15.231C85.6745 15.5297 85.9652 15.7456 86.3575 15.8788Z"
        fill="#35B729"
      />
      <path
        d="M97.8928 19.2073L100.061 15.35L101.629 16.2317L97.8883 22.8889L96.367 22.034L97.032 20.8508L96.9626 20.8118C96.6029 21.1001 96.1846 21.2661 95.7076 21.3098C95.2335 21.3551 94.7465 21.2373 94.2466 20.9564C93.8103 20.7112 93.479 20.3977 93.2526 20.0157C93.0308 19.6325 92.9307 19.2018 92.9523 18.7235C92.9756 18.2423 93.1366 17.7359 93.4354 17.2043L95.8174 12.9655L97.3864 13.8472L95.1407 17.8433C94.9036 18.2651 94.8309 18.6652 94.9224 19.0436C95.0139 19.422 95.2475 19.7167 95.6231 19.9278C95.8542 20.0577 96.1098 20.1272 96.3898 20.1363C96.6699 20.1454 96.9418 20.0777 97.2055 19.9332C97.4737 19.7873 97.7028 19.5454 97.8928 19.2073Z"
        fill="#35B729"
      />
      <path
        d="M100.588 24.9695L105.379 19.0235L106.738 20.1185L105.94 21.1095L106.002 21.1594C106.387 20.9035 106.785 20.7887 107.198 20.815C107.616 20.8408 107.995 20.991 108.336 21.2655C108.413 21.3279 108.497 21.4014 108.586 21.4861C108.68 21.5703 108.754 21.6449 108.808 21.7097L107.769 22.9988C107.726 22.9303 107.647 22.8364 107.53 22.7169C107.418 22.5969 107.303 22.4891 107.184 22.3935C106.929 22.1876 106.654 22.058 106.361 22.0046C106.072 21.9508 105.792 21.9739 105.52 22.0739C105.248 22.1738 105.01 22.3503 104.806 22.6032L101.989 26.0988L100.588 24.9695Z"
        fill="#35B729"
      />
      <path
        d="M111.085 28.5352L107.74 31.5064L106.545 30.1607L112.254 25.0899L113.397 26.3761L112.426 27.2377L112.486 27.3047C112.922 27.1521 113.362 27.1359 113.806 27.2561C114.252 27.3788 114.664 27.6533 115.042 28.0795C115.392 28.4735 115.613 28.8916 115.704 29.3337C115.797 29.7782 115.756 30.223 115.579 30.668C115.405 31.1155 115.088 31.5405 114.63 31.9429L110.995 35.1716L109.8 33.826L113.227 30.7822C113.609 30.4432 113.819 30.0789 113.859 29.6892C113.901 29.3019 113.767 28.9348 113.459 28.5879C113.25 28.3524 113.012 28.1893 112.745 28.0983C112.483 28.0076 112.209 27.9961 111.923 28.0637C111.64 28.1337 111.36 28.2909 111.085 28.5352Z"
        fill="#35B729"
      />
      <path
        d="M114.457 40.8647C114.062 40.2086 113.858 39.5599 113.843 38.9188C113.834 38.2788 114.003 37.6798 114.352 37.1218C114.704 36.5621 115.222 36.0765 115.907 35.6648C116.58 35.26 117.248 35.0326 117.91 34.9824C118.576 34.9335 119.191 35.0551 119.753 35.3472C120.319 35.6377 120.789 36.0954 121.165 36.7203C121.408 37.1236 121.571 37.5435 121.656 37.9799C121.745 38.4175 121.737 38.8552 121.633 39.293C121.531 39.7336 121.319 40.159 120.997 40.5692C120.678 40.9776 120.231 41.3543 119.658 41.6994L119.185 41.9838L115.959 36.6197L116.998 35.9946L119.335 39.8802C119.629 39.6997 119.853 39.4778 120.008 39.2145C120.166 38.9494 120.244 38.6646 120.242 38.3599C120.243 38.0581 120.154 37.7595 119.976 37.4641C119.787 37.1488 119.544 36.918 119.247 36.7716C118.953 36.6236 118.642 36.5571 118.315 36.5722C117.992 36.5884 117.683 36.6831 117.389 36.8562L116.481 37.4019C116.101 37.6308 115.816 37.8968 115.627 38.2C115.441 38.5014 115.351 38.8207 115.356 39.1577C115.364 39.4931 115.469 39.8283 115.671 40.1635C115.806 40.3879 115.959 40.5722 116.131 40.7164C116.306 40.8589 116.496 40.9593 116.701 41.0177C116.906 41.076 117.121 41.0837 117.347 41.0409L118.051 42.5783C117.616 42.7163 117.179 42.7429 116.742 42.6581C116.308 42.5744 115.895 42.3821 115.501 42.0812C115.111 41.7786 114.762 41.3731 114.457 40.8647Z"
        fill="#35B729"
      />
      <path
        d="M115.317 48.7065C115.231 48.4767 115.171 48.257 115.135 48.0474C115.098 47.8422 115.081 47.6734 115.085 47.5411L116.544 47.443C116.564 47.7149 116.611 47.9466 116.686 48.138C116.761 48.3294 116.885 48.4762 117.057 48.5782C117.228 48.6844 117.467 48.7385 117.775 48.7403L118.216 48.7406L124.554 43.4241L125.221 45.2131L120.431 48.8757L120.459 48.9503L126.479 48.5907L127.147 50.3843L118.057 50.5081C117.629 50.5157 117.238 50.4596 116.886 50.3397C116.53 50.221 116.222 50.0284 115.96 49.7617C115.696 49.4993 115.481 49.1476 115.317 48.7065Z"
        fill="#35B729"
      />
      <path
        d="M12.847 67.7923L11.6992 67.4636C11.1824 67.3176 10.7116 67.041 10.3319 66.6605C9.95213 66.28 9.67622 65.8081 9.5305 65.2902L9.20252 64.1399C9.18602 64.0997 9.15796 64.0653 9.1219 64.0411C9.08584 64.0168 9.04341 64.0039 9 64.0039C8.95659 64.0039 8.91416 64.0168 8.8781 64.0411C8.84204 64.0653 8.81398 64.0997 8.79748 64.1399L8.4695 65.2902C8.32378 65.8081 8.04787 66.28 7.66815 66.6605C7.28843 67.041 6.81762 67.3176 6.30077 67.4636L5.15303 67.7923C5.10897 67.8048 5.07018 67.8314 5.04256 67.8681C5.01494 67.9047 5 67.9493 5 67.9952C5 68.0412 5.01494 68.0858 5.04256 68.1224C5.07018 68.1591 5.10897 68.1857 5.15303 68.1982L6.30077 68.5269C6.81762 68.6729 7.28843 68.9494 7.66815 69.33C8.04787 69.7105 8.32378 70.1823 8.4695 70.7003L8.79748 71.8505C8.80999 71.8947 8.83653 71.9336 8.87308 71.9613C8.90963 71.9889 8.95419 72.0039 9 72.0039C9.04581 72.0039 9.09037 71.9889 9.12692 71.9613C9.16347 71.9336 9.19001 71.8947 9.20252 71.8505L9.5305 70.7003C9.67622 70.1823 9.95213 69.7105 10.3319 69.33C10.7116 68.9494 11.1824 68.6729 11.6992 68.5269L12.847 68.1982C12.891 68.1857 12.9298 68.1591 12.9574 68.1224C12.9851 68.0858 13 68.0412 13 67.9952C13 67.9493 12.9851 67.9047 12.9574 67.8681C12.9298 67.8314 12.891 67.8048 12.847 67.7923Z"
        fill="#35B729"
      />
      <path
        d="M130.847 67.7923L129.699 67.4636C129.182 67.3176 128.712 67.041 128.332 66.6605C127.952 66.28 127.676 65.8081 127.53 65.2902L127.203 64.1399C127.186 64.0997 127.158 64.0653 127.122 64.0411C127.086 64.0168 127.043 64.0039 127 64.0039C126.957 64.0039 126.914 64.0168 126.878 64.0411C126.842 64.0653 126.814 64.0997 126.797 64.1399L126.47 65.2902C126.324 65.8081 126.048 66.28 125.668 66.6605C125.288 67.041 124.818 67.3176 124.301 67.4636L123.153 67.7923C123.109 67.8048 123.07 67.8314 123.043 67.8681C123.015 67.9047 123 67.9493 123 67.9952C123 68.0412 123.015 68.0858 123.043 68.1224C123.07 68.1591 123.109 68.1857 123.153 68.1982L124.301 68.5269C124.818 68.6729 125.288 68.9494 125.668 69.33C126.048 69.7105 126.324 70.1823 126.47 70.7003L126.797 71.8505C126.81 71.8947 126.837 71.9336 126.873 71.9613C126.91 71.9889 126.954 72.0039 127 72.0039C127.046 72.0039 127.09 71.9889 127.127 71.9613C127.163 71.9336 127.19 71.8947 127.203 71.8505L127.53 70.7003C127.676 70.1823 127.952 69.7105 128.332 69.33C128.712 68.9494 129.182 68.6729 129.699 68.5269L130.847 68.1982C130.891 68.1857 130.93 68.1591 130.957 68.1224C130.985 68.0858 131 68.0412 131 67.9952C131 67.9493 130.985 67.9047 130.957 67.8681C130.93 67.8314 130.891 67.8048 130.847 67.7923Z"
        fill="#35B729"
      />
    </svg>
  );
};

export const DownArrowIcon2 = (svgProps) => {
  return (
    <svg
      {...svgProps}
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15 6.25V23.75M15 23.75L23.75 15M15 23.75L6.25 15"
        stroke="#35B729"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const ToolplateIcon = (svgProps) => {
  return (
    <svg {...svgProps} viewBox="0 0 24 25" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.35059 0.879398C0.818043 0.411945 1.57593 0.411945 2.04339 0.879398C7.17588 6.01189 7.17205 14.3534 2.04605 19.5119C1.58008 19.9808 0.822189 19.9832 0.353264 19.5172C-0.115661 19.0512 -0.118052 18.2933 0.347924 17.8244C4.54907 13.5967 4.54524 6.76685 0.35059 2.5722C-0.116863 2.10474 -0.116863 1.34685 0.35059 0.879398Z"
        fill="url(#paint0_linear_7511_33475)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.347924 0.882073C0.8139 0.413147 1.57179 0.410757 2.04071 0.876733C6.26845 5.07787 13.0983 5.07405 17.2929 0.879398C17.7604 0.411945 18.5183 0.411945 18.9857 0.879398C19.4532 1.34685 19.4532 2.10474 18.9857 2.5722C13.8532 7.70469 5.51171 7.70086 0.353264 2.57486C-0.115661 2.10889 -0.118052 1.351 0.347924 0.882073Z"
        fill="url(#paint1_linear_7511_33475)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.353264 17.7895C5.51171 12.6634 13.8532 12.6596 18.9857 17.7921C19.4532 18.2596 19.4532 19.0175 18.9857 19.4849C18.5183 19.9524 17.7604 19.9524 17.2929 19.4849C13.0983 15.2903 6.26845 15.2864 2.04071 19.4876C1.57179 19.9536 0.8139 19.9512 0.347924 19.4822C-0.118052 19.0133 -0.115661 18.2554 0.353264 17.7895Z"
        fill="url(#paint2_linear_7511_33475)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.9837 0.847924C19.4526 1.3139 19.455 2.07179 18.989 2.54071C14.7879 6.76845 14.7917 13.5983 18.9864 17.7929L18.14 18.6393L18.9864 17.7929L23.6499 22.4565C24.1174 22.924 24.1174 23.6819 23.6499 24.1493C23.1825 24.6168 22.4246 24.6168 21.9571 24.1493L17.2936 19.4857C12.1611 14.3532 12.1649 6.01171 17.2909 0.853264C17.7569 0.384339 18.5148 0.381948 18.9837 0.847924Z"
        fill="url(#paint3_linear_7511_33475)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_7511_33475"
          x1="6.27084"
          y1="5.10846"
          x2="-3.88582"
          y2="15.2651"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5A4FCF" stopOpacity="0.3" />
          <stop offset="1" stopColor="#5A4FCF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_7511_33475"
          x1="14.7347"
          y1="-3.35538"
          x2="4.57809"
          y2="6.80122"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5A4FCF" stopOpacity="0.3" />
          <stop offset="1" stopColor="#5A4FCF" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_7511_33475"
          x1="14.7347"
          y1="13.5722"
          x2="4.57803"
          y2="23.7289"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5A4FCF" stopOpacity="0.3" />
          <stop offset="1" stopColor="#5A4FCF" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_7511_33475"
          x1="25.5284"
          y1="7.43837"
          x2="15.3718"
          y2="17.595"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5A4FCF" stopOpacity="0.3" />
          <stop offset="1" stopColor="#5A4FCF" />
        </linearGradient>
      </defs>
    </svg>
  );
};
export const WoofferIcon = (svgProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
      viewBox="0 0 24 25"
      fill="none"
    >
      <path
        d="M12.4287 23.7916L23.7083 15.605C23.9612 15.4197 24.0624 15.0996 23.9612 14.7964L19.645 1.55637C19.5438 1.25316 19.274 1.06787 18.9706 1.06787H5.02704C4.72355 1.06787 4.43693 1.27001 4.35263 1.55637L0.0363723 14.8133C-0.0647899 15.1165 0.0532327 15.4365 0.289278 15.6218L11.5689 23.8084C11.8218 23.9769 12.1758 23.9769 12.4287 23.7916Z"
        fill="#F26A34"
      />
      <path
        d="M11.8726 16.8851L9.61328 18.6875C11.0633 19.6139 12.9348 19.6139 14.4016 18.6875L12.1255 16.8851C12.058 16.8345 11.9569 16.8345 11.8726 16.8851Z"
        fill="#F8FAFC"
      />
      <path
        d="M21.1458 13.9711L17.7737 3.59467C17.7063 3.37569 17.4365 3.29147 17.251 3.42623L14.4185 5.49814C14.3005 5.58237 14.1319 5.58237 14.0138 5.49814L12.2098 4.06633C12.0918 3.96526 11.9063 3.96526 11.7883 4.06633L10.0011 5.49814C9.88305 5.59921 9.71445 5.59921 9.59643 5.51499L6.76389 3.42623C6.57842 3.29147 6.30866 3.37569 6.24121 3.59467L2.86914 13.9711C2.81856 14.1059 2.86914 14.2575 2.98716 14.3417L7.65749 17.7444C7.77551 17.8286 7.94411 17.8286 8.06213 17.7275L11.1644 15.2513C11.3162 15.1334 11.333 14.8976 11.1982 14.7628L9.17492 12.5393C8.97259 12.3203 9.12434 11.9666 9.42782 11.9666H14.5871C14.8737 11.9666 15.0423 12.3203 14.84 12.5393L12.8168 14.7628C12.6819 14.9144 12.6987 15.1334 12.8505 15.2513L15.9359 17.7444C16.0539 17.8454 16.2226 17.8454 16.3406 17.7612L21.0109 14.3585C21.1458 14.2575 21.1964 14.1059 21.1458 13.9711Z"
        fill="#F8FAFC"
      />
    </svg>
  );
};

export const AgileIcon = (svgProps) => {
  return (
    <svg
      viewBox="0 0 856 423"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <path
        d="M354.951 199.172L364.927 170.039H374.196L384.172 199.172H377.286L369.914 175.071H369.252L361.837 199.172H354.951ZM360.557 193.434V189.108H379.493V193.434H360.557ZM396.942 207.25C394.411 207.25 392.322 207.044 390.674 206.632C389.056 206.249 387.864 205.675 387.099 204.91C386.334 204.145 385.951 203.189 385.951 202.041C385.951 200.687 386.481 199.584 387.54 198.73C388.629 197.877 390.218 197.362 392.307 197.186V196.303C390.718 196.303 389.497 196.141 388.644 195.817C387.79 195.464 387.364 194.861 387.364 194.007C387.364 193.184 387.775 192.448 388.599 191.8C389.453 191.153 390.851 190.638 392.793 190.256V189.373C390.968 189.255 389.541 188.667 388.511 187.607C387.481 186.548 386.966 185.179 386.966 183.502C386.966 182.031 387.378 180.721 388.202 179.574C389.026 178.397 390.247 177.47 391.866 176.793C393.514 176.087 395.529 175.733 397.913 175.733H409.654V180.589L402.371 179.574V180.589C404.254 180.854 405.608 181.325 406.432 182.001C407.285 182.678 407.712 183.62 407.712 184.826C407.712 186.033 407.315 187.092 406.52 188.004C405.726 188.917 404.593 189.638 403.122 190.167C401.68 190.668 399.943 190.918 397.913 190.918C397.53 190.918 397.118 190.903 396.677 190.874C396.236 190.844 395.5 190.771 394.47 190.653C393.911 191.065 393.47 191.433 393.146 191.756C392.852 192.051 392.704 192.33 392.704 192.595C392.704 192.801 392.807 192.978 393.013 193.125C393.249 193.272 393.543 193.375 393.896 193.434C394.279 193.493 394.661 193.522 395.044 193.522H401.665C402.253 193.522 403.019 193.566 403.96 193.654C404.902 193.713 405.843 193.934 406.785 194.316C407.727 194.699 408.521 195.317 409.169 196.17C409.816 196.994 410.14 198.186 410.14 199.746C410.14 201.452 409.654 202.865 408.683 203.983C407.742 205.101 406.285 205.925 404.313 206.455C402.371 206.985 399.914 207.25 396.942 207.25ZM397.648 202.173C399.443 202.173 400.841 202.07 401.841 201.864C402.842 201.688 403.548 201.379 403.96 200.937C404.402 200.526 404.622 199.996 404.622 199.348C404.622 198.76 404.49 198.304 404.225 197.98C403.99 197.656 403.666 197.436 403.254 197.318C402.871 197.171 402.474 197.083 402.062 197.053C401.68 196.994 401.341 196.965 401.047 196.965H395.485C394.249 197.083 393.352 197.421 392.793 197.98C392.263 198.51 391.998 199.128 391.998 199.834C391.998 200.481 392.204 200.967 392.616 201.291C393.028 201.614 393.646 201.835 394.47 201.953C395.323 202.1 396.383 202.173 397.648 202.173ZM397.56 187.607C398.972 187.607 400.032 187.269 400.738 186.592C401.444 185.886 401.797 184.973 401.797 183.855C401.797 182.678 401.429 181.707 400.694 180.942C399.988 180.177 398.943 179.794 397.56 179.794C396.206 179.794 395.147 180.177 394.382 180.942C393.617 181.678 393.234 182.634 393.234 183.811C393.234 184.547 393.396 185.209 393.72 185.797C394.073 186.357 394.573 186.798 395.22 187.122C395.868 187.445 396.648 187.607 397.56 187.607ZM414.271 199.172V175.998H420.627V199.172H414.271ZM417.493 172.511C416.257 172.511 415.301 172.261 414.624 171.761C413.976 171.231 413.653 170.466 413.653 169.466C413.653 168.436 413.976 167.656 414.624 167.126C415.301 166.596 416.257 166.332 417.493 166.332C418.758 166.332 419.715 166.596 420.362 167.126C421.039 167.656 421.377 168.436 421.377 169.466C421.377 170.437 421.039 171.187 420.362 171.717C419.715 172.246 418.758 172.511 417.493 172.511ZM426.513 199.172V167.612H432.781V199.172H426.513ZM449.395 199.79C447.483 199.79 445.776 199.525 444.275 198.995C442.804 198.466 441.553 197.701 440.523 196.7C439.493 195.7 438.699 194.464 438.14 192.992C437.61 191.521 437.345 189.858 437.345 188.004C437.345 186.18 437.595 184.503 438.096 182.972C438.625 181.413 439.39 180.074 440.391 178.956C441.391 177.808 442.613 176.925 444.055 176.307C445.496 175.689 447.13 175.38 448.954 175.38C450.72 175.38 452.294 175.675 453.677 176.263C455.06 176.822 456.208 177.676 457.12 178.823C458.062 179.971 458.738 181.383 459.15 183.061C459.592 184.709 459.754 186.607 459.636 188.755L441.45 188.931V185.444L455.972 185.312L453.721 187.077C453.927 185.518 453.824 184.238 453.412 183.237C453 182.237 452.397 181.501 451.602 181.03C450.837 180.559 449.984 180.324 449.042 180.324C447.924 180.324 446.938 180.618 446.085 181.207C445.232 181.795 444.57 182.663 444.099 183.811C443.628 184.929 443.392 186.283 443.392 187.872C443.392 190.373 443.937 192.212 445.026 193.39C446.144 194.567 447.6 195.155 449.395 195.155C450.219 195.155 450.911 195.052 451.47 194.846C452.059 194.611 452.529 194.316 452.883 193.963C453.265 193.61 453.559 193.213 453.765 192.772C454.001 192.33 454.192 191.889 454.339 191.447L459.857 192.639C459.592 193.728 459.195 194.714 458.665 195.597C458.165 196.45 457.488 197.2 456.634 197.848C455.781 198.466 454.751 198.936 453.545 199.26C452.368 199.613 450.985 199.79 449.395 199.79ZM327.183 252.172V223.039H336.762L344.883 244.977H345.104L353.049 223.039H362.186V252.172H356.139L356.536 229.484H356.051L347.223 252.172H341.926L333.23 229.484H332.745L333.142 252.172H327.183ZM379.112 252.79C377.199 252.79 375.492 252.525 373.992 251.995C372.52 251.466 371.27 250.701 370.24 249.7C369.21 248.7 368.415 247.464 367.856 245.992C367.326 244.521 367.062 242.858 367.062 241.004C367.062 239.18 367.312 237.503 367.812 235.972C368.342 234.413 369.107 233.074 370.107 231.956C371.108 230.808 372.329 229.925 373.771 229.307C375.213 228.689 376.846 228.38 378.67 228.38C380.436 228.38 382.01 228.675 383.393 229.263C384.777 229.822 385.924 230.676 386.836 231.823C387.778 232.971 388.455 234.383 388.867 236.061C389.308 237.709 389.47 239.607 389.352 241.755L371.167 241.931V238.444L385.689 238.312L383.438 240.077C383.644 238.518 383.541 237.238 383.129 236.237C382.717 235.237 382.113 234.501 381.319 234.03C380.554 233.559 379.7 233.324 378.759 233.324C377.641 233.324 376.655 233.618 375.801 234.207C374.948 234.795 374.286 235.663 373.815 236.811C373.344 237.929 373.109 239.283 373.109 240.872C373.109 243.373 373.653 245.212 374.742 246.39C375.86 247.567 377.317 248.155 379.112 248.155C379.936 248.155 380.627 248.052 381.186 247.846C381.775 247.611 382.246 247.316 382.599 246.963C382.982 246.61 383.276 246.213 383.482 245.772C383.717 245.33 383.908 244.889 384.056 244.447L389.573 245.639C389.308 246.728 388.911 247.714 388.381 248.597C387.881 249.45 387.204 250.2 386.351 250.848C385.497 251.466 384.468 251.936 383.261 252.26C382.084 252.613 380.701 252.79 379.112 252.79ZM402.597 252.702C400.007 252.702 398.095 252.025 396.859 250.671C395.652 249.288 395.049 247.096 395.049 244.094V233.986H391.694L391.783 228.998H394.122C395.034 228.998 395.711 228.866 396.152 228.601C396.594 228.336 396.859 227.851 396.947 227.144L397.521 223.834H401.14V228.998H407.011V234.163H401.14V243.829C401.14 244.889 401.39 245.654 401.891 246.125C402.391 246.596 403.156 246.831 404.186 246.831C404.745 246.831 405.275 246.772 405.775 246.654C406.305 246.537 406.761 246.345 407.143 246.081V251.995C406.231 252.29 405.392 252.481 404.627 252.569C403.862 252.657 403.185 252.702 402.597 252.702ZM411.533 252.172V237.606V220.612H417.934V227.851C417.934 228.439 417.904 229.057 417.846 229.705C417.816 230.352 417.743 231.014 417.625 231.691C417.537 232.338 417.434 233 417.316 233.677C417.228 234.354 417.125 235.031 417.007 235.708H417.978C418.39 234.207 418.905 232.912 419.523 231.823C420.17 230.735 420.994 229.896 421.995 229.307C422.995 228.689 424.261 228.38 425.791 228.38C428.498 228.38 430.528 229.337 431.882 231.249C433.236 233.133 433.912 236.031 433.912 239.945V252.172H427.512V240.916C427.512 238.444 427.144 236.62 426.409 235.443C425.702 234.236 424.628 233.633 423.186 233.633C422.009 233.633 421.038 233.986 420.273 234.692C419.508 235.399 418.934 236.355 418.552 237.562C418.169 238.739 417.949 240.063 417.89 241.534V252.172H411.533ZM449.885 252.79C447.56 252.79 445.515 252.334 443.75 251.421C441.984 250.48 440.601 249.111 439.6 247.316C438.6 245.492 438.1 243.226 438.1 240.519C438.1 237.812 438.6 235.56 439.6 233.765C440.63 231.97 442.028 230.632 443.794 229.749C445.589 228.836 447.619 228.38 449.885 228.38C452.21 228.38 454.255 228.836 456.02 229.749C457.816 230.661 459.213 232.029 460.214 233.854C461.244 235.649 461.759 237.9 461.759 240.607C461.759 243.344 461.229 245.624 460.17 247.449C459.14 249.244 457.727 250.583 455.932 251.466C454.167 252.348 452.151 252.79 449.885 252.79ZM450.106 248.067C451.253 248.067 452.21 247.802 452.975 247.272C453.769 246.743 454.358 245.948 454.74 244.889C455.152 243.8 455.358 242.49 455.358 240.96C455.358 239.342 455.138 237.973 454.696 236.855C454.284 235.708 453.666 234.84 452.842 234.251C452.018 233.633 450.974 233.324 449.708 233.324C448.62 233.324 447.678 233.589 446.883 234.119C446.089 234.619 445.486 235.413 445.074 236.502C444.662 237.562 444.456 238.871 444.456 240.431C444.456 242.932 444.956 244.83 445.957 246.125C446.957 247.419 448.34 248.067 450.106 248.067ZM474.708 252.79C472.766 252.79 471.059 252.29 469.588 251.289C468.146 250.289 467.028 248.876 466.233 247.052C465.439 245.227 465.042 243.064 465.042 240.563C465.042 238.209 465.395 236.12 466.101 234.295C466.807 232.471 467.852 231.043 469.235 230.014C470.618 228.984 472.339 228.469 474.399 228.469C475.9 228.469 477.165 228.748 478.195 229.307C479.225 229.866 480.064 230.69 480.711 231.779C481.388 232.839 481.918 234.119 482.3 235.619H483.316C483.11 234.678 482.918 233.765 482.742 232.883C482.595 231.97 482.477 231.102 482.389 230.278C482.3 229.454 482.256 228.733 482.256 228.116V220.612H488.612V240.784V252.172H483.316V245.463H482.433C482.109 247.11 481.609 248.479 480.932 249.568C480.255 250.656 479.387 251.466 478.328 251.995C477.298 252.525 476.091 252.79 474.708 252.79ZM476.827 247.581C477.798 247.581 478.622 247.375 479.299 246.963C479.976 246.551 480.535 246.022 480.976 245.374C481.418 244.697 481.741 243.977 481.947 243.211C482.153 242.417 482.256 241.681 482.256 241.004V240.166C482.256 239.607 482.168 239.033 481.991 238.444C481.844 237.826 481.624 237.223 481.329 236.635C481.035 236.046 480.667 235.531 480.226 235.09C479.784 234.619 479.269 234.251 478.681 233.986C478.092 233.721 477.445 233.589 476.739 233.589C475.679 233.589 474.782 233.883 474.046 234.472C473.311 235.06 472.737 235.884 472.325 236.944C471.913 237.973 471.707 239.18 471.707 240.563C471.707 241.976 471.913 243.211 472.325 244.271C472.766 245.33 473.369 246.154 474.134 246.743C474.9 247.302 475.797 247.581 476.827 247.581Z"
        fill="#212125"
      />
      <path
        d="M399.141 355.614C354.18 353.479 314.624 330.778 289.646 296.742L272.837 331.438L224.672 320.385C260.269 379.509 324.018 419.824 397.381 422.807L425.061 389.274L399.141 355.614Z"
        fill="#42D833"
      />
      <path
        d="M830.108 422.997H395.617V355.805H830.108L855.997 389.401L830.108 422.997Z"
        fill="#42D833"
      />
      <path
        d="M621.227 384.453H624.714V396.04H629.019V398.677H621.227V384.453Z"
        fill="white"
      />
      <path
        d="M631.637 397.196C630.632 396.129 630.129 394.747 630.129 393.052C630.072 391.517 630.613 390.02 631.637 388.876C632.094 388.353 632.66 387.936 633.295 387.654C633.93 387.372 634.619 387.231 635.313 387.243C636.727 387.243 637.858 387.777 638.612 388.844V387.368H642.1V398.672H638.612V397.039C638.25 397.598 637.751 398.056 637.163 398.369C636.575 398.682 635.917 398.84 635.25 398.829C634.566 398.836 633.888 398.693 633.264 398.411C632.64 398.129 632.085 397.715 631.637 397.196ZM637.858 395.093C638.361 394.632 638.612 393.941 638.612 393.02C638.637 392.646 638.583 392.271 638.453 391.92C638.323 391.568 638.12 391.248 637.858 390.979C637.636 390.746 637.368 390.561 637.07 390.437C636.773 390.312 636.453 390.251 636.13 390.257C635.808 390.255 635.489 390.318 635.192 390.442C634.895 390.566 634.627 390.749 634.402 390.979C634.149 391.259 633.955 391.587 633.831 391.943C633.707 392.298 633.655 392.676 633.679 393.052C633.658 393.423 633.711 393.794 633.835 394.145C633.959 394.495 634.152 394.818 634.402 395.093C634.627 395.323 634.895 395.506 635.192 395.63C635.489 395.754 635.808 395.817 636.13 395.815C636.453 395.821 636.773 395.76 637.07 395.635C637.368 395.511 637.636 395.326 637.858 395.093Z"
        fill="white"
      />
      <path
        d="M652.815 387.367H656.303V398.671H652.815V397.006C652.093 398.2 650.962 398.796 649.391 398.796C648.805 398.817 648.222 398.713 647.681 398.491C647.139 398.269 646.651 397.934 646.249 397.509C645.432 396.63 645.023 395.468 645.023 393.992V387.367H648.479V393.521C648.444 394.138 648.647 394.745 649.045 395.217C649.482 395.615 650.056 395.828 650.647 395.813C650.941 395.828 651.234 395.782 651.51 395.68C651.785 395.577 652.037 395.42 652.25 395.217C652.648 394.745 652.85 394.138 652.815 393.521V387.367Z"
        fill="white"
      />
      <path
        d="M662.808 387.37V389.034C663.53 387.841 664.661 387.244 666.232 387.244C666.822 387.226 667.408 387.332 667.955 387.553C668.501 387.775 668.995 388.108 669.406 388.531C670.191 389.411 670.599 390.572 670.599 392.079V398.673H667.143V392.55C667.168 392.244 667.13 391.937 667.033 391.645C666.936 391.354 666.781 391.085 666.578 390.855C666.369 390.647 666.117 390.487 665.841 390.384C665.565 390.281 665.27 390.238 664.975 390.258C664.682 390.244 664.388 390.29 664.113 390.392C663.837 390.495 663.586 390.652 663.373 390.855C663.17 391.085 663.015 391.354 662.918 391.645C662.821 391.937 662.783 392.244 662.808 392.55V398.673H659.352V387.37H662.808Z"
        fill="white"
      />
      <path
        d="M674.274 397.215C673.237 396.178 672.703 394.765 672.703 393.007C672.703 391.249 673.237 389.867 674.274 388.8C674.815 388.278 675.456 387.871 676.157 387.601C676.859 387.332 677.607 387.205 678.359 387.23C679.603 387.189 680.826 387.552 681.846 388.266C682.831 388.962 683.516 390.006 683.763 391.186H680.024C679.89 390.876 679.665 390.614 679.379 390.434C679.093 390.255 678.759 390.166 678.421 390.181C678.104 390.155 677.785 390.214 677.498 390.352C677.211 390.49 676.965 390.702 676.788 390.966C676.395 391.572 676.208 392.287 676.253 393.007C676.212 393.737 676.398 394.461 676.788 395.079C676.962 395.332 677.198 395.536 677.472 395.674C677.747 395.811 678.052 395.876 678.359 395.864C678.71 395.875 679.058 395.788 679.364 395.613C679.66 395.426 679.89 395.152 680.024 394.828H683.763C683.491 395.998 682.812 397.033 681.846 397.748C680.826 398.465 679.605 398.839 678.359 398.816C677.604 398.842 676.853 398.713 676.151 398.437C675.448 398.162 674.81 397.746 674.274 397.215Z"
        fill="white"
      />
      <path
        d="M689.199 383.648V389.018C689.89 387.825 691.052 387.228 692.623 387.228C693.208 387.211 693.79 387.317 694.331 387.538C694.872 387.76 695.361 388.093 695.765 388.515C696.582 389.394 696.99 390.556 696.99 392.063V398.657H693.534V392.534C693.534 391.781 693.346 391.216 692.937 390.839C692.733 390.634 692.487 390.474 692.216 390.372C691.945 390.269 691.656 390.225 691.366 390.242C691.072 390.222 690.777 390.265 690.501 390.368C690.225 390.471 689.973 390.631 689.764 390.839C689.561 391.069 689.406 391.338 689.309 391.629C689.212 391.92 689.174 392.228 689.199 392.534V398.657H685.711V383.648H689.199Z"
        fill="white"
      />
      <path
        d="M261.775 211.502C261.753 191.423 265.938 171.561 274.06 153.195L236.294 165.032L212.919 125.125C200.059 153.961 193.803 185.303 194.608 216.863C195.412 248.423 203.257 279.406 217.569 307.55L266.833 318.885L282.637 286.231C268.956 263.705 261.739 237.852 261.775 211.502Z"
        fill="#1DC10E"
      />
      <path
        d="M238.343 270.121L243.591 268.363L244.69 271.66L231.211 276.181L229.42 270.843C228.886 269.305 228.886 267.923 229.389 266.793C229.618 266.258 229.958 265.777 230.386 265.383C230.813 264.988 231.319 264.687 231.871 264.501C232.697 264.238 233.583 264.227 234.416 264.469C235.338 264.699 236.123 265.296 236.773 266.259L241.171 261.109L242.459 264.94L238.218 269.744L238.343 270.121ZM232.594 272.068L235.861 270.969L235.17 268.928C235.112 268.704 235.006 268.496 234.859 268.317C234.713 268.138 234.53 267.993 234.322 267.892C233.903 267.707 233.429 267.696 233.002 267.86C232.783 267.924 232.58 268.032 232.405 268.177C232.229 268.322 232.085 268.502 231.982 268.705C231.879 268.908 231.819 269.131 231.805 269.358C231.791 269.585 231.825 269.813 231.902 270.027L232.594 272.068Z"
        fill="white"
      />
      <path
        d="M239.564 254.326C239.941 255.927 239.784 257.34 238.998 258.627C238.213 259.915 236.988 260.762 235.291 261.202C233.594 261.642 232.086 261.453 230.829 260.7C230.193 260.307 229.643 259.791 229.211 259.181C228.779 258.572 228.475 257.881 228.316 257.151C227.89 255.681 228.06 254.103 228.787 252.756C229.541 251.437 230.767 250.558 232.557 250.118L233.626 249.93L235.542 257.465C235.765 257.384 235.968 257.259 236.141 257.097C236.314 256.935 236.452 256.74 236.548 256.523C236.762 256.13 236.828 255.674 236.736 255.236C236.656 254.906 236.529 254.589 236.359 254.294C236.267 254.139 236.138 254.009 235.982 253.917C235.831 253.797 235.661 253.701 235.479 253.635L234.537 249.992C235.697 250.014 236.811 250.448 237.679 251.217C238.624 252.025 239.285 253.115 239.564 254.326ZM231.301 254.859C231.087 255.332 231.043 255.865 231.175 256.366C231.281 256.872 231.572 257.32 231.992 257.622C232.417 257.908 232.927 258.041 233.437 257.999L232.4 253.886C232.159 253.961 231.936 254.087 231.746 254.255C231.557 254.423 231.405 254.629 231.301 254.859Z"
        fill="white"
      />
      <path
        d="M226.722 250.168L226.094 246.557L233.949 242.569L225.214 241.47L224.586 237.828L236.4 239.838L237.185 244.296L226.722 250.168Z"
        fill="white"
      />
      <path
        d="M222.763 232.782C222.976 232.942 223.153 233.145 223.283 233.377C223.413 233.61 223.493 233.867 223.518 234.132C223.562 234.404 223.55 234.682 223.484 234.95C223.417 235.218 223.297 235.469 223.131 235.689C222.964 235.909 222.755 236.093 222.516 236.23C222.276 236.367 222.011 236.454 221.737 236.486C221.463 236.518 221.186 236.494 220.921 236.415C220.657 236.337 220.411 236.206 220.198 236.03C219.986 235.854 219.811 235.637 219.685 235.391C219.559 235.146 219.484 234.878 219.464 234.603C219.427 234.342 219.444 234.076 219.515 233.822C219.585 233.569 219.707 233.332 219.873 233.127C220.217 232.709 220.71 232.439 221.249 232.375C221.787 232.31 222.33 232.456 222.763 232.782ZM235.645 234.446L224.397 235.765L223.989 232.342L235.237 231.023L235.645 234.446Z"
        fill="white"
      />
      <path
        d="M234.725 223.354C234.804 224.098 234.728 224.851 234.501 225.565C234.274 226.278 233.9 226.937 233.405 227.498C232.4 228.629 231.017 229.225 229.258 229.351C227.498 229.476 226.084 229.005 224.985 228.032C223.885 227.059 223.288 225.74 223.162 224.076C223.032 222.55 223.493 221.032 224.451 219.837C225.425 218.675 226.807 218.047 228.629 217.953H229.729L230.2 225.74C230.439 225.7 230.668 225.613 230.873 225.483C231.078 225.354 231.255 225.185 231.394 224.986C231.673 224.628 231.817 224.184 231.803 223.73C231.792 223.398 231.729 223.069 231.614 222.757C231.528 222.588 231.423 222.43 231.3 222.286L230.86 221.941L230.609 218.173C231.74 218.423 232.75 219.055 233.468 219.963C234.253 220.921 234.696 222.115 234.725 223.354ZM226.524 222.38C226.225 222.812 226.081 223.332 226.116 223.856C226.142 224.364 226.355 224.845 226.713 225.206C227.058 225.573 227.529 225.798 228.032 225.834L227.781 221.595C227.529 221.633 227.287 221.722 227.071 221.857C226.855 221.992 226.668 222.17 226.524 222.38Z"
        fill="white"
      />
      <path
        d="M223.142 202.672L223.237 199.406L234.453 203.08L234.359 206.816L227.635 208.229L234.296 209.988L234.202 213.724L222.797 216.833L222.891 213.347L230.872 211.809L222.985 209.862L223.08 206.188L231.06 204.65L223.142 202.672Z"
        fill="white"
      />
      <path
        d="M363.246 39.1815L387.312 0.8125C313.918 7.31199 251.3 51.2071 218.688 113.313L241.34 152.028L280.96 139.594C303.675 100.189 344.457 72.5267 391.994 67.8797L363.246 39.1815Z"
        fill="#35B729"
      />
      <path
        d="M264.736 92.6177C266.213 90.8908 267.941 89.9175 269.889 89.6977C270.847 89.5801 271.818 89.6738 272.736 89.9721C273.653 90.2705 274.494 90.7663 275.199 91.4246C276.801 92.8061 277.618 94.4702 277.65 96.4483C277.681 98.4264 276.958 100.279 275.45 101.974L272.151 105.742L261.438 96.3855L264.736 92.6177ZM272.465 101.409L273.471 100.248C274.351 99.2428 274.728 98.2066 274.602 97.0763C274.476 95.946 273.942 94.9726 272.905 94.0935C271.869 93.2143 270.8 92.7747 269.701 92.8061C268.601 92.8375 267.596 93.3399 266.716 94.3446L265.71 95.5064L272.465 101.409Z"
        fill="white"
      />
      <path
        d="M285.499 91.5333C284.305 92.6951 282.985 93.2602 281.477 93.2288C279.969 93.1974 278.618 92.5695 277.393 91.3135C276.167 90.0575 275.57 88.676 275.57 87.2002C275.592 86.4521 275.764 85.7161 276.078 85.0362C276.391 84.3564 276.838 83.7468 277.393 83.2439C278.456 82.1388 279.912 81.4959 281.446 81.4542C282.954 81.4228 284.336 82.0822 285.624 83.3695C285.874 83.6364 286.104 83.9197 286.316 84.2173L280.754 89.6493C281.124 89.9296 281.58 90.0734 282.044 90.0562C282.508 90.039 282.951 89.862 283.299 89.5552C283.545 89.3323 283.747 89.0662 283.896 88.7702C283.984 88.6044 284.037 88.423 284.054 88.2364C284.072 88.0475 284.061 87.8569 284.022 87.6712L286.724 85.0337C287.278 86.0494 287.467 87.2242 287.258 88.362C287.015 89.5759 286.4 90.6842 285.499 91.5333ZM280.88 84.6883C280.351 84.7205 279.855 84.9569 279.498 85.3477C279.132 85.6982 278.899 86.164 278.838 86.6664C278.802 87.1709 278.947 87.6716 279.246 88.0794L282.263 85.1279C282.078 84.9589 281.859 84.832 281.62 84.7561C281.381 84.6802 281.129 84.6571 280.88 84.6883Z"
        fill="white"
      />
      <path
        d="M287.363 76.2955C287.269 74.9768 287.772 73.8778 288.871 72.9359C289.399 72.4886 290.019 72.1615 290.686 71.9772C291.353 71.7929 292.053 71.7557 292.736 71.8683C294.212 72.0567 295.469 72.7789 296.569 74.0976C297.583 75.2359 298.122 76.7189 298.077 78.2422C298.064 78.9311 297.899 79.6087 297.595 80.2272C297.291 80.8457 296.855 81.39 296.317 81.8216C295.821 82.2626 295.216 82.5645 294.565 82.6968C293.914 82.8291 293.24 82.7872 292.61 82.5752L297.134 87.9757L294.464 90.205L283.75 77.3945L286.421 75.1652L287.363 76.2955ZM294.589 78.3992C294.684 77.7084 294.432 77.0491 293.867 76.3583C293.301 75.6675 292.641 75.2908 291.95 75.2594C291.631 75.2234 291.308 75.2522 291 75.3439C290.693 75.4356 290.407 75.5884 290.159 75.7931C289.911 75.9968 289.707 76.2498 289.561 76.5363C289.415 76.8227 289.33 77.1363 289.311 77.4573C289.217 78.148 289.468 78.8074 290.034 79.4982C290.599 80.1889 291.259 80.5657 291.95 80.6285C292.598 80.6752 293.238 80.473 293.741 80.0633C293.99 79.8597 294.194 79.6066 294.34 79.3202C294.486 79.0338 294.571 78.7201 294.589 78.3992Z"
        fill="white"
      />
      <path
        d="M301.845 77.2627L292.922 65.2052L295.718 63.1328L304.642 75.2217L301.845 77.2627Z"
        fill="white"
      />
      <path
        d="M306.872 71.8029C305.332 71.5203 304.107 70.6725 303.133 69.1968C302.159 67.721 301.907 66.2767 302.284 64.7695C302.714 63.2878 303.659 62.0084 304.95 61.162C306.24 60.3157 307.791 59.9581 309.322 60.1539C310.83 60.4051 312.087 61.2842 313.03 62.7286C313.972 64.1729 314.287 65.6801 313.941 67.1872C313.767 67.9398 313.444 68.6499 312.991 69.2755C312.537 69.9011 311.963 70.4296 311.302 70.8295C310.004 71.7085 308.419 72.0568 306.872 71.8029ZM310.673 66.8104C310.799 66.1511 310.611 65.4603 310.108 64.6753C309.605 63.8903 309.008 63.4508 308.348 63.3252C308.045 63.2517 307.729 63.2411 307.421 63.294C307.113 63.3469 306.819 63.4622 306.557 63.6327C306.295 63.8033 306.071 64.0255 305.898 64.2858C305.725 64.5461 305.607 64.8389 305.552 65.1463C305.492 65.5219 305.512 65.9059 305.609 66.2735C305.707 66.6412 305.88 66.9845 306.118 67.2814C306.62 68.0664 307.186 68.506 307.846 68.663C308.155 68.7399 308.476 68.7517 308.79 68.6977C309.104 68.6436 309.403 68.5249 309.668 68.349C309.937 68.1803 310.166 67.9554 310.339 67.6898C310.513 67.4242 310.627 67.1242 310.673 66.8104Z"
        fill="white"
      />
      <path
        d="M320.355 64.8634L311.148 57.2642L314.605 55.3801L320.386 60.6556L319.224 52.8366L322.397 51.0781L324.126 69.1967L320.952 70.9552L320.355 64.8634Z"
        fill="white"
      />
      <path
        d="M406.18 0C404.326 0 402.472 3.83189e-06 400.65 0.0628009L377.086 37.6154L406.714 67.1928C458.178 67.3812 503.264 94.4467 528.682 135.108L543.983 100.507L591.771 109.738C555.765 44.3347 486.141 0 406.18 0Z"
        fill="#31AA26"
      />
      <path
        d="M453.742 38.4026L454.559 35.8906L465.273 39.2818L464.456 41.7938L460.749 40.632L457.23 51.6847L453.931 50.6171L457.45 39.5644L453.742 38.4026Z"
        fill="white"
      />
      <path
        d="M467.071 55.4208C465.531 54.7928 464.463 53.8194 463.897 52.4378C463.332 51.0563 463.332 49.5491 463.992 47.9164C464.652 46.2836 465.657 45.1847 467.008 44.6195C468.359 44.0543 469.836 44.0543 471.375 44.6823C472.915 45.3102 474.015 46.2836 474.643 47.6338C475.271 48.9839 475.24 50.5225 474.58 52.218C474.438 52.565 474.27 52.9009 474.077 53.2228L466.851 50.3027C466.744 50.7566 466.798 51.2334 467.005 51.6516C467.211 52.0698 467.557 52.4032 467.982 52.5948C468.29 52.7336 468.619 52.8186 468.956 52.846L469.49 52.7832L470.024 52.532L473.512 53.9136C472.814 54.835 471.812 55.4801 470.684 55.7347C469.477 56.0201 468.21 55.91 467.071 55.4208ZM471.47 48.4502C471.228 47.9767 470.811 47.6163 470.307 47.4454C469.841 47.2379 469.313 47.2154 468.83 47.3826C468.358 47.5448 467.958 47.8669 467.699 48.2932L471.627 49.8945C471.703 49.6562 471.728 49.4045 471.701 49.1557C471.674 48.9069 471.595 48.6665 471.47 48.4502Z"
        fill="white"
      />
      <path
        d="M477.035 50.8306C477.258 50.3939 477.577 50.0139 477.969 49.72C478.361 49.426 478.816 49.2259 479.298 49.135C480.366 48.9152 481.528 49.1036 482.848 49.7316C484.168 50.3596 485.079 51.176 485.581 52.1807C485.829 52.6474 485.966 53.1646 485.983 53.6927C485.999 54.2207 485.894 54.7454 485.676 55.2265L482.471 53.6879C482.543 53.5468 482.58 53.3908 482.58 53.2326C482.58 53.0744 482.543 52.9184 482.471 52.7773C482.245 52.5004 481.954 52.2845 481.623 52.1494C480.806 51.7412 480.272 51.804 480.052 52.3064C479.832 52.8087 480.177 53.3111 481.089 53.9391C481.961 54.5599 482.762 55.2761 483.476 56.0742C483.803 56.4722 484.019 56.9484 484.105 57.4558C484.19 58.0129 484.102 58.5826 483.853 59.0885C483.636 59.5531 483.307 59.9568 482.896 60.2636C482.485 60.5704 482.004 60.7708 481.497 60.8469C480.29 61.0261 479.058 60.8172 477.978 60.2503C476.869 59.7427 475.93 58.9247 475.276 57.8954C475 57.4162 474.845 56.8774 474.823 56.3252C474.801 55.7729 474.913 55.2235 475.15 54.7241L478.324 56.2626C478.135 56.922 478.418 57.4558 479.203 57.8326C479.517 58.0128 479.883 58.0793 480.24 58.021C480.38 58.0011 480.513 57.948 480.628 57.866C480.742 57.784 480.836 57.6756 480.9 57.55C481.026 57.2569 480.931 56.9429 480.617 56.608C480.244 56.2042 479.834 55.8363 479.392 55.509C478.889 55.1323 478.386 54.7241 477.884 54.2845C477.412 53.8621 477.065 53.3194 476.878 52.7145C476.773 52.4067 476.732 52.0804 476.759 51.7561C476.786 51.4319 476.88 51.1167 477.035 50.8306Z"
        fill="white"
      />
      <path
        d="M489.827 63.0101L490.896 63.6067L489.482 66.1814L487.942 65.3022C486.811 64.7161 486.088 63.9939 485.774 63.1357C485.46 62.2774 485.617 61.2726 486.246 60.1213L488.539 56.0079L487.628 55.4741L489.042 52.9621L489.953 53.4959L491.304 51.0781L494.32 52.7737L493.001 55.1915L494.98 56.2905L493.566 58.8025L491.587 57.7035L489.293 61.7855C489.042 62.2565 489.199 62.6647 489.827 63.0101Z"
        fill="white"
      />
      <path
        d="M598.024 122.083L550.456 112.852L535.375 146.982C545.214 166.689 550.414 188.384 550.578 210.408C550.741 232.431 545.863 254.2 536.318 274.052L575.246 268.588L592.086 312.64C607.905 283.55 616.682 251.159 617.714 218.067C618.745 184.974 612.001 152.101 598.024 122.083Z"
        fill="#249E19"
      />
      <path
        d="M584.363 155.194C585.117 157.361 585.085 159.339 584.3 161.129C583.514 162.919 582.132 164.174 580.152 164.865C578.173 165.556 576.288 165.399 574.56 164.426C572.832 163.452 571.606 161.914 570.884 159.779L569.281 155.037L582.76 150.453L584.363 155.194ZM572.894 157.455L573.397 158.931C573.549 159.487 573.819 160.005 574.187 160.449C574.556 160.893 575.015 161.253 575.534 161.506C576.639 161.959 577.877 161.971 578.99 161.537C580.146 161.21 581.127 160.444 581.723 159.402C582.257 158.428 582.289 157.298 581.88 156.042L581.378 154.598L572.894 157.455Z"
        fill="white"
      />
      <path
        d="M574.921 173.721C574.713 173 574.653 172.245 574.745 171.5C574.837 170.755 575.078 170.037 575.456 169.388C576.241 168.101 577.466 167.253 579.194 166.845C580.922 166.437 582.368 166.562 583.625 167.347C584.881 168.132 585.73 169.294 586.138 170.895C586.56 172.366 586.391 173.943 585.667 175.291C584.913 176.61 583.687 177.489 581.896 177.929L580.828 178.117L578.912 170.55C578.473 170.731 578.115 171.066 577.906 171.492C577.687 171.895 577.63 172.367 577.749 172.811C577.806 173.133 577.923 173.442 578.095 173.721C578.205 173.871 578.332 174.007 578.472 174.129L579.006 174.412L579.917 178.054C578.76 178.02 577.65 177.587 576.775 176.83C575.842 176.017 575.192 174.928 574.921 173.721ZM583.153 173.187C583.369 172.704 583.414 172.161 583.279 171.649C583.171 171.152 582.88 170.714 582.462 170.424C582.048 170.141 581.549 170.008 581.048 170.047L582.054 174.161C582.293 174.08 582.513 173.953 582.702 173.785C582.891 173.618 583.044 173.415 583.153 173.187Z"
        fill="white"
      />
      <path
        d="M587.736 177.867L588.365 181.51L580.51 185.497L589.244 186.596L589.873 190.239L578.027 188.198L577.273 183.77L587.736 177.867Z"
        fill="white"
      />
      <path
        d="M579.191 198.309C579.034 196.676 579.411 195.263 580.385 194.101C581.359 192.939 582.678 192.311 584.438 192.123C586.197 191.935 587.611 192.343 588.774 193.285C589.347 193.767 589.815 194.361 590.15 195.031C590.485 195.7 590.679 196.431 590.722 197.178C590.912 198.694 590.506 200.225 589.591 201.449C588.679 202.642 587.297 203.301 585.475 203.489C585.11 203.531 584.742 203.541 584.375 203.521L583.621 195.765C583.15 195.876 582.737 196.156 582.458 196.55C582.195 196.925 582.073 197.381 582.113 197.838C582.146 198.16 582.219 198.476 582.333 198.78C582.418 198.948 582.524 199.107 582.647 199.251L583.118 199.596L583.495 203.332C582.361 203.125 581.33 202.538 580.573 201.668C579.77 200.721 579.287 199.546 579.191 198.309ZM587.423 198.968C587.714 198.535 587.837 198.01 587.768 197.492C587.737 196.984 587.517 196.505 587.15 196.151C586.784 195.796 586.298 195.592 585.789 195.577L586.197 199.784C586.448 199.745 586.688 199.651 586.899 199.51C587.111 199.37 587.289 199.184 587.423 198.968Z"
        fill="white"
      />
      <path
        d="M579.906 206.097L594.925 205.438L595.082 208.923L580.063 209.551L579.906 206.097Z"
        fill="white"
      />
      <path
        d="M581.665 213.32C582.765 212.19 584.179 211.656 585.907 211.656C587.635 211.656 589.049 212.284 590.086 213.446C591.097 214.623 591.635 216.134 591.594 217.685C591.605 219.241 591.007 220.739 589.928 221.861C588.86 222.991 587.478 223.525 585.718 223.494C583.959 223.462 582.576 222.866 581.539 221.735C581.013 221.174 580.605 220.512 580.34 219.789C580.075 219.066 579.96 218.297 580 217.528C579.987 216.752 580.127 215.98 580.413 215.258C580.699 214.536 581.124 213.878 581.665 213.32ZM583.676 219.255C584.256 219.74 584.994 219.996 585.75 219.977C586.692 219.977 587.383 219.757 587.886 219.286C588.112 219.071 588.293 218.813 588.417 218.528C588.541 218.242 588.606 217.934 588.609 217.622C588.622 217.306 588.568 216.992 588.448 216.699C588.329 216.406 588.148 216.143 587.918 215.927C587.638 215.674 587.31 215.48 586.954 215.356C586.598 215.232 586.22 215.18 585.844 215.204C585.46 215.171 585.073 215.215 584.706 215.334C584.339 215.453 583.999 215.643 583.707 215.895C583.477 216.108 583.292 216.365 583.162 216.651C583.033 216.937 582.962 217.246 582.953 217.559C582.944 217.877 583.003 218.194 583.128 218.486C583.253 218.779 583.44 219.041 583.676 219.255Z"
        fill="white"
      />
      <path
        d="M589.433 229.734C590.439 230.582 590.847 231.744 590.721 233.188C590.664 233.874 590.46 234.54 590.124 235.141C589.788 235.742 589.328 236.264 588.774 236.673C587.517 237.526 586.004 237.916 584.491 237.779C582.979 237.641 581.561 236.984 580.479 235.92C580.004 235.423 579.642 234.828 579.42 234.177C579.197 233.527 579.119 232.836 579.191 232.152C579.232 231.489 579.444 230.848 579.808 230.292C580.171 229.736 580.673 229.284 581.264 228.98L574.258 228.321L574.572 224.867L591.224 226.406L590.91 229.86L589.433 229.734ZM582.804 233.345C583.052 233.626 583.357 233.852 583.698 234.008C584.038 234.165 584.408 234.249 584.783 234.256C585.157 234.318 585.54 234.302 585.907 234.21C586.275 234.119 586.619 233.952 586.92 233.722C587.174 233.521 587.384 233.269 587.536 232.983C587.687 232.696 587.777 232.381 587.8 232.058C587.852 231.415 587.661 230.777 587.265 230.268C587.017 229.987 586.712 229.761 586.372 229.604C586.031 229.448 585.661 229.363 585.286 229.357C584.912 229.295 584.53 229.311 584.162 229.402C583.795 229.494 583.45 229.661 583.149 229.891C582.895 230.092 582.685 230.344 582.533 230.63C582.382 230.917 582.292 231.232 582.27 231.555C582.217 232.198 582.408 232.836 582.804 233.345Z"
        fill="white"
      />
      <path
        d="M568.186 280.57L529.886 285.971C505.694 326.004 462.587 353.352 412.945 355.644L439.055 389.523L411.469 422.931C484.518 421.141 548.361 382.364 585.026 324.654L568.186 280.57Z"
        fill="#208B16"
      />
      <path
        d="M471.04 365.104C473.114 364.194 475.094 364.005 476.947 364.633C477.87 364.917 478.718 365.402 479.43 366.054C480.142 366.705 480.7 367.507 481.063 368.401C481.482 369.271 481.707 370.221 481.723 371.186C481.739 372.15 481.546 373.107 481.158 373.991C480.372 375.812 478.927 377.162 476.853 378.104L472.297 380.145L466.453 367.145L471.04 365.104ZM474.371 376.314L475.785 375.686C476.979 375.152 477.764 374.367 478.141 373.3C478.485 372.161 478.372 370.934 477.827 369.877C477.303 368.621 476.518 367.773 475.471 367.334C474.929 367.125 474.349 367.033 473.769 367.066C473.19 367.098 472.623 367.254 472.109 367.522L470.726 368.15L474.371 376.314Z"
        fill="white"
      />
      <path
        d="M490.829 371.347C490.187 371.741 489.469 371.996 488.721 372.093C487.974 372.19 487.215 372.129 486.493 371.912C485.048 371.504 483.917 370.499 483.068 368.96C482.22 367.422 482 365.946 482.44 364.533C482.88 363.12 483.791 362.021 485.236 361.205C486.682 360.388 488.158 360.2 489.603 360.577C491.049 360.954 492.243 361.927 493.122 363.528C493.303 363.848 493.45 364.184 493.562 364.533L486.744 368.27C487.017 368.664 487.435 368.935 487.907 369.023C488.351 369.136 488.821 369.08 489.226 368.866C489.525 368.721 489.792 368.518 490.012 368.27C490.135 368.125 490.24 367.967 490.326 367.799C490.382 367.613 490.424 367.425 490.452 367.233L493.751 365.444C494.005 366.564 493.872 367.737 493.374 368.772C492.825 369.882 491.933 370.785 490.829 371.347ZM488.221 363.528C487.704 363.437 487.172 363.526 486.713 363.779C486.255 364.026 485.898 364.426 485.708 364.91C485.549 365.378 485.549 365.886 485.708 366.354L489.446 364.313C489.15 363.908 488.713 363.628 488.221 363.528Z"
        fill="white"
      />
      <path
        d="M493.311 360.733C493.035 360.322 492.863 359.85 492.808 359.358C492.753 358.867 492.818 358.369 492.997 357.907C493.374 356.902 494.16 355.992 495.385 355.207C496.61 354.422 497.773 354.045 498.872 354.139C499.404 354.166 499.922 354.316 500.386 354.577C500.85 354.839 501.246 355.205 501.543 355.646L498.558 357.562C498.477 357.427 498.361 357.315 498.224 357.238C498.086 357.161 497.931 357.121 497.773 357.122C497.418 357.15 497.08 357.281 496.799 357.499C496.013 357.97 495.793 358.472 496.107 358.912C496.422 359.351 497.019 359.32 498.056 358.88C499.031 358.457 500.055 358.151 501.103 357.97C501.61 357.866 502.137 357.921 502.611 358.127C503.149 358.344 503.601 358.73 503.899 359.226C504.178 359.649 504.347 360.134 504.391 360.638C504.435 361.143 504.352 361.65 504.151 362.115C503.687 363.251 502.858 364.201 501.794 364.815C500.796 365.506 499.616 365.888 498.401 365.914C497.851 365.896 497.313 365.747 496.832 365.479C496.351 365.212 495.941 364.833 495.636 364.375L498.621 362.46C499.061 362.962 499.658 362.994 500.381 362.523C500.697 362.352 500.942 362.074 501.072 361.738C501.141 361.61 501.172 361.465 501.161 361.32C501.15 361.175 501.097 361.037 501.009 360.921C500.852 360.67 500.538 360.576 500.098 360.67C499.548 360.783 499.011 360.952 498.495 361.173L496.673 361.801C496.07 361.978 495.433 362 494.819 361.863C494.506 361.784 494.211 361.643 493.952 361.449C493.693 361.255 493.475 361.012 493.311 360.733Z"
        fill="white"
      />
      <path
        d="M503.177 348.44C503.151 348.707 503.064 348.966 502.922 349.194C502.78 349.423 502.588 349.616 502.36 349.759C502.149 349.923 501.906 350.042 501.646 350.107C501.386 350.172 501.116 350.181 500.852 350.135C500.585 350.093 500.33 349.996 500.103 349.85C499.876 349.704 499.682 349.513 499.533 349.288C499.366 349.078 499.246 348.835 499.181 348.575C499.116 348.315 499.107 348.044 499.155 347.78C499.25 347.253 499.542 346.781 499.972 346.462C500.189 346.3 500.436 346.184 500.699 346.12C500.962 346.056 501.235 346.045 501.502 346.089C501.769 346.133 502.025 346.231 502.253 346.376C502.481 346.52 502.678 346.71 502.832 346.933C502.986 347.149 503.096 347.393 503.155 347.652C503.215 347.91 503.222 348.178 503.177 348.44ZM507.984 360.529L501.355 351.329L504.183 349.319L510.781 358.488L507.984 360.529Z"
        fill="white"
      />
      <path
        d="M513.29 354.833C511.813 354.644 510.556 353.859 509.488 352.572C508.42 351.285 507.917 349.872 508.011 348.396C508.036 347.703 508.213 347.024 508.527 346.406C508.842 345.787 509.288 345.245 509.833 344.817C510.31 344.384 510.888 344.079 511.515 343.931C512.141 343.783 512.795 343.796 513.415 343.969L512.473 342.807L515.175 340.609L522.275 349.338C523.227 350.412 523.752 351.797 523.752 353.232C523.753 353.97 523.558 354.696 523.186 355.335C522.729 356.089 522.144 356.758 521.458 357.313C520.118 358.423 518.83 359.019 517.594 359.103C516.337 359.197 515.237 358.726 514.201 357.69L516.84 355.524C517.657 355.995 518.536 355.806 519.542 354.99C519.775 354.816 519.969 354.595 520.11 354.34C520.251 354.086 520.336 353.804 520.359 353.514C520.422 352.918 520.17 352.258 519.573 351.536L518.599 350.311C518.663 350.977 518.558 351.648 518.295 352.262C518.031 352.877 517.617 353.415 517.091 353.828C516.569 354.263 515.959 354.578 515.302 354.752C514.645 354.925 513.958 354.953 513.29 354.833ZM516.777 349.275C516.803 348.899 516.752 348.521 516.628 348.165C516.504 347.809 516.308 347.482 516.054 347.203C515.834 346.896 515.553 346.637 515.229 346.443C514.905 346.248 514.544 346.122 514.169 346.073C513.85 346.037 513.527 346.066 513.219 346.157C512.912 346.249 512.626 346.402 512.378 346.606C511.878 347.024 511.562 347.622 511.499 348.271C511.404 348.961 511.656 349.652 512.221 350.343C512.441 350.65 512.722 350.909 513.047 351.103C513.371 351.298 513.732 351.424 514.106 351.473C514.749 351.529 515.39 351.338 515.897 350.939C516.398 350.522 516.714 349.924 516.777 349.275Z"
        fill="white"
      />
      <path
        d="M519.795 336.335L520.957 337.529C520.643 336.178 521.052 334.954 522.183 333.886C522.591 333.462 523.086 333.131 523.633 332.914C524.18 332.697 524.768 332.601 525.356 332.63C526.55 332.693 527.65 333.258 528.687 334.326L533.243 339.099L530.76 341.485L526.519 337.058C526.114 336.584 525.537 336.291 524.916 336.241C524.623 336.234 524.331 336.289 524.06 336.402C523.789 336.516 523.545 336.686 523.345 336.901C523.129 337.096 522.953 337.333 522.829 337.597C522.705 337.861 522.635 338.148 522.623 338.439C522.651 339.061 522.922 339.647 523.377 340.072L527.65 344.499L525.136 346.917L517.281 338.753L519.795 336.335Z"
        fill="white"
      />
      <path
        d="M424.406 389.401L398.517 422.997H0L25.8891 389.401L0 355.805H398.517L424.406 389.401Z"
        fill="#197111"
      />
      <path
        d="M183.012 388.818C182.99 389.83 182.624 390.804 181.975 391.581C181.54 392.096 180.976 392.486 180.341 392.711C179.518 393.018 178.644 393.167 177.765 393.151H175.628V398.677H172.141V384.453H177.765C179.43 384.453 180.718 384.893 181.629 385.741C182.066 386.125 182.415 386.599 182.654 387.13C182.892 387.66 183.014 388.236 183.012 388.818ZM175.628 390.513H177.765C177.995 390.524 178.226 390.488 178.442 390.407C178.658 390.326 178.855 390.202 179.022 390.042C179.175 389.879 179.293 389.686 179.369 389.476C179.445 389.265 179.476 389.041 179.461 388.818C179.473 388.354 179.304 387.903 178.99 387.562C178.831 387.401 178.639 387.276 178.428 387.195C178.217 387.114 177.991 387.078 177.765 387.091H175.628V390.513Z"
        fill="white"
      />
      <path
        d="M185.094 398.657V383.648H188.581V398.657H185.094Z"
        fill="white"
      />
      <path
        d="M192.293 397.185C191.288 396.117 190.785 394.736 190.785 393.04C190.728 391.506 191.269 390.009 192.293 388.864C192.747 388.345 193.308 387.931 193.937 387.649C194.566 387.367 195.249 387.225 195.938 387.231C196.583 387.199 197.226 387.329 197.807 387.608C198.389 387.888 198.891 388.309 199.269 388.833V387.357H202.725V398.661H199.269V397.028C198.906 397.587 198.407 398.044 197.819 398.357C197.231 398.67 196.573 398.829 195.907 398.818C195.222 398.824 194.544 398.682 193.92 398.4C193.296 398.118 192.741 397.703 192.293 397.185ZM198.514 395.081C199.017 394.621 199.269 393.93 199.269 393.009C199.293 392.635 199.239 392.26 199.109 391.908C198.979 391.556 198.777 391.236 198.514 390.968C198.292 390.734 198.024 390.55 197.726 390.425C197.429 390.301 197.109 390.24 196.786 390.246C196.465 390.243 196.146 390.306 195.849 390.43C195.552 390.555 195.283 390.737 195.058 390.968C194.556 391.449 194.304 392.14 194.304 393.04C194.28 393.414 194.334 393.789 194.464 394.141C194.594 394.493 194.796 394.813 195.058 395.081C195.283 395.312 195.552 395.495 195.849 395.619C196.146 395.743 196.465 395.806 196.786 395.803C197.109 395.809 197.429 395.748 197.726 395.624C198.024 395.5 198.292 395.315 198.514 395.081Z"
        fill="white"
      />
      <path
        d="M209.253 387.355V389.019C209.976 387.826 211.107 387.229 212.678 387.229C213.263 387.208 213.846 387.312 214.388 387.534C214.93 387.756 215.418 388.091 215.82 388.517C216.637 389.396 217.045 390.557 217.045 392.065V398.658H213.589V392.536C213.613 392.23 213.575 391.922 213.478 391.63C213.381 391.339 213.226 391.07 213.023 390.84C212.811 390.637 212.559 390.48 212.284 390.377C212.008 390.275 211.715 390.229 211.421 390.243C211.127 390.223 210.832 390.266 210.555 390.369C210.279 390.472 210.028 390.632 209.819 390.84C209.616 391.07 209.461 391.339 209.364 391.63C209.267 391.922 209.229 392.23 209.253 392.536V398.658H205.766V387.355H209.253Z"
        fill="white"
      />
    </svg>
  );
};
