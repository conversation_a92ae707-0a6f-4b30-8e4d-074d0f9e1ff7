import React from "react";
import { IdeaSection } from "./IdeaSection";
import { WorkSection } from "./WorkSection";
import HeroSection from "./HeroSection";
import IndustrySection from "@/common/IndustrySection";
import { TechStackSection } from "../home/<USER>";
import { HiringSection } from "../home/<USER>";
import { BenefitsSection } from "../home/<USER>";
import { FaqSection } from "@/common/FaqSection";
import { BlogSection } from "../home/<USER>";
import CTASection from "@/common/CTASection";
import { ClientSection } from "./ClientSection";
import { BusinessSection } from "./BusinessSection";
import { fullIconsList, serviceBenefitsData } from "@/utils/constant";
import { ServicesSection } from "./ServiceSection";
import { TestimonialsSection } from "../home/<USER>";

const heroStats = [
  { value: "40", sign: "+", label: "Happy end-users" },
  { value: "3", sign: "+", label: "Years in business" },
  { value: "30", sign: "+", label: "Industry Experts" },
];

const Servicepage = () => {
  return (
    <>
      <HeroSection
        title="Build Smarter, Scale Faster"
        subTitle="From your first idea to global growth, OneBuild delivers custom tech solutions designed for startups and businesses ready to lead. Premium quality. Startup-friendly pricing. Transparent delivery — every step of the way."
        btnText="Explore Our Services"
        titleClassName="!tw-bg-[linear-gradient(90.32deg,#E5DFF2_0.08%,#BEB5DE_48.95%,#BCB3DD_52.1%,#7E71BD_98.53%)]"
        className="tw-bg-serviceHeroBg tw-pt-[2rem] md:tw-pt-[5rem] lg:tw-pt-[7rem] xl:tw-pt-[9rem] 2xl:tw-pt-[12rem]"
      />
      <ServicesSection className="tw-bg-primary_color md:tw-pt-[100px] tw-pt-[70px] tw-pb-[50px]" />
      {/* <IdeaSection className="md:tw-py-[100px] tw-py-[60px]" /> */}
      {/* <IndustrySection
        className=""
        iconsData={fullIconsList}
        title="Innovating Across 20+ Industries"
      /> */}
      {/* <TechStackSection className="md:tw-py-[100px] tw-py-[70px]" /> */}
      {/* <WorkSection className="md:tw-py-[100px] tw-pt-[70px] tw-pb-[50px]" /> */}
      <BenefitsSection
        className="md:tw-py-[100px] tw-py-[30px]"
        data={serviceBenefitsData}
      />
      {/* <ClientSection className="md:tw-py-[100px] tw-py-[50px]" /> */}
      {/* <BusinessSection className="md:tw-py-[100px] tw-py-[50px]" /> */}
      <TestimonialsSection
        isButton={true}
        className="md:tw-py-[100px] tw-py-[70px]"
        blackTitle="Proven Results, "
        greenTitle="Trusted Client "
        subTitle="We don't just deliver software. We build long-term  partnerships. Here’s what our clients say about working with  OneBuild"
      />
      <FaqSection className="md:tw-py-[100px] tw-py-[40px]" />
      <CTASection
        title="Turn Your Idea Into Reality"
        description="Every great business starts with the decision to build. Let’s create yours.."
        buttonText="Build Your Project!"
        descriptionWidth="lg:tw-w-[60%] md:tw-w-3/4 tw-w-[90%]"
      />
      {/* <BlogSection className="md:tw-py-[50px] tw-pt-[70px] tw-pb-[40px]" /> */}
    </>
  );
};

export default Servicepage;
