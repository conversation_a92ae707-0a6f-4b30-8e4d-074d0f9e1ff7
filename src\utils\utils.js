import img_4 from "../../public/logo/logo.png";

// export const animationCreate = () => {
//   if (typeof window !== "undefined") {
//     window.WOW = require("wowjs");
//   }
//   new WOW.WOW({ live: false }).init();
// };


export const alphbetNumber = ["one", "two", "three", "four", "five"]


export const fetchfeatureCardList = (listData, box_1) => {

  const finalData = {}
  const card1 = "/assets/img/payment/Frame52.png"
  const ff = box_1?.map((itm, index) => {

    return {
      ...itm,
      title: listData?.[index]?.featureintro?.title || itm?.title,
      des: listData?.[index]?.featureintro?.description || itm?.des,
      width: listData?.[index]?.width || itm?.width,
      cardbg: card1
    }
  })
  return ff

}

export const totlLogCount = (prevData) => {
  return (+prevData?.cpuhistory + +prevData?.serverlog + +prevData?.crashlog + +prevData?.cutomelog + +prevData?.apilog)?.toFixed(0)
}

export const totlLogPrice = (val) => {
  return (+(+val?.apilog * +val?.apiBackupConst) + +(+val?.cutomelog * +val?.customLogConst) + +(+val?.crashlog * +val?.errorLogConst) + +(+val?.serverlog * +val?.serverActivityConst) + +(+val?.cpuhistory * val?.cpuHistoryConst))?.toFixed(2)
}

export const createStaticData = (apiData) => {
  const totalDatabaseRow = 200000;
  const currentBilling = 17000;
  const developmentCost = 50000;
  const dayofEstimate = 30;
  const logCapacityperDay = totalDatabaseRow * 7;
  const logCapacityperDayEstimate = logCapacityperDay * dayofEstimate;
  const estimatedCostperLog = currentBilling * logCapacityperDayEstimate;
  const backupDays = 7;
  const historylogDuration = 15;
  const cpuhistory = ((60 / historylogDuration) * 24) * dayofEstimate;
  const serverlog = 10 * dayofEstimate;
  const crashlog = 10 * dayofEstimate;
  const cutomelog = 80 * dayofEstimate;
  const apilog = 500 * dayofEstimate;
  const totlLogperClient = (cpuhistory + serverlog + crashlog + cutomelog + apilog) * dayofEstimate;

  const perPriceData = apiData?.find(
    (el) => el?.backUpDaysConst === backupDays
  );

  const totalLog = (cpuhistory + serverlog + crashlog + cutomelog + apilog);
  const totalcpuhistoryLogPrice = perPriceData?.cpuHistoryConst * cpuhistory;
  const totalserverlogLogPrice = perPriceData?.serverActivityConst * serverlog;
  const totalcrashlogLogPrice = perPriceData?.errorLogConst * crashlog;
  const totalcutomelogLogPrice = perPriceData?.customLogConst * cutomelog;
  const totalapilogLogPrice = perPriceData?.apiBackupConst * apilog;
  const totalAlllogPrice = totalcpuhistoryLogPrice + totalserverlogLogPrice + totalcrashlogLogPrice + totalcutomelogLogPrice + totalapilogLogPrice;
  delete perPriceData?.id;
  delete perPriceData?.createdAt;
  delete perPriceData?.updatedAt;
  delete perPriceData?.deletedAt;

  return {
    totalDatabaseRow,
    currentBilling,
    developmentCost,
    dayofEstimate,
    logCapacityperDay,
    logCapacityperDayEstimate,
    estimatedCostperLog,
    backupDays,
    historylogDuration,
    cpuhistory,
    serverlog,
    crashlog,
    cutomelog,
    apilog,
    totlLogperClient,
    totalLog,
    totalAlllogPrice,
    ...perPriceData
  };
}



const shimmer = (w, h) => `
    svg width=&quot;${w}&quot; height=&quot;${h}&quot; version=&quot;1.1&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot; xmlns:xlink=&quot;http://www.w3.org/1999/xlink&quot;
      defs
        linearGradient id=&quot;g&quot;
          stop stop-color=&quot;#CFCFCF&quot; offset=&quot;20%&quot; /
          stop stop-color=&quot;#CFCFCF&quot; offset=&quot;50%&quot; /
          stop stop-color=&quot;#CFCFCF&quot; offset=&quot;70%&quot; /
        /linearGradient
      /defs
      rect width=&quot;${w}&quot; height=&quot;${h}&quot; fill=&quot;#CFCFCF&quot; /
      rect id=&quot;r&quot; width=&quot;${w}&quot; height=&quot;${h}&quot; fill=&quot;url(#g)&quot; /
      />animate xlink:href=&quot;#r&quot; attributeName=&quot;x&quot; from=&quot;-${w}&quot; to=&quot;${w}&quot; dur=&quot;1s&quot; repeatCount=&quot;indefinite&quot;  /
    /svg
`;
const toBase64 = (str) =>
  typeof window === "undefined"
    ? Buffer.from(str).toString("base64")
    : window.btoa(str);

export const blurDataURL = (width, height) =>
  `data:image/svg+xml;base64,${toBase64(shimmer(width, height))}`;

// office location data CONTACT US
export const office_data = [
  {
    id: 1,
    cls: "",
    img: img_4,
    cls: "p-relative",
    location: "Ahmedabad",
    badge: "Main Headquarter",
    address: (
      <>
        Titanium Heights, A-1206, Corporate Rd, opp. Vodafone House, Prahlad
        Nagar
      </>
    ),
  },
  {
    id: 2,

    img: img_4,
    // badge: "Main Office",

    location: "Surat",
    address: (
      <>
        Titanium Heights, A-1206, Corporate Rd, opp. Vodafone House, Prahlad
        Nagar
      </>
    ),
  },
  // {
  //   id: 3,
  //   cls: "",
  //   img: img_3,
  //   location: "Egypt",
  //   address: (
  //     <>
  //       Av. Cordoba 1309, 3&apos;A, City of <br /> Buenos Aires, Egypt
  //     </>
  //   ),
  // },
];
