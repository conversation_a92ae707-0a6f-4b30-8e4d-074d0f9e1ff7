"use client";
import { ResourceCard } from "@/components/card/ResourceCard";
import React, { useRef, useState } from "react";
import Slider from "react-slick";

const ResourcesSlider = ({ resources }) => {
  const settings = {
    dots: true,
    arrows: false,
    infinite: false,
    speed: 500,
    slidesToShow: 3, // default for >1024px
    slidesToScroll: 3,
    responsive: [
      {
        breakpoint: 1024, // applies when width <= 1024px
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 590, // applies when width <= 450px
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <>
      <Slider {...settings}>
        {resources.map((resource, idx) => (
          <div key={idx} className="lg:tw-p-3 tw-p-2">
            <ResourceCard
              image={resource.image}
              category={resource.category}
              fileType={resource.fileType}
              fileTypeColor={resource.fileTypeColor}
              fileTypeBorderColor={resource.fileTypeBorderColor}
              title={resource.title}
            />
          </div>
        ))}
      </Slider>
    </>
  );
};

export default ResourcesSlider;
