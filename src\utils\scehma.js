import { LIVE_URL } from "@/config/config";
import { convertEmbedToContentUrl, fetchUploadDate, toCapitalize } from "./function";

export const homeScehma = (faqListData, homeData) => (
    <>
        <script
            type="application/ld+json"
            id="application/ld+jsonFAQMain"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org",
                    "@type": "FAQPage",
                    mainEntity: faqListData?.faqs?.map((el, index) => {
                        return {
                            "@type": "Question",
                            name: el?.question,
                            acceptedAnswer: {
                                "@type": "Answer",
                                text: stripATags(el?.answer),
                            },
                        };
                    }),
                }),
            }}
        />
        <script
            type="application/ld+json"
            id="application/ld+jsonOrg"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": homeData?.organizationschema?.type ?? "Organization",
                    "@id": homeData?.organizationschema?.type
                        ? `#${homeData?.organizationschema?.type}`
                        : "#Organization",
                    url: homeData?.organizationschema?.url ?? "https://tsttechnology.in/",
                    address: {
                        "@type": "PostalAddress",
                        streetAddress: homeData?.organizationschema?.address,
                        addressLocality: homeData?.organizationschema?.city,
                        addressRegion: homeData?.organizationschema?.state,
                        postalCode: homeData?.organizationschema?.pincode,
                        addressCountry: homeData?.organizationschema?.country,
                    },
                    legalName: homeData?.organizationschema?.name ?? "TST Technology",
                    name: homeData?.organizationschema?.name ?? "TST Technology",
                    description:
                        homeData?.organizationschema?.description ??
                        "TST Technology provides real-time server monitoring to prevent downtime and ensure optimal performance for your business-critical applications.",
                    logo:
                        homeData?.organizationschema?.logo?.data?.attributes?.url ??
                        `https://tst-strapi.s3.ap-south-1.amazonaws.com/TS_Tlogo_fec6529d2b.png`,
                    email: homeData?.organizationschema?.email ?? "<EMAIL>",
                    sameAs: homeData?.organizationschema?.socialmedialist
                        ? homeData?.organizationschema?.socialmedialist.split(",")
                        : ["https://www.linkedin.com/showcase/TST Technology"],
                    // image: headerPartData?.linkimage?.data?.attributes?.url,
                }),
            }}
        />
        <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": homeData?.webSiteschema?.type ?? "WebSite",
                    name: homeData?.webSiteschema?.name ?? "TST Technology",
                    url: homeData?.webSiteschema?.url ?? "https://tsttechnology.in/",
                    logo:
                        homeData?.organizationschema?.logo?.data?.attributes?.url ??
                        `https://tst-strapi.s3.ap-south-1.amazonaws.com/TS_Tlogo_fec6529d2b.png`,
                    potentialAction: {
                        "@type": "SearchAction",
                        target: `${homeData?.webSiteschema?.url ?? "https://tsttechnology.in/"
                            }{search_term_string}`,
                        "query-input": "required name=search_term_string",
                    },
                }),
            }}
        />
    </>
);

export const aboutScehma = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "About TST Technology",
                            item: LIVE_URL
                                ? `${LIVE_URL}/about`
                                : "https://tsttechnology.in/about",
                        },
                    ],
                }),
            }}
        />

    </>

}

export const ourTeamScehma = (faqListData) => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Our Team",
                            item: LIVE_URL
                                ? `${LIVE_URL}/our-team`
                                : "https://tsttechnology.in/our-team",
                        },
                    ],
                }),
            }}
        />
        <script
            type="application/ld+json"
            id="application/ld+jsonFAQMain"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org",
                    "@type": "FAQPage",
                    mainEntity: faqListData?.faqs?.map((el, index) => {
                        return {
                            "@type": "Question",
                            name: el?.question,
                            acceptedAnswer: {
                                "@type": "Answer",
                                text: stripATags(el?.answer),
                            },
                        };
                    }),
                }),
            }}
        />

    </>

}

export const cultureValueScehma = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Culture & Value",
                            item: LIVE_URL
                                ? `${LIVE_URL}/our-team`
                                : "https://tsttechnology.in/culture-value",
                        },
                    ],
                }),
            }}
        />


    </>

}

export const agileMindsetScehma = (faqListData) => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Agile Mindset",
                            item: LIVE_URL
                                ? `${LIVE_URL}/agile-mindset`
                                : "https://tsttechnology.in/agile-mindset",
                        },
                    ],
                }),
            }}
        />
    </>

}

export const journeyScehma = (faqListData) => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Our Memorable Journey",
                            item: LIVE_URL
                                ? `${LIVE_URL}/journey`
                                : "https://tsttechnology.in/journey",
                        },
                    ],
                }),
            }}
        />
    </>

}

export const serviceScehma = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Our Top Service",
                            item: LIVE_URL
                                ? `${LIVE_URL}/service`
                                : "https://tsttechnology.in/service",
                        },
                    ],
                }),
            }}
        />
    </>

}

export const portfolioScehma = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Our Portfolio",
                            item: LIVE_URL
                                ? `${LIVE_URL}/portfolio`
                                : "https://tsttechnology.in/portfolio",
                        },
                    ],
                }),
            }}
        />
    </>

}

export const blogScehma = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Blog",
                            item: LIVE_URL
                                ? `${LIVE_URL}/blog`
                                : "https://tsttechnology.in/blog",
                        },
                    ],
                }),
            }}
        />
    </>
}

export const newsRoomScehma = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Blog",
                            item: LIVE_URL
                                ? `${LIVE_URL}/blog`
                                : "https://tsttechnology.in/happenings-at-tst-technology",
                        },
                    ],
                }),
            }}
        />
    </>

}
const stripATags = (htmlString) => {
    if (typeof window === "undefined") return htmlString; // Check if running on the server
    const tempElement = document.createElement("div");
    tempElement.innerHTML = htmlString;
    const aTags = tempElement.querySelectorAll("a");
    aTags.forEach((aTag) => {
        const textNode = document.createTextNode(aTag.textContent);
        aTag.parentNode.replaceChild(textNode, aTag);
    });
    return tempElement.innerHTML;
};

export const newsRoomListScehma = async (data, slug) => {
    const formatDateWithTimeZone = (dateString) => {
        const date = new Date(dateString);
        return date.toISOString();
    };
    // console.log(data?.videoList, "jmdfjdbgj")

    const datePublished = data?.newsDetail?.date
        ? formatDateWithTimeZone(data.newsDetail.date)
        : data?.publishedAt
            ? formatDateWithTimeZone(data.publishedAt)
            : null;

    const dateModified = data?.newsDetail?.date
        ? formatDateWithTimeZone(data.newsDetail.date)
        : data?.updatedAt
            ? formatDateWithTimeZone(data.updatedAt)
            : null;

    const videoList = data?.newsDetail?.length > 0
        ? await Promise.all(data?.newsDetail?.videoList?.map(async (el) => {
            if (!el?.link) return;
            const contentUrl = convertEmbedToContentUrl(el?.link);
            const videoId = new URL(el?.link)?.pathname.split('/')[2];
            const { uploadDate, description } = await fetchUploadDate(el?.link);

            return {
                "@type": "VideoObject",
                "name": el?.title ?? "",
                "description": description ?? el?.title ?? "Default Description",
                "thumbnailUrl": `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
                "uploadDate": uploadDate ?? "Unknown Date",
                // "duration": "PT2M30S",
                "contentUrl": contentUrl ?? "",
                "embedUrl": el?.link ?? ""
            };
        }))
        : [];


    return (
        <>
            <script
                type="application/ld+json"
                id="application/ld+jsonTool"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org/",
                        "@type": "BreadcrumbList",
                        itemListElement: [
                            {
                                "@type": "ListItem",
                                position: 1,
                                name: "TST Technology",
                                item: LIVE_URL ?? "https://tsttechnology.in/",
                            },
                            {
                                "@type": "ListItem",
                                position: 2,
                                name: "Blog | TST Technology",
                                item: LIVE_URL
                                    ? `${LIVE_URL}/newsroom`
                                    : "https://tsttechnology.in/newsroom",
                            },
                            {
                                "@type": "ListItem",
                                position: 3,
                                name: data?.newsDetail?.title || "Blog | TST Technology",
                                item: `https://tsttechnology.in/newsroom/${slug}`,
                            },
                        ],
                    }),
                }}
            />
            <script
                type="application/ld+json"
                id="application/ld+jsonNewsArticle"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "NewsArticle",
                        headline: data?.newsDetail?.title,
                        "description": data?.seodata?.summary,
                        image: [
                            data?.newsDetail?.heroimg?.data?.attributes?.url,
                            data?.newsDetail?.sectionimg?.data?.attributes?.url,
                        ],
                        datePublished: datePublished,
                        dateModified: dateModified,
                        publisher: {
                            "@type": "Organization",
                            name: "TST Technology",
                            logo: {
                                "@type": "ImageObject",
                                "url": "https://tst-strapi.s3.ap-south-1.amazonaws.com/TS_Tlogo_fec6529d2b.png"
                            }
                        },
                        // author: [
                        //     {
                        //         "@type": "Person",
                        //         name: data?.newsDetail?.writerinfo?.title || "",
                        //         url: data?.newsDetail?.writerinfo?.image?.data?.attributes?.url ?? "",
                        //     },
                        // ],

                        video: videoList
                    }),
                }}
            />
        </>
    )
}

export const blogListScehma = async (blogDetail, slug) => {
    const formatDateWithTimeZone = (dateString) => {
        const date = new Date(dateString);
        return date.toISOString();
    };
    // console.log(blogDetail?.videoList, "jmdfjdbgj")

    const datePublished = blogDetail?.blog?.date
        ? formatDateWithTimeZone(blogDetail.blog.date)
        : blogDetail?.publishedAt
            ? formatDateWithTimeZone(blogDetail.publishedAt)
            : null;

    const dateModified = blogDetail?.blog?.date
        ? formatDateWithTimeZone(blogDetail.blog.date)
        : blogDetail?.updatedAt
            ? formatDateWithTimeZone(blogDetail.updatedAt)
            : null;

    const videoList = blogDetail?.videoList?.length > 0
        ? await Promise.all(blogDetail.videoList.map(async (el) => {
            if (!el?.link) return;
            const contentUrl = convertEmbedToContentUrl(el?.link);
            const videoId = new URL(el?.link)?.pathname.split('/')[2];
            const { uploadDate, description } = await fetchUploadDate(el?.link);

            return {
                "@type": "VideoObject",
                "name": el?.title ?? "",
                "description": description ?? el?.title ?? "Default Description",
                "thumbnailUrl": `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
                "uploadDate": uploadDate ?? "Unknown Date",
                // "duration": "PT2M30S",
                "contentUrl": contentUrl ?? "",
                "embedUrl": el?.link ?? ""
            };
        }))
        : [];

    return (
        <>
            {blogDetail?.BlogFaq?.faqs?.length > 0 && (
                <script
                    type="application/ld+json"
                    id="application/ld+jsonFAQMain"
                    dangerouslySetInnerHTML={{
                        __html: JSON.stringify({
                            "@context": "https://schema.org",
                            "@type": "FAQPage",
                            mainEntity: blogDetail?.BlogFaq?.faqs?.map((el, index) => {
                                return {
                                    "@type": "Question",
                                    name: el?.question,
                                    acceptedAnswer: {
                                        "@type": "Answer",
                                        text: stripATags(el?.answer ?? el?.answerwithList),
                                    },
                                };
                            }),
                        }),
                    }}
                />
            )}
            <script
                type="application/ld+json"
                id="application/ld+jsonTool"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org/",
                        "@type": "BreadcrumbList",
                        itemListElement: [
                            {
                                "@type": "ListItem",
                                position: 1,
                                name: "TST Technology",
                                item: LIVE_URL ?? "https://tsttechnology.in/",
                            },
                            {
                                "@type": "ListItem",
                                position: 2,
                                name: "Blog | TST Technology",
                                item: LIVE_URL
                                    ? `${LIVE_URL}/blog`
                                    : "https://tsttechnology.in/blog",
                            },
                            {
                                "@type": "ListItem",
                                position: 3,
                                name: blogDetail?.blog?.title || "Blog | TST Technology",
                                item: `https://tsttechnology.in/blog/${slug}`,
                            },
                        ],
                    }),
                }}
            />
            <script
                type="application/ld+json"
                id="application/ld+jsonBlogPosting"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "BlogPosting",
                        headline: blogDetail?.blog?.title,
                        image: [
                            blogDetail?.blog?.heroimg?.data?.attributes?.url,
                            blogDetail?.blog?.sectionimg?.data?.attributes?.url,
                        ],
                        datePublished: datePublished,
                        dateModified: dateModified,
                        author: [
                            {
                                "@type": "Person",
                                name: blogDetail?.blog?.writerinfo?.title || "",
                                url: blogDetail?.blog?.writerinfo?.image?.data?.attributes?.url ?? "",
                            },
                        ],
                        video: videoList
                    }),
                }}
            />
        </>
    );
};


export const serviceListScehma = async (blogDetail, slug) => {

    const videoList = blogDetail?.videoList?.length > 0
        ? await Promise.all(blogDetail.videoList.map(async (el) => {
            if (!el?.link) return;
            const contentUrl = convertEmbedToContentUrl(el?.link);
            const videoId = new URL(el?.link)?.pathname.split('/')[2];
            const { uploadDate, description } = await fetchUploadDate(el?.link);

            return {
                "@type": "VideoObject",
                "name": el?.title ?? "",
                "description": description ?? el?.title ?? "Default Description",
                "thumbnailUrl": `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
                "uploadDate": uploadDate ?? "Unknown Date",
                // "duration": "PT2M30S",
                "contentUrl": contentUrl ?? "",
                "embedUrl": el?.link ?? ""
            };
        }))
        : [];

    return (
        <>
            {blogDetail?.FaqArea?.faqs?.length > 0 && (
                <script
                    type="application/ld+json"
                    id="application/ld+jsonFAQMain"
                    dangerouslySetInnerHTML={{
                        __html: JSON.stringify({
                            "@context": "https://schema.org",
                            "@type": "FAQPage",
                            mainEntity: blogDetail?.FaqArea?.faqs?.map(
                                (el, index) => {
                                    return {
                                        "@type": "Question",
                                        name: el?.question,
                                        acceptedAnswer: {
                                            "@type": "Answer",
                                            text: stripATags(el?.answer ?? el?.answerwithList),
                                        },
                                    };
                                }
                            ),
                        }),
                    }}
                />
            )}
            <script
                type="application/ld+json"
                id="application/ld+jsonTool"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org/",
                        "@type": "BreadcrumbList",
                        itemListElement: [
                            {
                                "@type": "ListItem",
                                position: 1,
                                name: "TST Technology",
                                item:
                                    LIVE_URL ??
                                    "https://tsttechnology.in/",
                            },
                            {
                                "@type": "ListItem",
                                position: 2,
                                name: "Service | TST Technology",
                                item: LIVE_URL
                                    ? `${LIVE_URL}/service`
                                    : "https://tsttechnology.in/service",
                            },
                            {
                                "@type": "ListItem",
                                position: 3,
                                name: blogDetail?.featureintro?.title || "Service | TST Technology",
                                item: `https://tsttechnology.in/service/${slug}`,
                            },
                        ],
                    }),
                }}
            />
            <script
                type="application/ld+json"
                id="application/ld+jsonBlogPosting"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "Service",
                        headline: blogDetail?.cards?.featureintro?.title,

                        video: videoList,
                        // image: [image],
                        // datePublished:
                        //     blogDetail?.blog?.date ??
                        //     blogDetail?.publishedAt,
                        // dateModified:
                        //     blogDetail?.blog?.date ??
                        //     blogDetail?.updatedAt,
                        // author: [
                        //     {
                        //         "@type": "Person",
                        //         name: blogDetail?.blog?.writerinfo?.title || "",
                        //         url:
                        //             blogDetail?.blog?.writerinfo?.image?.data
                        //                 ?.attributes?.url ?? "",
                        //     },
                        //     // {
                        //     //   "@type": "Person",
                        //     //   name: data?.data?.Admin?.name || '',
                        //     //   url: `${NEXT_PUBLIC_CDN}/asset/commentUser.avif`,
                        //     // },
                        // ],
                    }),
                }}
            />
        </>
    );
}

export const portfolioListScehma = (blogDetail, slug) => (
    <>
        {/* {blogDetail?.CoreFeature?.faqs?.length > 0 && (
            <script
                type="application/ld+json"
                id="application/ld+jsonFAQMain"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "FAQPage",
                        mainEntity: blogDetail?.FaqArea?.faqs?.map(
                            (el, index) => {
                                return {
                                    "@type": "Question",
                                    name: el?.question,
                                    acceptedAnswer: {
                                        "@type": "Answer",
                                        text: stripATags(el?.answer ?? el?.answerwithList),
                                    },
                                };
                            }
                        ),
                    }),
                }}
            />
        )} */}
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Portfolio | TST Technology",
                            item: LIVE_URL
                                ? `${LIVE_URL}/portfolio`
                                : "https://tsttechnology.in/portfolio",
                        },
                        {
                            "@type": "ListItem",
                            position: 3,
                            name: blogDetail?.HeroTitle || "Portfolio | TST Technology",
                            item: `https://tsttechnology.in/portfolio/${slug}`,
                        },
                    ],
                }),
            }}
        />
        {/* <script
            type="application/ld+json"
            id="application/ld+jsonBlogPosting"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org",
                    "@type": "BlogPosting",
                    headline: blogDetail?.blog?.title,
                    // image: [image],
                    datePublished:
                        blogDetail?.blog?.date ??
                        blogDetail?.publishedAt,
                    dateModified:
                        blogDetail?.blog?.date ??
                        blogDetail?.updatedAt,
                    author: [
                        {
                            "@type": "Person",
                            name: blogDetail?.blog?.writerinfo?.title || "",
                            url:
                                blogDetail?.blog?.writerinfo?.image?.data
                                    ?.attributes?.url ?? "",
                        },
                        // {
                        //   "@type": "Person",
                        //   name: data?.data?.Admin?.name || '',
                        //   url: `${NEXT_PUBLIC_CDN}/asset/commentUser.avif`,
                        // },
                    ],
                }),
            }}
        /> */}
    </>
);

export const careerSchema = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "TST Technology Careers",
                            item: LIVE_URL
                                ? `${LIVE_URL}/career`
                                : "https://tsttechnology.in/career",
                        },
                    ],
                }),
            }}
        />

    </>

}

export const contactScehma = (contactPageData) => {
    const schemaData = {
        "@context": "https://schema.org/",
        "@type": "ContactPage",
        "name": "TST Technology",
        "url": "https://tsttechnology.in/contact",
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "TST Technology",
                    "item": "https://tsttechnology.in/",
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Contact",
                    "item": "https://tsttechnology.in/contact",
                },
            ],
        },
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": contactPageData?.MobileNum ?? "+91 98255 82469",  // Update with your actual contact number
            "contactType": "Customer Support",
            "email": contactPageData?.emailtext ?? "<EMAIL>",  // Update with your actual email
            "areaServed": "Global",
            "availableLanguage": ["English", "Gujrati", "Hindi"]
        },
        // "sameAs": [
        //     "https://www.facebook.com/tsttechnology",  // Add your actual social media links
        //     "https://www.linkedin.com/company/tsttechnology"
        // ]
    };

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify(schemaData),
            }}
        />
    </>
}

export const careerJobSchema = (jobDetail, slug) => (
    <>

        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Careers | TST Technology",
                            item: LIVE_URL
                                ? `${LIVE_URL}/careers`
                                : "https://tsttechnology.in/careers",
                        },
                        {
                            "@type": "ListItem",
                            position: 3,
                            name: jobDetail?.title || "Careers | TST Technology",
                            item: LIVE_URL
                                ? `${LIVE_URL}/careers/${slug}`
                                : `https://tsttechnology.in/careers/${slug}`,
                        },
                    ],
                }),
            }}
        />

    </>
);

export const privacyPolicySchema = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "TST Technology Privacy Policy",
                            item: LIVE_URL
                                ? `${LIVE_URL}/privacy-policy`
                                : "https://tsttechnology.in/privacy-policy",
                        },
                    ],
                }),
            }}
        />

    </>

}
export const TermsAndConditionSchema = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "TST Technology Terms And Conditions",
                            item: LIVE_URL
                                ? `${LIVE_URL}/terms-and-conditions`
                                : "https://tsttechnology.in/terms-and-conditions",
                        },
                    ],
                }),
            }}
        />

    </>

}
export const DisclaimerSchema = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "TST Technology Disclaimer",
                            item: LIVE_URL
                                ? `${LIVE_URL}/disclaimer`
                                : "https://tsttechnology.in/disclaimer",
                        },
                    ],
                }),
            }}
        />

    </>

}

export const FAQsSchema = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonFaq"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "TST Technology FAQs",
                            item: LIVE_URL
                                ? `${LIVE_URL}/faqs`
                                : "https://tsttechnology.in/faqs",
                        },
                    ],
                }),
            }}
        />

    </>

}

export const subServiceListScehma = async (title, featurefaq, slug, subSlug) => {
    return (
        <>
            {featurefaq?.length > 0 && (
                <script
                    type="application/ld+json"
                    id="application/ld+jsonFAQMain"
                    dangerouslySetInnerHTML={{
                        __html: JSON.stringify({
                            "@context": "https://schema.org",
                            "@type": "FAQPage",
                            mainEntity: featurefaq?.map(
                                (el, index) => {
                                    return {
                                        "@type": "Question",
                                        name: el?.question,
                                        acceptedAnswer: {
                                            "@type": "Answer",
                                            text: stripATags(el?.answer ?? el?.answerwithList),
                                        },
                                    };
                                }
                            ),
                        }),
                    }}
                />
            )}
            <script
                type="application/ld+json"
                id="application/ld+jsonTool"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org/",
                        "@type": "BreadcrumbList",
                        itemListElement: [
                            {
                                "@type": "ListItem",
                                position: 1,
                                name: "TST Technology",
                                item:
                                    LIVE_URL ??
                                    "https://tsttechnology.in/",
                            },
                            {
                                "@type": "ListItem",
                                position: 2,
                                name: "Service | TST Technology",
                                item: LIVE_URL
                                    ? `${LIVE_URL}/service`
                                    : "https://tsttechnology.in/service",
                            },
                            {
                                "@type": "ListItem",
                                position: 3,
                                name: toCapitalize(slug.replaceAll("-", " ")) || "Service | TST Technology",
                                item: `https://tsttechnology.in/service/${slug}`,
                            },
                            {
                                "@type": "ListItem",
                                position: 3,
                                name: title || "Service | TST Technology",
                                item: `https://tsttechnology.in/service/${slug}/${subSlug}`,
                            },
                        ],
                    }),
                }}
            />

        </>
    );
}

export const hirepageScehma = async (featurefaq, slug) => {
    return (
        <>
            {featurefaq?.length > 0 && (
                <script
                    type="application/ld+json"
                    id="application/ld+jsonFAQMain"
                    dangerouslySetInnerHTML={{
                        __html: JSON.stringify({
                            "@context": "https://schema.org",
                            "@type": "FAQPage",
                            mainEntity: featurefaq?.map(
                                (el, index) => {
                                    return {
                                        "@type": "Question",
                                        name: el?.question,
                                        acceptedAnswer: {
                                            "@type": "Answer",
                                            text: stripATags(el?.answer ?? el?.answerwithList),
                                        },
                                    };
                                }
                            ),
                        }),
                    }}
                />
            )}
            <script
                type="application/ld+json"
                id="application/ld+jsonTool"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org/",
                        "@type": "BreadcrumbList",
                        itemListElement: [
                            {
                                "@type": "ListItem",
                                position: 1,
                                name: "TST Technology",
                                item:
                                    LIVE_URL ??
                                    "https://tsttechnology.in/",
                            },
                            {
                                "@type": "ListItem",
                                position: 2,
                                name: "Service | TST Technology",
                                item: LIVE_URL
                                    ? `${LIVE_URL}/service`
                                    : "https://tsttechnology.in/hire",
                            },
                            {
                                "@type": "ListItem",
                                position: 3,
                                name: toCapitalize(slug.replaceAll("-", " ")) || "Hire | TST Technology",
                                item: `https://tsttechnology.in/hire/${slug}`,
                            },
                           
                        ],
                    }),
                }}
            />

        </>
    );
}

export const resourcesSchema = () => {

    return <>
        <script
            type="application/ld+json"
            id="application/ld+jsonTool"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify({
                    "@context": "https://schema.org/",
                    "@type": "BreadcrumbList",
                    itemListElement: [
                        {
                            "@type": "ListItem",
                            position: 1,
                            name: "TST Technology",
                            item:
                                LIVE_URL ??
                                "https://tsttechnology.in/",
                        },
                        {
                            "@type": "ListItem",
                            position: 2,
                            name: "Resources Library",
                            item: LIVE_URL
                                ? `${LIVE_URL}/resources-library`
                                : "https://tsttechnology.in/resources-library",
                        },
                    ],
                }),
            }}
        />
    </>
}


export const freeBieListSchema = async (blogDetail, slug) => {
    const formatDateWithTimeZone = (dateString) => {
        const date = new Date(dateString);
        return date.toISOString();
    };
    // console.log(blogDetail?.videoList, "jmdfjdbgj")

    const datePublished = blogDetail?.blog?.date
        ? formatDateWithTimeZone(blogDetail.blog.date)
        : blogDetail?.publishedAt
            ? formatDateWithTimeZone(blogDetail.publishedAt)
            : null;

    const dateModified = blogDetail?.blog?.date
        ? formatDateWithTimeZone(blogDetail.blog.date)
        : blogDetail?.updatedAt
            ? formatDateWithTimeZone(blogDetail.updatedAt)
            : null;

    const videoList = blogDetail?.videoList?.length > 0
        ? await Promise.all(blogDetail.videoList.map(async (el) => {
            if (!el?.link) return;
            const contentUrl = convertEmbedToContentUrl(el?.link);
            const videoId = new URL(el?.link)?.pathname.split('/')[2];
            const { uploadDate, description } = await fetchUploadDate(el?.link);

            return {
                "@type": "VideoObject",
                "name": el?.title ?? "",
                "description": description ?? el?.title ?? "Default Description",
                "thumbnailUrl": `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
                "uploadDate": uploadDate ?? "Unknown Date",
                // "duration": "PT2M30S",
                "contentUrl": contentUrl ?? "",
                "embedUrl": el?.link ?? ""
            };
        }))
        : [];

    return (
        <>
            {blogDetail?.attributes?.faqs?.length > 0 && (
                <script
                    type="application/ld+json"
                    id="application/ld+jsonFAQMain"
                    dangerouslySetInnerHTML={{
                        __html: JSON.stringify({
                            "@context": "https://schema.org",
                            "@type": "FAQPage",
                            mainEntity: blogDetail?.attributes?.faqs?.map((el, index) => {
                                return {
                                    "@type": "Question",
                                    name: el?.question,
                                    acceptedAnswer: {
                                        "@type": "Answer",
                                        text: stripATags(el?.answer ?? el?.answerwithList),
                                    },
                                };
                            }),
                        }),
                    }}
                />
            )}
            <script
                type="application/ld+json"
                id="application/ld+jsonTool"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org/",
                        "@type": "BreadcrumbList",
                        itemListElement: [
                            {
                                "@type": "ListItem",
                                position: 1,
                                name: "TST Technology",
                                item: LIVE_URL ?? "https://tsttechnology.in/",
                            },
                            {
                                "@type": "ListItem",
                                position: 2,
                                name: "Resources Library | TST Technology",
                                item: LIVE_URL
                                    ? `${LIVE_URL}/resources-library`
                                    : "https://tsttechnology.in/resources-library",
                            },
                            {
                                "@type": "ListItem",
                                position: 3,
                                name: blogDetail?.singleFreebie?.title || "Resources library | TST Technology",
                                item: `https://tsttechnology.in/resources-library/${slug}`,
                            },
                        ],
                    }),
                }}
            />

        </>
    );
};