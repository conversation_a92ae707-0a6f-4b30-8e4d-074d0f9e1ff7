import Image from "next/image";
import React from "react";

export const SmallBlogCard = ({
  image,
  category,
  readTime,
  date,
  title,
  author,
}) => {
  return (
    <div className="tw-bg-white tw-shadow-happenings_card tw-p-5 md:tw-p-6 xl:tw-p-[30px] tw-rounded-[30px] tw-h-full tw-w-full tw-grid  md:tw-grid-cols-[1fr_33%] xl:tw-grid-cols-[1fr_33%] tw-gap-5 tw-group">
      <div className="tw-flex tw-flex-col tw-gap-[15px] lg:tw-gap-2.5 xl:tw-gap-[15px]">
        <div className="tw-flex tw-justify-between tw-items-center">
          <div className="tw-flex tw-gap-2.5 tw-items-center">
            <div className="tw-px-2 tw-py-1 tw-text-[14px] tw-text-primary_green tw-bg-[#D5F0D2] tw-rounded-lg">
              {category}
            </div>
            <div className="tw-text-primary_gray tw-text-[14px]">
              {readTime}
            </div>
          </div>
          <div className="tw-text-primary_black tw-text-[14px]">{date}</div>
        </div>
        <h3 className=" group-hover:tw-text-primary_green tw-transition-colors tw-duration-700 tw-ease-in-out tw-line-clamp-2 lg:tw-line-clamp-1 xl:tw-line-clamp-2 tw-text-primary_black tw-font-bold tw-leading-[1.3] tw-text-[16px] md:tw-text-[18px] lg:tw-text-[20px] 2xl:tw-text-[24px] tw-mb-0">
          {title}
        </h3>
        <div className="tw-flex tw-items-center tw-gap-[15px]">
          <div className="tw-relative tw-aspect-square tw-w-[30px]">
            <Image
              src={author.avatar}
              fill
              className="tw-rounded-full tw-object-contain"
              alt={author.name}
            />
          </div>
          <div className="tw-my-auto tw-text-[16px] tw-text-primary_black tw-font-medium tw-leading-[1.4]">
            {author.name}
          </div>
        </div>
      </div>

      <div className="md:tw-block tw-hidden tw-relative tw-h-full tw-w-full">
        <Image
          fill
          src={image}
          className="tw-object-cover tw-rounded-[15px]"
          alt={title}
        />
      </div>
    </div>
  );
};

// tw-aspect-[185/140] lg:tw-aspect-[185/120] s1440:tw-aspect-[185/140]
