"use client";

import React from "react";
import { Container, Row, Col } from "reactstrap";
import msg91Icon from "../../../public/partnershipsLogo/msg91.png";
import strapiIcon from "../../../public/partnershipsLogo/strapi.png";
import bulkpeIcon from "../../../public/partnershipsLogo/bulkpe.png";
import cashfreePaymentsIcon from "../../../public/partnershipsLogo/cashfreepayments.png";
import uthoIcon from "../../../public/partnershipsLogo/utho.png";
import digitalOceanIcon from "../../../public/partnershipsLogo/digitalocean.png";
import startupIndiaIcon from "../../../public/cartificatesLogo/startupindia.png";
import awscloudIcon from "../../../public/cartificatesLogo/awscloud.png";
import awsarchitectIcon from "../../../public/cartificatesLogo/awsarchitect.png";
import Image from "next/image";

export const PartnershipsSection = ({ className }) => {
  const partners = [
    msg91Icon,
    strapiIcon,
    bulkpeIcon,
    cashfreePaymentsIcon,
    uthoIcon,
    digitalOceanIcon,
  ];

  const certificates = [startupIndiaIcon, awscloudIcon, awsarchitectIcon];

  return (
    <Container
      tag="section"
      className={`tw-flex tw-flex-col lg:tw-gap-[100px] tw-gap-[70px] ${className}`}
    >
      {/* Partnerships */}
      <div>
        <h2 className="md:!tw-text-[36px] tw-text-[26px] md:tw-mb-[30px] tw-mb-[25px] tw-font-bold tw-font-bricolageGrotesque tw-text-center">
          Partnership
        </h2>
        <Row className="justify-content-center g-4">
          {partners.map((logo, index) => (
            <Col key={index} xs="6" sm="4" lg="3" xl="2" xxl="2">
              <div
                className="tw-rounded-[20px] tw-shadow-tech_card tw-p-[15px] tw-flex tw-justify-center tw-items-center tw-bg-white tw-transition-transform tw-duration-300 tw-ease-in-out hover:tw-shadow-lg hover:tw-scale-[1.05]"
              >
                <div className="tw-relative tw-w-[140px] tw-h-[80px]">
                  <Image
                    fill
                    src={logo}
                    alt={`Partner ${index + 1}`}
                    className="tw-object-contain"
                  />
                </div>
              </div>
            </Col>
          ))}
        </Row>
      </div>

      {/* Certificates */}
      <div>
        <h2 className="md:!tw-text-[36px] tw-text-[26px] md:tw-mb-[30px] tw-mb-[25px] tw-font-bold tw-font-bricolageGrotesque tw-text-center">
          Certificates & Recognitions
        </h2>
        <Row className="justify-content-center g-4">
          {certificates.map((logo, index) => (
            <Col key={index} xs="6" sm="4" lg="3" xl="2" xxl="2">
              <div
                className="tw-rounded-[20px] tw-shadow-tech_card tw-p-[15px] tw-flex tw-justify-center tw-items-center tw-bg-white tw-transition-transform tw-duration-300 tw-ease-in-out hover:tw-shadow-lg hover:tw-scale-[1.05]"
              >
                <div className="tw-relative tw-w-[140px] tw-h-[80px]">
                  <Image
                    fill
                    src={logo}
                    alt={`Certificate ${index + 1}`}
                    className="tw-object-contain"
                  />
                </div>
              </div>
            </Col>
          ))}
        </Row>
      </div>
    </Container>
  );
};
