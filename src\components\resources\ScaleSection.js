import { TopRightArrowIcon } from '@/utils/icons';
import React from 'react'
import { Container } from 'reactstrap';

export const ScaleSection = () => {
    return (
        <Container className="sm:tw-w-[70%] lg:tw-w-full tw-mx-auto tw-text-center">
            <div className="sm:tw-w-[80%] lg:tw-w-full tw-mx-auto tw-text-center tw-shadow-happenings_card tw-rounded-[30px] tw-grid tw-grid-cols-1 lg:tw-grid-cols-[1fr_1fr_125px] tw-gap-[40px] xl:tw-gap-[70px] tw-h-full tw-px-6 tw-py-[70px] sm:tw-p-10 lg:tw-p-[60px] tw-items-center">

                {/* Title Section */}
                <h2 className="tw-font-bricolageGrotesque tw-font-bold tw-text-primary_black tw-text-[36px] md:tw-text-[42px] xl:tw-text-[48px] tw-leading-[1.3] tw-text-center lg:tw-text-left tw-mb-0">
                    Ready to Scale Smarter with TST?
                </h2>


                {/* Description Section */}
                <p className="tw-font-inter tw-font-normal tw-text-primary_gray tw-text-[18px] lg:tw-text-[20px] tw-leading-[1.3] tw-text-center lg:tw-text-left tw-mb-0">
                    Let&apos;s talk about how we can turn your product vision into
                    reality with the right tech, team, and execution.
                </p>


                {/* Icon Section */}
                <div className="tw-aspect-square tw-w-[125px] tw-bg-[#35b72908] tw-rounded-[15px] tw-border-[3px] tw-border-[#35B72926] tw-backdrop-blur-[1px] tw-backdrop-brightness-[100%] [-webkit-backdrop-filter:blur(1px)_brightness(100%)] tw-flex tw-items-center tw-justify-center tw-flex-shrink-0  tw-mx-auto tw-text-center">

                    <TopRightArrowIcon className="tw-w-8 tw-h-8" />

                </div>

            </div>
        </Container>
    );
}
