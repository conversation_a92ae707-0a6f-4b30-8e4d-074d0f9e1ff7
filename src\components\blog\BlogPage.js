'use client';
import React from 'react'
import <PERSON><PERSON><PERSON><PERSON> from './HeroSection'
import { TabSection } from './TabSection'
import CTASection from '@/common/CTASection'
import { blogsCategories, blogSections } from "@/utils/constant";
import { BlogCard } from '../card/BlogCard';
import { ContentSection } from './ContentSection';

const BlogPage = () => {
    return (
        <>
            <HeroSection />
            <TabSection
                className="md:tw-py-[100px] tw-py-[70px]" 
                blackText="Fuel for"
                greenText="Curious Minds"
                subTitle="Discover how we think, what we build, and the lessons we learn along the way."
                isInput={true}
                sections={blogSections}
                categories={blogsCategories}
                pagePathname="/blog"
                CardComponent={(props) => <BlogCard {...props} />}
                ExploreComponent={() => <ContentSection />}
                />
             
            <CTASection title="Stay Ahead of the Curve"
            description="Subscribe for expert tips, growth hacks, and exclusive insights—delivered straight to your inbox."
            isButton={false}
            isInput={true}
            onInputChange={() => {}}
            inputValue=""
            handleClick={() => {}}
            inputPlaceholder="Enter your email"
            buttonText="Subscribe Now"
            />
        </>
    )
}

export default BlogPage