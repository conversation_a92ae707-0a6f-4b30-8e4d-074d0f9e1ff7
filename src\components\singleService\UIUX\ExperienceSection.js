import FillButton from "@/components/buttons/FillButton";
import React from "react";
import { Container } from "reactstrap";

const ExperienceSection = ({ className }) => {
  return (
    <section
      className={`tw-bg-[#F7F9FB] xl:tw-bg-ex-bg-img lg:tw-bg-ex-bg-1024-img md:tw-bg-ex-bg-768-img tw-bg-ex-bg-mobile-img tw-bg-contain tw-bg-no-repeat tw-bg-center ${className}`}
    >
      <Container>
        <div className="tw-flex tw-flex-col md:tw-items-start tw-items-center tw-justify-center xl:tw-py-[120px] md:tw-py-[65px] tw-py-[117px] md:tw-text-left tw-text-center tw-gap-10">
          <div className="tw-bg-slate-50/2 w tw-shrink-0 tw-flex tw-flex-col tw-items-center md:tw-items-start tw-justify-center tw-gap-[15px] md:tw-w-[76%] tw-w-[95%] lg:tw-w-[75%] xl:tw-w-[70%]">
            <h1 className="tw-mb-0 xl:tw-text-[48px] lg:tw-text-[38px] md:tw-text-[34px] tw-text-[30px] tw-font-bricolageGrotesque tw-leading-[1.3] tw-font-semibold">
              Design Experiences Users Love
            </h1>
            <p className="tw-mb-0 xl:tw-text-[18px] lg:tw-text-[16px]  tw-text-[14px] tw-font-inter tw-text-primary_gray tw-leading-[1.4] md:tw-w-[90%]">
              We craft intuitive, accessible, and visually engaging interfaces
              that enhance usability, boost user satisfaction, and drive
              meaningful digital experiences across every touchpoint.
            </p>
          </div>
          <FillButton
            title={"Build Better Experiences"}
            className={
              "tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-5 tw-py-[8px] tw-px-4"
            }
          />
        </div>
      </Container>
    </section>
  );
};

export default ExperienceSection;
