import React from "react";
import { Col, Container, Row } from "reactstrap";
import { BusinessCard } from "../card/BusinessCard";
import icon1 from "../../../public/businessicons/icon1.png";
import icon2 from "../../../public/businessicons/icon2.png";
import icon3 from "../../../public/businessicons/icon3.png";
import BusinessSlider from "@/common/Slider/BusinessSlider";
import FillButton from "../buttons/FillButton";

export const BusinessSection = ({ className }) => {
  const plans = [
    {
      title: "Fixed base",
      image: icon1,
      serviceList: [
        "Predefined set of requirements",
        "Requirement Analysis, Query Documentation and Clarification",
        "Fixing Scope of work",
        "Proposal having detailed Feature list, Fixed time and cost estimation",
        "Fixed Milestone wise Payment Schedule",
        "UAT and Warranty Period",
      ],
    },
    {
      title: "Hourly base",
      image: icon2,
      serviceList: [
        "160+ hours of dedicated support",
        "Scope of work is not defined, chances of changes gradually",
        "Identify skillset and experience level of Resources",
        "Present CVs of shortlisted candidates",
        "Agile Methodology, Sprint Planning, Scrum Meetings",
        "Agile tools for communication, transparency and audit purpose",
        "Track and Record time and task details",
        "Fixed Monthly Charge",
      ],
    },
    {
      title: "Dedicated Team",
      image: icon3,
      serviceList: [
        "Purchase bucket of hours per month which can be used in bits and parts within same month",
        "Minimum 80 hours",
        "Charge on hourly basis",
        "Track and Record time and task details",
        "Response time - 1/2 days",
      ],
    },
  ];

  return (
    <section className=" tw-bg-[#F7F9FB]">
      <Container
        className={`tw-flex tw-flex-col tw-items-center tw-gap-[40px] lg:tw-gap-[70px] ${className}`}
      >
        <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
          <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            <span>Flexible Engagement Models for</span>
            <span className="tw-text-primary_green"> Every Business Need</span>
          </div>
          <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-leading-[120%]">
            Choose a collaboration model that aligns with your timeline, budget,
            and scope — from fixed-cost certainty to agile, on-demand teams.
          </div>
        </div>

        <div className="lg:tw-block tw-hidden">
          <Row className="">
            {plans.map((plan, index) => (
              <Col key={index} lg="4" >
                <BusinessCard
                  key={index}
                  title={plan.title}
                  icon={plan.image}
                  serviceList={plan.serviceList}
                />
              </Col>
            ))}
          </Row>
        </div>


        <div className="lg:tw-hidden tw-block tw-mb-6 tw-w-full">
          <BusinessSlider plans={plans} />
        </div>

        <BottomButton />
      </Container>
    </section>
  );
};

const BottomButton = () => {
  return (
    <FillButton title={'Book a Free Consultation'} className={'tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-5 tw-py-[8px] tw-px-4'} />
  );
};
