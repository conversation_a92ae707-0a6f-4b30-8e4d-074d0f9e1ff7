"use client";
import React, { useLayoutEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import { gsap } from "gsap";
import { Container } from "reactstrap";
import Image from "next/image";
import FillButton from "@/components/buttons/FillButton";
const HeroSection = () => {
    const pathname = usePathname();

    // Container ref for GSAP context
    const sectionRef = useRef(null);
    const titleRef = useRef(null);
    const descRef = useRef(null);
    const buttonRef = useRef(null);

    // Floating images refs
    const ctoRef = useRef(null);
    const devopsRef = useRef(null);
    const aiRef = useRef(null);
    const agileRef = useRef(null);
    const post1Ref = useRef(null);
    const post2Ref = useRef(null);
    const linkedinRef = useRef(null);
    const youtubeRef = useRef(null);
    const modelRef = useRef(null);

    useLayoutEffect(() => {
        const ctx = gsap.context(() => {
            const tl = gsap.timeline({ defaults: { ease: "power3.out", duration: 1 } });

            // Add label with delay
            tl.add("start", "+=0.9");

            // Text animations
            tl.from(titleRef.current, { y: -50, opacity: 0 }, "start")
                .from(descRef.current, { y: -30, opacity: 0 }, "start")
                .from(buttonRef.current, { y: -20, opacity: 0 }, "start");

            // Images animations
            const imageAnimations = [
                { ref: ctoRef, props: { x: -100 } },
                { ref: post1Ref, props: { x: -100 } },
                { ref: devopsRef, props: { y: -100 } },
                { ref: aiRef, props: { y: -100 } },
                { ref: agileRef, props: { x: 100 } },
                { ref: post2Ref, props: { x: 100 } },
                { ref: linkedinRef, props: { y: 100 } },
                { ref: youtubeRef, props: { y: 100 } },
                { ref: modelRef, props: { y: 100 } },
            ];

            imageAnimations.forEach(({ ref, props }) => {
                if (ref.current) {
                    gsap.set(ref.current, { ...props, opacity: 0 });
                    tl.to(ref.current, {
                        ...Object.fromEntries(Object.entries(props).map(([k]) => [k, 0])),
                        opacity: 1,
                        duration: 0.8,
                        ease: "power2.out"
                    }, "start");
                }
            });
        }, sectionRef);

        return () => ctx.revert();
    }, [pathname]);


    return (
        <section ref={sectionRef} className="tw-bg-[#F7F9FB] tw-shadow-home_hero_section lg:tw-pb-[.9375rem] tw-pb-2 tw-relative tw-overflow-hidden">
            <div className="tw-bg-blogHeroBg tw-bg-no-repeat tw-bg-cover tw-bg-top lg:tw-rounded-[1.25rem] tw-rounded-[.9375rem] lg:tw-mx-[.9375rem] tw-mx-2 tw-relative">
                <Container>
                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center xl:tw-py-[210px] lg:tw-py-[210px] md:tw-py-[130px] tw-pt-[165px] tw-pb-[200px] tw-text-center tw-gap-[30px] tw-relative tw-z-10">
                        <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px]">
                            <h1
                                ref={titleRef}
                                className="tw-mb-0 xl:tw-text-[70px] lg:tw-text-[55px] md:tw-text-[40px] tw-text-[34px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold md:tw-w-[90%]"
                            >
                                Explore Ideas. Share Knowledge. Inspire Growth.
                            </h1>
                            <p
                                ref={descRef}
                                className="tw-mb-0 tw-text-[22px] lg:tw-text-xl md:tw-text-lg tw-text-base tw-text-primary_gray lg:tw-w-[65%] md:tw-w-[80%] tw-w-[94%] md:tw-leading-[140%] tw-leading-[120%]"
                            >
                                Dive into expert insights, company stories, design thinking, and tech updates—written by the people behind the innovation.                            </p>
                        </div>

                        <FillButton
                            ref={buttonRef}
                            title="Explore Articles"
                            className="tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-5 tw-py-[8px] tw-px-4"
                        />
                    </div>
                </Container>

                {/* Floating Images - Positioned like reference */}

                {/* LEFT SIDE */}
                {/* CTO - Top Left */}
                <div
                    ref={ctoRef}
                    className="tw-absolute xl:tw-top-[7%] lg:tw-top-[15%] tw-top-[10%] lg:-tw-left-[3%] md:-tw-left-[4%] -tw-left-[8%] tw-w-[90px] tw-h-[70px] lg:tw-w-[150px] lg:tw-h-[90px] xl:tw-w-[150px] xl:tw-h-[150px] "
                >
                    <Image
                        src="/blogpage/cto.png"
                        fill
                        alt="CTO"
                        className="tw-object-contain"
                    />
                </div>

                {/* Post 1 - Bottom Left */}
                <div
                    ref={post1Ref}
                    className="tw-absolute xl:tw-bottom-[25%] md:tw-bottom-[12%] xl:-tw-left-[4%] lg:-tw-left-[7%] md:-tw-left-[5%] -tw-left-[8%] tw-bottom-0 tw-w-[140px] tw-h-[120px] md:tw-w-[170px] md:tw-h-[180px] lg:tw-w-[300px] lg:tw-h-[230px] xl:tw-w-[300px] xl:tw-h-[250px] "
                >
                    <Image
                        src="/blogpage/post1.png"
                        fill
                        alt="Post 1"
                        className="tw-object-contain"
                    />
                </div>

                {/* TOP */}
                {/* DevOps - Top Center Left */}
                <div
                    ref={devopsRef}
                    className="tw-absolute xl:-tw-top-2 lg:tw-top-10 -tw-top-[2%] tw-left-[25%] tw-w-[180px] tw-h-[70px] lg:tw-w-[170px] lg:tw-h-[120px] xl:tw-w-[280px] xl:tw-h-[128px] "
                >
                    <Image
                        src="/blogpage/devops.png"
                        fill
                        alt="DevOps"
                        className="tw-object-contain"
                    />
                </div>

                {/* AI - Top Center Right */}
                <div
                    ref={aiRef}
                    className="tw-absolute xl:-tw-top-2 lg:tw-top-[7%] md:-tw-top-[2%]  tw-top-20 md:tw-right-[25%] tw-right-[40%] tw-w-[80px] tw-h-[60px] lg:tw-w-[100px] lg:tw-h-[80px] xl:tw-w-[140px] xl:tw-h-[108px] "
                >
                    <Image
                        src="/blogpage/ai.png"
                        fill
                        alt="AI"
                        className="tw-object-contain"
                    />
                </div>

                {/* RIGHT SIDE */}
                {/* Agile - Top Right */}
                <div
                    ref={agileRef}
                    className="tw-absolute xl:tw-top-[8%] tw-top-[12%] -tw-right-[3%] tw-w-[100px] tw-h-[80px] lg:tw-w-[140px] lg:tw-h-[90px] xl:tw-w-[157px] xl:tw-h-[128px] "
                >
                    <Image
                        src="/blogpage/agaile.png"
                        fill
                        alt="Agile"
                        className="tw-object-contain"
                    />
                </div>

                {/* Post 2 - Bottom Right */}
                <div
                    ref={post2Ref}
                    className="tw-absolute xl:tw-bottom-[25%] md:tw-bottom-[12%] xl:-tw-right-[4%] md:-tw-right-[6%] -tw-right-[8%] tw-w-[140px] tw-bottom-0  tw-h-[120px] md:tw-w-[170px] md:tw-h-[180px] lg:tw-w-[300px] lg:tw-h-[230px] xl:tw-w-[300px] xl:tw-h-[250px] "
                >
                    <Image
                        src="/blogpage/post2.png"
                        fill
                        alt="Post 2"
                        className="tw-object-contain"
                    />
                </div>

                {/* BOTTOM */}
                {/* LinkedIn - Bottom Left */}
                <div
                    ref={linkedinRef}
                    className="tw-absolute xl:tw-bottom-[8%] md:tw-bottom-[5%] tw-bottom-[20%] md:tw-left-[20%] tw-left-[15%] tw-w-[50px] tw-h-[50px] md:tw-w-[60px] md:tw-h-[60px] lg:tw-w-[80px] lg:tw-h-[80px] xl:tw-w-[90px] xl:tw-h-[90px] "
                >
                    <Image
                        src="/blogpage/linkedin.png"
                        fill
                        alt="LinkedIn"
                        className="tw-object-contain"
                    />
                </div>

                {/* Model - Bottom Center */}
                <div
                    ref={modelRef}
                    className="tw-absolute md:tw-bottom-0 tw-bottom-[12%] md:tw-left-1/2 md:tw-transform md:-tw-translate-x-1/2 tw-left-[27%] tw-w-[140px] tw-h-[80px] md:tw-w-[165px] md:tw-h-[100px] lg:tw-w-[365px] lg:tw-h-[140px] xl:tw-w-[365px] xl:tw-h-[140px] "
                >
                    <Image
                        src="/blogpage/model.png"
                        fill
                        alt="Model"
                        className="tw-object-contain"
                    />
                </div>

                {/* YouTube - Bottom Right */}
                <div
                    ref={youtubeRef}
                    className="tw-absolute xl:tw-bottom-[8%] md:tw-bottom-[5%] tw-bottom-[20%] md:tw-right-[20%] tw-right-[15%] tw-w-[50px] tw-h-[50px] md:tw-w-[60px] md:tw-h-[60px] lg:tw-w-[80px] lg:tw-h-[80px] xl:tw-w-[90px] xl:tw-h-[90px]"
                >
                    <Image
                        src="/blogpage/youtube.png"
                        fill
                        alt="YouTube"
                        className="tw-object-contain"
                    />
                </div>
            </div>
        </section>
    );
};

export default HeroSection;
