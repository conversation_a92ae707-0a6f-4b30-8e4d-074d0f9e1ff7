import React from "react";
import { Container, Row, Col } from "reactstrap";
import { ServiceCard } from "../card/ServiceCard";
import Image from "next/image";
import uiuxIcon from "../../../public/serviceLogo/uiux.png";
import mobileAppIcon from "../../../public/serviceLogo/mobileApp.png";
import saasDevIcon from "../../../public/serviceLogo/saasDev.png";
import webDevIcon from "../../../public/serviceLogo/webDev.png";
import softwareDevIcon from "../../../public/serviceLogo/softwareDev.png";
import itConsultingIcon from "../../../public/serviceLogo/itConsulting.png";
import devOpsIcon from "../../../public/serviceLogo/devOps.png";
import virtualCtoIcon from "../../../public/serviceLogo/virtualCto.png";
import decorativeLinesIcon from "../../../public/serviceLogo/Servicebelow.svg";
import { DecorativeLinesIcon, RightArrowIcon } from "@/utils/icons";
import TextButton from "../buttons/TextButton";

export const ServicesSection = ({ className }) => {
  const services = [
    {
      icon: softwareDevIcon,
      title: "Custom Software Development",
      subtitle: "Develop Custom Services",
      link: "/service/software-development",
    },

    {
      icon: webDevIcon,
      title: "Web Development Services",
      subtitle: "Start Website Project",
      link: "/service/web-development",
    },
    {
      icon: mobileAppIcon,
      title: "Mobile App Development Services",
      subtitle: "Build Mobile App",
      link: "/service/mobile-app-development",
    },
    {
      icon: uiuxIcon,
      title: "UI UX Design",
      subtitle: "Enhance User Experience",
      link: "/service/ui-ux-design",
    },
    {
      icon: devOpsIcon,
      title: "DevOps Services & Solutions",
      subtitle: "Optimize Devops Workflow",
      link: "/service/devops-as-a-service",
    },
    {
      icon: saasDevIcon,
      title: "SaaS Development Services",
      subtitle: "Launch Saas Product",
      link: "/service/saas-product-development",
    },
    {
      icon: itConsultingIcon,
      title: "IT Consulting Services",
      subtitle: "Get Expert IT Advice",
      link: "/service/it-consultancy",
    },
    {
      icon: itConsultingIcon,
      title: "IT Consulting Services",
      subtitle: "Get Expert IT Advice",
      link: "/service/it-consultancy",
    },
    {
      icon: itConsultingIcon,
      title: "IT Consulting Services",
      subtitle: "Get Expert IT Advice",
      link: "/service/it-consultancy",
    },
    // {
    //   icon: softwareDevIcon,
    //   title: "Software Development Services",
    //   subtitle: "Develop Custom Services",
    //   link: "/service/software-development",
    // },
    // {
    //   icon: virtualCtoIcon,
    //   title: "Virtual CTO Services",
    //   subtitle: "Guide on Tech Strategy",
    //   link: "/service/virtual-cto-as-a-service",
    // },
  ];

  return (
    <section className={`tw-bg-service-bg ${className}`}>
      <Container tag="section">
        <h2 className="tw-relative lg:tw-mb-[50px] tw-mb-[40px] tw-text-secondary_color tw-font-bold tw-text-center tw-font-bricolageGrotesque md:tw-text-[36px] tw-text-[26px] ">
          <span>Explore What We </span>
          <div className="tw-inline tw-relative ">
            Do Best
            <DecorativeLinesIcon
              fill={"#fff"}
              className="tw-absolute tw-right-2 aspect-[100/11] tw-w-[80px] sm:left-[24px] lg:tw-w-[78px] xl:tw-w-[120px]"
            />
          </div>
        </h2>
        <Row className="justify-content-center g-3 g-lg-4 ">
          {services.map((service, index) => (
            <Col key={index} xs="12" sm="6" md="6" lg="4" xl="4">
              <ServiceCard
                icon={service.icon}
                title={service.title}
                subtitle={service.subtitle}
                link={service.link}
              />
            </Col>
          ))}
        </Row>
        <TextButton
          blackTitle="Check out all of our"
          greenTitle="Best features"
          className="tw-mt-9 lg:tw-mt-[50px] lg:tw-text-[20px]"
        />
      </Container>
    </section>
  );
};
