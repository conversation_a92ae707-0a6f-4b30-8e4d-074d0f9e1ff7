import Image from "next/image";
import React from "react";
import { Container } from "reactstrap";
import FillButton from "../buttons/FillButton";

const HappeningsSection = ({ className }) => {
  const allImages = [
    "https://images.unsplash.com/photo-1648260296289-ab882814a005",
    "https://images.unsplash.com/photo-1618788372246-79faff0c3742",
    "https://images.unsplash.com/photo-1534670007418-fbb7f6cf32c3",
    "https://plus.unsplash.com/premium_photo-1661589354357-f56ddf86a0b4",
    "https://images.unsplash.com/photo-1506097425191-7ad538b29cef",
    "https://plus.unsplash.com/premium_photo-1661326248013-3107a4b2bd91",
    "https://images.unsplash.com/photo-1587440871875-191322ee64b0",
    "https://images.unsplash.com/photo-1648260296289-ab882814a005",
  ];
  return (
    <section className={`${className}`}>
      <Container className="tw-flex tw-flex-col tw-gap-[36px] md:tw-gap-[70px]">
        {/* Title */}
        <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
          <div className="tw-text-primary_green tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            Happenings
            <span className="tw-text-primary_black"> At TST Technology</span>
          </div>
          <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-w-[80%] tw-leading-[140%]">
            At TST Technology, innovation isn&apos;t just what we build —
            it&apos;s how we grow, celebrate, and connect. From team
            achievements and behind-the-scenes moments to events and updates,
            here&apos;s a glimpse into life at TST.
          </div>
        </div>

        {/* Image Masonry Grid */}
        <div className="tw-columns-1 md:tw-columns-2 xl:tw-columns-3 tw-gap-5">
          {allImages.map((sm, index) => (
            <div
              key={index}
              className="tw-relative tw-mb-6 tw-group tw-break-inside-avoid tw-overflow-hidden tw-rounded-[14px] lg:tw-rounded-[24px]"
            >
              <Image
                src={sm}
                alt={`Image ${index + 1}`}
                className="tw-w-full tw-h-auto tw-object-cover tw-rounded-[14px] lg:tw-rounded-[24px] group-hover:tw-scale-110 tw-transition-transform tw-origin-bottom tw-duration-500"
                width={600}
                height={400}
              />
              <div className="tw-absolute tw-bottom-5 tw-left-1/2 -tw-translate-x-1/2 tw-translate-y-5 group-hover:tw-translate-y-0 tw-bg-white tw-rounded-full tw-py-1 tw-px-4  tw-invisible group-hover:tw-visible tw-opacity-0 group-hover:tw-opacity-100 tw-transition-all tw-duration-[100ms] group-hover:tw-duration-[300ms] tw-ease-in group-hover:tw-ease-out tw-truncate tw-font-semibold tw-text-lg">
                With Union Bank Leader.
              </div>
            </div>
          ))}
        </div>
        <div className="tw-flex tw-justify-center">
          <FillButton
            title={"View All Moments"}
            className="tw-w-fit tw-rounded-[12px] tw-px-10 tw-py-[13px] lg:tw-px-4 lg:tw-py-[8px] xl:tw-px-10 xl:tw-py-[15px]"
            isLink
            href="/happenings-at-tst-technology"
          />
        </div>
      </Container>
    </section>
  );
};

export default HappeningsSection;
