import React from "react";
import { Container, Row, Col } from "reactstrap";
import Image from "next/image";
import uiUxIcon from "../../../public/service-page/service-section/uiUx.png";
import mobileAppIcon from "../../../public/service-page/service-section/appDev.png";
import saasDevIcon from "../../../public/service-page/service-section/saas.png";
import webDevIcon from "../../../public/service-page/service-section/webDev.png";
import softwareDevIcon from "../../../public/service-page/service-section/softwareDev.png";
import itConsultingIcon from "../../../public/service-page/service-section/maintenance.png";
import devOpsIcon from "../../../public/service-page/service-section/devOps.png";
import { DecorativeLinesIcon, RightArrowIcon } from "@/utils/icons";
import TextButton from "../buttons/TextButton";
import { ServiceCard } from "./ServiceCard";

export const ServicesSection = ({ className }) => {
    const services = [
        {
            icon: softwareDevIcon,
            title: "Custom Software Development",
            subtitle: "Whether you’re building your first product or scaling an existing platform, we craft tailor-made software solutions designed to meet your unique needs — fast, secure, and future-ready.",
            features: [
                {
                    label: "MVP Development",
                },
                {
                    label: "End-to-End Product Development",
                },
                {
                    label: "Custom Business Tools",
                },
            ],
        },

        {
            icon: webDevIcon,
            title: "Web Development Services",
            subtitle: "From simple websites to complex web applications, we build digital experiences that are fast, responsive, and ready to scale.",
            features: [
                {
                    label: "Custom Web Applications",
                },
                {
                    label: "Corporate Websites",
                },
                {
                    label: "E-commerce Platforms",
                },
                {
                    label: "Web Portals & Dashboards",
                },
            ],
        },
        {
            icon: mobileAppIcon,
            title: "Mobile App Development Services",
            subtitle: "We create seamless mobile experiences — from idea to App Store. Whether it’s Android, iOS, or cross-platform, we design apps that your users love and your business needs.",
            features: [
                {
                    label: "Native & Cross-Platform Apps",
                },
                {
                    label: "UI/UX Mobile App Design",
                },
                {
                    label: "App Maintenance & Updates",
                },
            ],
        },
        {
            icon: uiUxIcon,
            title: "UI UX Design",
            subtitle: "Functional designs are just the beginning — we craft digital experiences that are beautiful, intuitive, and built to scale. Our user-first approach ensures every design looks stunning, works flawlessly, and grows with your business.",
            features: [
                {
                    label: "Wireframing and Prototyping",
                },
                {
                    label: "Web and Mobile UI/UX Design",
                },
                {
                    label: "Design System Creation",
                },
            ],
        },
        {
            icon: devOpsIcon,
            title: "DevOps Services & Solutions",
            subtitle: "Fast, secure, and scalable — we help your tech stay online, optimize cloud costs, and deploy updates without friction.",
            features: [
                {
                    label: "Cloud Infrastructure Setup (AWS, Azure)",
                },
                {
                    label: "Continuous Integration & Deployment (CI/CD)",
                },
                {
                    label: "Server Management & Monitoring",
                },
            ],
        },
        {
            icon: saasDevIcon,
            title: "SaaS Development Services",
            subtitle: "Planning a SaaS product? We help you design, build, and scale SaaS platforms that are robust, secure, and designed for growth.",
            features: [
                {
                    label: "SaaS Platform Development",
                },
                {
                    label: "Subscription Management Solutions",
                },
                {
                    label: "Scalable Cloud Infrastructure",
                },
            ],
        },
        {
            icon: itConsultingIcon,
            title: "IT Consulting & Maintenance",
            subtitle: "Not sure where to start or how to scale? Our experienced team provides honest, actionable advice to align your tech strategy with your business goals.",
            features: [
                {
                    label: "Technology Roadmapping",
                },
                {
                    label: "Scalability & Infrastructure Planning",
                },
                {
                    label: "Product-Market Fit Guidance",
                },
            ],
        },

    ];

    return (
        // tw-bg-service-bg
        <section className={`tw-bg-serviceFeatureBg tw-bg-no-repeat ${className}`}>
            <Container tag="section">
                <h2 className="tw-relative lg:tw-mb-[50px] tw-mb-[40px] tw-text-secondary_color tw-font-bold tw-text-center tw-font-bricolageGrotesque md:tw-text-[36px] tw-text-[26px] ">
                    <span>Explore What We </span>
                    <div className="tw-inline tw-relative ">
                        Do Best
                        <DecorativeLinesIcon
                            fill={"#fff"}
                            className="tw-absolute tw-right-2 aspect-[100/11] tw-w-[80px] sm:left-[24px] lg:tw-w-[78px] xl:tw-w-[120px]"
                        />
                    </div>
                </h2>
                <Row className="justify-content-center g-3 g-lg-4 ">
                    {services.map((service, index) => (
                        <Col className={``} key={index} xs="12" >
                            <ServiceCard
                                isImgRightSide={index % 2 === 0}
                                icon={service.icon}
                                title={service.title}
                                subtitle={service.subtitle}
                                link={service.link}
                                features={service.features}
                                isLastElement={index === services.length - 1}
                            />
                        </Col>
                    ))}
                </Row>
                {/* <TextButton
                    blackTitle="Check out all of our"
                    greenTitle="Best features"
                    className="tw-mt-9 lg:tw-mt-[50px] lg:tw-text-[20px]"
                /> */}
            </Container>
        </section>
    );
};
