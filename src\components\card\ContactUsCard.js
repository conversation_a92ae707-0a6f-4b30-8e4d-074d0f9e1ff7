import Image from "next/image";
import React from "react";

export const ContactUsCard = ({ Logo, description, owner }) => {
  return (
    <>
      <div className="tw-flex tw-flex-col flex xl:tw-gap-[35px] tw-gap-[25px] tw-w-full">
        <div className="tw-font-inter tw-font-medium tw-text-[16px] md:tw-text-[18px] lg:tw-text-[20px] tw-leading-[130%] tw-text-primary_black tw-line-clamp-[9] ">
          {description}
        </div>

        <div className="tw-flex tw-justify-between tw-items-center">
          <div>
            <p className="tw-text-primary_black tw-font-semibold md:tw-text-[18px] tw-text-[16px] tw-leading-[120%] tw-font-inter tw-mb-2.5">
              {owner.name}
            </p>
            <span className="tw-block tw-text-primary_gray lg:tw-text-[16px] md:tw-text-[14px] tw-text-[12px] tw-leading-[120%] tw-font-inter">
              {owner.position}
            </span>
          </div>
          <div className="tw-relative tw-w-28 tw-h-[50px]">
            <Image
              src={Logo}
              className="tw-object-contain"
              alt="client Company Logo"
            />
          </div>
        </div>
      </div>
    </>
  );
};
