import React from "react";
import { Col, Container, Row } from "reactstrap";


import finTechIcon from "/public/partnersAndPraise/finTech.png";
import saasIcon from "/public/partnersAndPraise/saas.png";
import eCommerceIcon from "/public/partnersAndPraise/eCommerce.png";
import healthcareIcon from "/public/partnersAndPraise/healthcare.png";
import educationIcon from "/public/partnersAndPraise/education.png";
import logisticsIcon from "/public/partnersAndPraise/logistics.png";
import networkingIcon from "/public/partnersAndPraise/networking.png";
import hospitalityIcon from "/public/partnersAndPraise/hospitality.png";
import socialMediaIcon from "/public/partnersAndPraise/socialMedia.png";
import Image from "next/image";

export const ValuesSection = ({ className }) => {
  const values = [
    {
      icon: finTechIcon,
      title: "FinTech",
      description:
        "We develop secure, scalable financial platforms with robust backend architecture, KYC/AML integration, and real-time transaction processing. Whether it’s building digital wallets, investment dashboards, or loan management systems, we ensure compliance, performance, and smooth user experience across devices.",
    },
    {
      icon: saasIcon,
      title: "SaaS",
      description:
        "We specialize in multi-tenant SaaS architectures, subscription models, and microservices-based backends. From MVPs to full-scale SaaS platforms, we implement CI/CD pipelines, scalable APIs, and secure authentication flows — enabling your software to evolve rapidly and serve thousands of users efficiently.",
    },
    {
      icon: eCommerceIcon,
      title: "eCommerce",
      description:
        "We build performance-optimized eCommerce platforms with custom CMS, payment gateway integration, and inventory synchronization. Whether headless or full-stack, our solutions support high-traffic environments, seamless mobile UX, and features like cart recovery, multi-language support, and CRM integration.",
    },
    {
      icon: healthcareIcon,
      title: "HealthTech",
      description:
        "We create HIPAA-compliant and GDPR-ready health applications with role-based access control, real-time data sync, and secure patient data handling. From appointment scheduling to EHR dashboards and telehealth modules, we ensure your platform is both clinically useful and technically sound.",
    },
    {
      icon: educationIcon,
      title: "Education",
      description:
        "We design and build LMS platforms, quiz engines, and student analytics dashboards with scalable cloud hosting and real-time classroom features. Whether synchronous orasynchronous learning, our systems support interactive content, user tracking, and seamless integrations with video APIs like Zoom or Jitsi.",
    },
    {
      icon: logisticsIcon,
      title: "Logistics & Mobility",
      description:
        "We engineer logistics tech with fleet tracking, real-time route optimization, and automated dispatch algorithms. With strong backend systems and intuitive admin panels, our solutions power driver apps, customer interfaces, and operational dashboards for full supply chain visibility.",
    },
    {
      icon: networkingIcon,
      title: "Social Networking",
      description:
        "We craft scalable social platforms using graph database models, push notification systems, and in-app messaging frameworks. From activity feeds to follow systems, comment moderation, and content recommendation engines — we build the backend and frontend logic that keeps communities thriving.",
    },
    {
      icon: hospitalityIcon,
      title: "Hospitality",
      description:
        "We deliver hospitality tech with reservation engines, digital menu systems, and property management integrations. Whether for restaurants, hotels, or multi-location chains, ourtools optimize guest journeys, staff workflows, and connect seamlessly with third-party platforms like POS or OTA systems.",
    },
    {
      icon: socialMediaIcon,
      title: "Media & Content",
      description:
        "We build high-performance content platforms with CDN integration, headless CMS, and video streaming capabilities. Whether you’re launching a media portal, podcast app, or custom publishing tool, we optimize for content delivery, SEO, and cross-platform engagement.",
    },

  ];

  return (
    <section className={`${className}`}>
      <Container className="tw-flex tw-flex-col tw-gap-[40px] lg:tw-gap-[70px]">
        {/* Title */}
        <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
          <div className="tw-text-white tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            <span className="tw-text-secondary_color">Industries </span>
            We Know Inside Out

          </div>
          <div className="lg:tw-text-[14px] tw-text-[12px] md:text-[14px] tw-text-[#E9E9E9] tw-font-inter tw-w-[80%] tw-leading-[140%]">
            Every business is different — but the need for reliable, scalable technology is universal. Here’s a look at the sectors we’ve helped shape with design, code, and clarity.
          </div>
        </div>

        {/* Step Table */}
        <Row className="justify-content-center">
          {values.map((value, index) => (
            <React.Fragment key={index}>
              <Col
                xs="12"
                sm="4"
                className="tw-flex tw-flex-col tw-justify-center tw-mb-[20px] sm:tw-mb-0"
              >
                <div className="tw-flex tw-items-center tw-gap-[15px]">
                  <div className="tw-relative tw-aspect-square tw-w-6 md:tw-w-8 lg:tw-w-10">
                    <Image
                      src={value.icon}
                      className="tw-object-contain"
                      alt={`${value.title} icon`}
                    />
                  </div>
                  <h2 className="tw-font-medium tw-font-inter tw-text-[20px] md:tw-text-[24px] xl:tw-text-[30px] tw-leading-[1.2] tw-text-white tw-mb-0">
                    {value.title}
                  </h2>
                </div>
              </Col>
              <Col xs="12" sm="8">
                <h3 className="tw-font-inter tw-font-normal lg:tw-font-medium  tw-text-[16px] md:tw-text-[18px] tw-text-[#A5A5A5] xl:tw-text-[20px] tw-leading-[1.2] tw-mb-0">
                  {value.description}
                </h3>
              </Col>
              {index !== values.length - 1 && (
                <div className="md:tw-py-9 lg:tw-py-[45px] tw-py-[30px]">
                  <div className="tw-border tw-border-[#977FCB]" />
                </div>
              )}
            </React.Fragment>
          ))}
        </Row>
      </Container>
    </section>
  );
};
