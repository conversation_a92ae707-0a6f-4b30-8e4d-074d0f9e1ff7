"use client";
import React, { useState } from "react";
import { Container } from "reactstrap";
import LocationCard from "../card/LocationCard";
import Slider from "react-slick";
import mapImg from "../../../public/aboutPage/contactUs/map1440.svg"
import Image from "next/image";
export const MapSection = ({ className }) => {
  const [hoveredLocation, setHoveredLocation] = useState(null);
  const locations = [
    {
      id: "germany-bavaria",
      country: "Germany",
      type: "Headquarter",
      name: "Bavaria",
      address: "97424 Schweinfurt, Bavaria, Germany",
      phone: "",
      connectionLines: ["line-germany"], // M600.426 1.6875L600.426 79.5903 (vertical line)
      tooltipPosition: { x: 700, y: 79 } // Position near middle of Germany vertical line
    },
    {
      id: "india-ahmedabad",
      country: "India",

      name: "Ahmedabad",
      address: "Titanium Heights, A-1206, Corporate Rd, opp. Vodafone House, Prahlad Nagar, Ahmedabad- 380015",
      phone: "+91 98255 82469",
      connectionLines: ["line-ahmedabad"], // M836.722 38.2891L791.391 160.619
      tooltipPosition: { x: 700, y: 139 } // Position near middle of Ahmedabad line
    },

    {
      id: "usa-california",
      country: "USA",
      type: "Partner",
      name: "California",
      address: "Brentwood, Los Angeles, California 90049",
      phone: "",
      connectionLines: ["line-usa"], // M211.395 144.547L264.434 197.33
      tooltipPosition: { x: 350, y: 175 } // Position near middle of USA line
    },


  ];

  const settings = {
    dots: false,
    arrows: false,
    infinite: false,
    speed: 500,
    swipeToSlide: true,
    responsive: [
      {
        breakpoint: 2600,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 1440,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 1380,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 980,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1.2,
          slidesToScroll: 1,
        },
      },
    ],

  };

  return (
    <>
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
      <section className={`tw-bg-primary_color ${className}`}>
        <Container className="">
          <h2 className="tw-text-white tw-font-bricolageGrotesque md:tw-text-4xl tw-text-[26px] tw-font-bold tw-leading-none tw-text-center tw-self-center ">
            <span>
              Our Global {" "}
            </span>
            <span className="tw-text-secondary_color tw-font-bricolageGrotesque">
              Presence
            </span>
          </h2>
          <p className="tw-text-[#E9E9E9] tw-text-sm  tw-text-center tw-mx-auto  tw-mt-2.5 xl:tw-w-[55%] lg:tw-w-3/4">
            From our headquarters in India to partnerships around the world — here’s where you can find us.
          </p>

          <div className="tw-relative xl:tw-mt-[120px]  lg:tw-mt-[90px] md:tw-mt-[70px] tw-mt-[40px]">
            <div className="tw-relative tw-w-full lg:tw-aspect-[1240/420] md:tw-aspect-[1140/400] tw-aspect-[353/120] tw-overflow-visible  lg:tw-min-h-[322px]">
              <Image
                fill
                src={mapImg}
                className="tw-object-contain"
                alt={'map image'}
                priority
              />
              <svg
                width="100%"
                height="100%"
                viewBox="0 0 1240 422"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="tw-absolute 2xl:-tw-top-3 2xl:tw-left-5 xl:-tw-top-5 lg:-tw-top-3 lg:tw-left-4 md:-tw-top-1.5 md:tw-left-3.5 -tw-mt-1.5 tw-left-1.5  tw-w-full tw-h-full lg:tw-aspect-[1240/420] md:tw-aspect-[1140/400] tw-aspect-[200/120]"
                preserveAspectRatio="xMidYMid meet"
              >
                <g clipPath="url(#clip0_8127_2628)">
                  <path d="M1231.86 421.745C1137.36 176.021 899.047 1.63281 620.006 1.63281C340.964 1.63281 102.657 176.021 8.15234 421.745" stroke="url(#paint0_linear_8127_2628)" strokeWidth="0.791667" />

                  {/* Surat Connection Line */}
                  {/* <path
                    id="line-surat"
                    d="M840.214 39.4609L793.043 165.52"
                    stroke={hoveredLocation && locations.find(loc => loc.id === hoveredLocation)?.connectionLines?.includes('line-surat') ? "white" : "#75757A"}
                    strokeWidth={hoveredLocation && locations.find(loc => loc.id === hoveredLocation)?.connectionLines?.includes('line-surat') ? "2" : "1"}
                    className="tw-transition-all tw-duration-500"
                  />
                  <path d="M794.543 165.523C794.543 166.352 793.873 167.023 793.043 167.023C792.213 167.023 791.543 166.352 791.543 165.523C791.543 164.695 792.213 164.023 793.043 164.023C793.873 164.023 794.543 164.695 794.543 165.523Z"
                    fill="white"
                    className="tw-transition-all tw-duration-500" />
                  <path d="M841.715 39.4609C841.715 40.2894 841.045 40.9609 840.215 40.9609C839.385 40.9609 838.715 40.2894 838.715 39.4609C838.715 38.6324 839.385 37.9609 840.215 37.9609C841.045 37.9609 841.715 38.6324 841.715 39.4609Z"
                    fill="white"
                    className="tw-transition-all tw-duration-500" /> */}

                  {/* Ahmedabad Connection Line */}
                  <path
                    id="line-ahmedabad"
                    d="M836.722 38.2891L791.391 160.619"
                    stroke={hoveredLocation && locations.find(loc => loc.id === hoveredLocation)?.connectionLines?.includes('line-ahmedabad') ? "white" : "#75757A"}
                    strokeWidth={hoveredLocation && locations.find(loc => loc.id === hoveredLocation)?.connectionLines?.includes('line-ahmedabad') ? "2" : "1"}
                    className="tw-transition-all tw-duration-500"
                  />
                  <path d="M792.891 160.617C792.891 161.446 792.221 162.117 791.391 162.117C790.561 162.117 789.891 161.446 789.891 160.617C789.891 159.789 790.561 159.117 791.391 159.117C792.221 159.117 792.891 159.789 792.891 160.617Z"
                    fill="white"
                    className="tw-transition-all tw-duration-500" />
                  <path d="M838.223 38.2891C838.223 39.1176 837.553 39.7891 836.723 39.7891C835.893 39.7891 835.223 39.1176 835.223 38.2891C835.223 37.4606 835.893 36.7891 836.723 36.7891C837.553 36.7891 838.223 37.4606 838.223 38.2891Z"
                    fill="white"
                    className="tw-transition-all tw-duration-500" />

                  {/* Germany Vertical Line */}
                  <path d="M600.426 1.6875L600.426 79.5903Z" fill="#75757A" />
                  <path
                    id="line-germany"
                    d="M600.426 1.6875L600.426 79.5903"
                    stroke={hoveredLocation && locations.find(loc => loc.id === hoveredLocation)?.connectionLines?.includes('line-germany') ? "white" : "#75757A"}
                    strokeWidth={hoveredLocation && locations.find(loc => loc.id === hoveredLocation)?.connectionLines?.includes('line-germany') ? "2" : "1"}
                    className="tw-transition-all tw-duration-500"
                  />
                  <path d="M601.926 80.0703C601.926 80.8988 601.256 81.5703 600.426 81.5703C599.596 81.5703 598.926 80.8988 598.926 80.0703C598.926 79.2418 599.596 78.5703 600.426 78.5703C601.256 78.5703 601.926 79.2418 601.926 80.0703Z" fill="white" />
                  <path d="M601.926 1.6875C601.926 2.516 601.256 3.1875 600.426 3.1875C599.596 3.1875 598.926 2.516 598.926 1.6875C598.926 0.859 599.596 0.1875 600.426 0.1875C601.256 0.1875 601.926 0.859 601.926 1.6875Z" fill="white" />

                  {/* USA Connection Line */}
                  <path
                    id="line-usa"
                    d="M211.395 144.547L264.434 197.33"
                    stroke={hoveredLocation && locations.find(loc => loc.id === hoveredLocation)?.connectionLines?.includes('line-usa') ? "white" : "#75757A"}
                    strokeWidth={hoveredLocation && locations.find(loc => loc.id === hoveredLocation)?.connectionLines?.includes('line-usa') ? "2" : "1"}
                    className="tw-transition-all tw-duration-500"
                  />
                  <path d="M266.305 197.648C266.305 198.477 265.635 199.148 264.805 199.148C263.975 199.148 263.305 198.477 263.305 197.648C263.305 196.82 263.975 196.148 264.805 196.148C265.635 196.148 266.305 196.82 266.305 197.648Z"
                    fill="white"
                    className="tw-transition-all tw-duration-500" />
                  <path d="M212.895 144.5C212.895 145.328 212.225 146 211.395 146C210.565 146 209.895 145.328 209.895 144.5C209.895 143.671 210.565 143 211.395 143C212.225 143 212.895 143.671 212.895 144.5Z"
                    fill="white"
                    className="tw-transition-all tw-duration-500" />
                </g>

                {/* Tooltips */}
                {hoveredLocation && (
                  <g className="tooltip-group">
                    {(() => {
                      const location = locations.find(loc => loc.id === hoveredLocation);
                      if (!location) return null;

                      const { x, y } = location.tooltipPosition;

                      return (
                        <g
                          className="tw-opacity-0 tw-animate-pulse"
                          style={{
                            animation: 'fadeIn 0.3s ease-in-out forwards'
                          }}
                        >
                          {/* Tooltip Background */}
                          <rect
                            x={x - 85}
                            y={y - 65}
                            width={170}
                            height={75}
                            rx="10"
                            fill="white"
                            stroke="none"
                            style={{
                              filter: 'drop-shadow(0 8px 32px rgba(0, 0, 0, 0.12))'
                            }}
                          />

                          {/* Location Name */}
                          <text
                            x={x - 75}
                            y={y - 35}
                            textAnchor="start"
                            fill="#1F2937"
                            fontSize="18"
                            fontWeight="600"
                            className="tw-font-semibold"
                          >
                            {location.name}
                          </text>

                          {/* Divider Line */}
                          <line
                            x1={x - 75}
                            y1={y - 25}
                            x2={x + 75}
                            y2={y - 25}
                            stroke="#E5E7EB"
                            strokeWidth="1"
                          />

                          {/* Country */}
                          <text
                            x={x - 75}
                            y={y - 8}
                            textAnchor="start"
                            fill="#6B7280"
                            fontSize="14"
                            className="tw-font-normal"
                          >
                            {location.country}
                          </text>

                          {/* Type */}
                          <text
                            x={x + 75}
                            y={y - 8}
                            textAnchor="end"
                            fill="#374151"
                            fontSize="14"
                            fontWeight="500"
                            className="tw-font-medium"
                          >
                            {location.type}
                          </text>
                        </g>
                      );
                    })()}
                  </g>
                )}

                <defs>
                  <linearGradient id="paint0_linear_8127_2628" x1="620.001" y1="268.719" x2="620.001" y2="1.73647" gradientUnits="userSpaceOnUse">
                    <stop stopColor="#1E1D28" />
                    <stop offset={1} stopColor="#AFB4C4" />
                  </linearGradient>
                  <clipPath id="clip0_8127_2628">
                    <rect width={1240} height="421.945" fill="white" transform="translate(0 0.0546875)" />
                  </clipPath>
                </defs>
              </svg>
            </div>
          </div>
          <Slider {...settings} className=" ">
            {locations?.map((item, index) => (
              <div
                key={index}
                className="lg:tw-p-3 tw-p-2 tw-h-full"
                onMouseEnter={() => setHoveredLocation(item.id)}
                onMouseLeave={() => setHoveredLocation(null)}
              >
                <LocationCard
                  country={item.country}
                  type={item.type}
                  name={item.name}
                  address={item.address}
                  phone={item.phone}
                />
              </div>
            ))}
          </Slider>


        </Container>
      </section >
    </>
  );
};
