"use client";
import Image from "next/image";
import React from "react";
export const TeamTestimonialCard = ({
    avatar,
    testimonial,
    name,
    position,
}) => {
    return (
        <div className="group tw-relative tw-inline-block  ">

            <div className="tw-bg-[url('/aboutPage/ValuesSection/leftTop.png')] tw-bg-cover tw-bg-no-repeat tw-bg-center tw-absolute tw-left-0 tw-top-0 tw-w-[100px] tw-h-[100px]" />
            <div className="tw-bg-[url('/aboutPage/ValuesSection/leftBottom.png')] tw-bg-cover tw-bg-no-repeat tw-bg-center tw-absolute tw-left-0 tw-bottom-0 tw-w-[100px] tw-h-[100px]" />
            <div className="tw-bg-[url('/aboutPage/ValuesSection/rightTop.png')] tw-bg-cover tw-bg-no-repeat tw-bg-center tw-absolute tw-right-0 tw-top-0 tw-w-[100px] tw-h-[100px]" />
            <div className="tw-bg-[url('/aboutPage/ValuesSection/rightBottom.png')] tw-bg-cover tw-bg-no-repeat tw-bg-center tw-absolute tw-right-0 tw-bottom-0 tw-w-[100px] tw-h-[100px]" />

            <div className="tw-relative tw-z-20 tw-rounded-[20px] tw-p-10 tw-bg-[#21212505]">
                <div className="tw-flex tw-flex-col tw-gap-y-6">
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-flex tw-flex-col tw-gap-1">
                            <h3 className="tw-text-[22px] tw-mb-0">{name}</h3>
                            <h4 className="tw-text-[18px] tw-mb-0">{position}</h4>
                        </div>
                        <div
                            className="tw-relative"

                        >
                            <div className="tw-absolute tw-inset-0 tw-flex tw-items-center tw-justify-center tw-z-0">
                                <div className="tw-absolute tw-w-[130%] tw-h-[130%] tw-rounded-full tw-border tw-border-[#212125]/10 tw-opacity-80" />
                                <div className="tw-absolute tw-w-[160%] tw-h-[160%] tw-rounded-full tw-border tw-border-[#212125]/10 tw-opacity-60" />
                            </div>
                            <Image
                                src={avatar}
                                alt={name}
                                width={60}
                                height={60}
                                className="tw-w-[60px] tw-h-[60px] tw-rounded-full tw-object-cover"
                            />

                        </div>

                    </div>

                    <p className="lg:tw-text-base tw-text-sm  tw-text-primary_gray tw-mb-0 tw-line-clamp-5">
                        {testimonial}
                    </p>
                </div>


            </div>
        </div>
    );
};
