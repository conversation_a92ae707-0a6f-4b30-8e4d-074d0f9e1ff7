const NEXT_PUBLIC_WEB_URL = {
    production: process.env.NEXT_PUBLIC_DEV_BACKEND,
    staging: process.env.NEXT_PUBLIC_DEV_BACKEND,
    local: process.env.NEXT_PUBLIC_LCL_BACKEND,
}

export const BASE_URL = NEXT_PUBLIC_WEB_URL[process.env.NEXT_PUBLIC_ENV]

const NEXT_PUBLIC_WEB = {
    production: process.env.NEXT_PUBLIC_PRODUCTION_WEB,
    staging: process.env.NEXT_PUBLIC_STAGING_WEB,
    local: process.env.NEXT_PUBLIC_LOCAL_WEB,
}

export const LIVE_URL = NEXT_PUBLIC_WEB[process.env.NEXT_PUBLIC_ENV]