"use client"
import React from 'react'

const OutlineButton = ({ title, className }) => {
    return (
        <>
            <button
                type="button"
                className={`ripple-fill-button tw-relative tw-group tw-inline-block tw-border-[.0938rem] tw-border-third_color tw-text-third_color tw-text-nowrap tw-font-medium ${className}`}
            >
                <span className="tw-relative tw-z-10 tw-mx-[10px] group-hover:tw-text-white">
                    {title}
                </span>
            </button>
        </>
    )
}

export default OutlineButton