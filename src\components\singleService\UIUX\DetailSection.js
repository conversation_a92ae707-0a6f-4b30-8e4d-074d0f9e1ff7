"use client";

import React, { useEffect, useRef } from "react";
import { Container } from "reactstrap";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import SplitType from "split-type";

gsap.registerPlugin(ScrollTrigger);

const DetailSection = ({ className, description }) => {
    const textRef = useRef(null);

    useEffect(() => {
        if (!textRef.current) return;

        // Split words
        const split = new SplitType(textRef.current, {
            types: "words",
            wordClass: "word",
        });

        // Set initial opacity to 0.2
        gsap.set(".word", {
            opacity: 0.4,
        });

        // Animate to full opacity on scroll with stagger
        gsap.to(".word", {
            opacity: 1,
            ease: "power2.out",
            stagger: 0.08,
            duration: 1.5,
            scrollTrigger: {
                trigger: textRef.current,
                start: "top 80%",
                end: "top 30%",
                scrub: true,
                //once: true  Ties animation to scroll progress
            },
        });

        return () => split.revert();
    }, []);

    return (
        <section className={`tw-bg-service-gradient-2 ${className}`}>
            <Container>
                <div
                    ref={textRef}
                    className="tw-text-white lg:tw-text-[36px] md:tw-text-[26px] tw-text-[20px] tw-font-bold tw-leading-[170%] tw-text-justify"
                >
                    {description}

                </div>
            </Container>
        </section>
    );
};

export default DetailSection;
