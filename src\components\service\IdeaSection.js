"use client";
import React, { useEffect, useState } from "react";
import { Container } from "reactstrap";
import Image from "next/image";
import researchIcon from "../../../public/workflowIcons/research.png";
import ideationIcon from "../../../public/workflowIcons/ideation.png";
import prototypeIcon from "../../../public/workflowIcons/prototype.png";
import buildIcon from "../../../public/workflowIcons/build.png";
import improveIcon from "../../../public/workflowIcons/improve.png";
import navIcon from "../../../public/workflowIcons/polygonIcon.svg";

export const IdeaSection = ({ className }) => {
  const [activeStep, setActiveStep] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) =>
        prev === workflowSteps.length - 1 ? 0 : prev + 1
      );
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  const workflowSteps = [

    {
      id: 0,
      title: "Discover",
      icon: ideationIcon,
      description:
        "Brainstorming design ideas for elevating overall user-experience.",
    },
    {
      id: 1,
      title: "Design",
      icon: prototypeIcon,
      description:
        "Simulating models - Testing designs - Iterating process for a finer-final results.",
    },
    {
      id: 2,
      title: "Develop in Sprints",
      icon: buildIcon,
      description:
        "Generating a resilient and more effective design structure with innovative concepts.",
    },
    {
      id: 3,
      title: "Test & Improve",
      icon: improveIcon,
      description:
        "Adopting Agile-model for a consistent improvement in design process and methodology.",
    },
    {
      id: 4,
      title: "Deploy & Maintain",
      icon: researchIcon,
      description:
        "Gathering industry insights for a data-driven design structure.",
    },
  ];

  return (
    <section className={className}>
      <Container className="tw-flex tw-flex-col tw-gap-[40px] lg:tw-gap-[50px]">
        <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
          <div className="tw-text-white tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            How We
            <span className="tw-text-secondary_color"> Work</span>
          </div>
          <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-[#FFFFFFB2] tw-font-inter tw-leading-[120%]">
            “A Smarter Way to Build – Powered by Agile Minds.
          </div>
        </div>

        <div className="tw-grid tw-gap-5 sm:tw-gap-6 lg:tw-gap-5 lg:tw-grid-cols-5 md:tw-grid-cols-3 tw-grid-cols-2">
          {workflowSteps.map((step, index) => (
            <React.Fragment key={step.id}>
              <div className="tw-flex tw-gap-5 sm:tw-gap-6 lg:tw-gap-5">
                <div
                  onClick={() => setActiveStep(step.id)}
                  className={`tw-flex tw-flex-col tw-items-center tw-w-full tw-py-[30px] tw-gap-[30px] sm:tw-py-10 sm:tw-gap-10 lg:tw-py-8 lg:tw-gap-8 tw-border tw-rounded-[20px] lg:tw-rounded-[25px] tw-cursor-pointer
                      tw-transition-colors tw-duration-200 tw-ease-in-out   
                      ${activeStep === step.id
                      ? "tw-bg-white  "
                      : "tw-border-[#FFFFFF4D]"
                    } `}
                >
                  <div className="tw-relative tw-h-[35px] tw-w-[35px] lg:tw-h-10 lg:tw-w-10 xl:tw-h-12 xl:tw-w-12">
                    <Image
                      fill
                      className="tw-object-contain"
                      src={step.icon}
                      alt={`${step.title} icon`}
                    />
                  </div>
                  <div className={`tw-text-base md:tw-text-xl tw-font-semibold ${activeStep === step.id
                    ? "tw-text-[#282828]" : "tw-text-white"} `}>
                    {step.title}
                  </div>
                </div>

                <div
                  className={`tw-hidden sm:tw-flex lg:tw-flex xl:tw-flex tw-relative tw-shrink xl:tw-shrink-0 tw-items-center  ${index < workflowSteps.length - 1 ? "" : "tw-invisible"
                    }`}
                >
                  <Image
                    src={navIcon}
                    alt="Arrow"
                    className="tw-object-contain"
                  />
                </div>
              </div>
            </React.Fragment>
          ))}
        </div>
        <div className="tw-max-w-[20rem] tw-mx-auto  tw-overflow-hidden md:tw-max-w-[100%]">
          <div key={activeStep} className="tw-flex tw-flex-col tw-text-center tw-items-center tw-gap-[15px]  tw-animate-slideInRight tw-overflow-hidden">
            <h3 className="tw-mb-0 tw-text-white tw-text-[24px] tw-font-medium tw-font-bricolageGrotesque tw-leading-[120%]">
              {workflowSteps[activeStep].title || ""}
            </h3>
            <p className="tw-text-[#FFFFFFCC] tw-text-[18px] tw-leading-[120%] tw-w-full">
              {workflowSteps[activeStep].description || ""}
            </p>
          </div>
        </div>
      </Container>
    </section>
  );
};
