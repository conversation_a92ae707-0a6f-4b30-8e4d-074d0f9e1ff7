import FillButton from '@/components/buttons/FillButton';
import Image from 'next/image';
import React from 'react';
import { Container } from 'reactstrap';

const HeroSection = () => {
  const images = [
    'https://images.unsplash.com/photo-1648260296289-ab882814a005',
    'https://images.unsplash.com/photo-1618788372246-79faff0c3742',
    'https://images.unsplash.com/photo-1534670007418-fbb7f6cf32c3',
    'https://plus.unsplash.com/premium_photo-1661589354357-f56ddf86a0b4',
    'https://images.unsplash.com/photo-1506097425191-7ad538b29cef',
    'https://plus.unsplash.com/premium_photo-1661326248013-3107a4b2bd91',
    'https://images.unsplash.com/photo-1587440871875-191322ee64b0',
    'https://images.unsplash.com/photo-1648260296289-ab882814a005',
    'https://images.unsplash.com/photo-1618788372246-79faff0c3742',
    'https://images.unsplash.com/photo-1534670007418-fbb7f6cf32c3',
    'https://plus.unsplash.com/premium_photo-1661589354357-f56ddf86a0b4',
    'https://images.unsplash.com/photo-1506097425191-7ad538b29cef',
  ];

  return (
    <section className="tw-bg-[#F7F9FB] tw-shadow-home_hero_section lg:tw-pb-[.9375rem] tw-pb-2">
      <div className='tw-bg-service-bg-img2  lg:tw-rounded-[1.25rem] tw-rounded-[.9375rem] lg:tw-mx-[.9375rem] tw-mx-2 '>
        <Container className="lg:tw-pt-[85px] md:tw-pt-[35px] xxs:tw-p-0">
          <div className=" tw-relative tw-flex tw-items-center tw-justify-center tw-overflow-hidden tw-w-full tw-drop-shadow-service_section lg:tw-h-[750px] md:tw-h-[450px] tw-h-[753px]">
            <div className="tw-relative tw-z-20 tw-w-full tw-overflow-hidden lg:tw-h-[750px] md:tw-h-[450px] tw-h-[753px]">
              <div className="tw-z-30 tw-absolute tw-top-0 tw-left-0 tw-w-full tw-animate-spin-slow-20 lg:tw-h-[1240px] md:tw-h-[715px] tw-h-[753px] w-rounded-full">
                {/* Desktop circle */}
                <Circle images={images} visible="desktop" radius={500} size={{ width: 204, height: 235 }} />
                {/* Mobile circle */}
                <Circle images={images} visible="mobile" radius={260} size={{ width: 105, height: 120 }} />
              </div>
              <div className="tw-z-50 tw-absolute tw-top-1/2 tw-left-1/2 -tw-translate-x-1/2 -tw-translate-y-1/2 tw-text-center 2xl:tw-mt-[145px] lg:tw-mt-[154px] md:tw-mt-[90px]">
                <h1 className='tw-font-semibold tw-font-bricolageGrotesque xl:tw-text-[70px] lg:tw-text-[60px] tw-text-[38px] tw-mb-0'>
                  UI/UX That Engages And Converts
                </h1>
                <FillButton title={'Let’s Design Something Amazing'} className={'tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-5 tw-py-[8px] tw-px-4 lg:tw-mt-[40px] tw-mt-[20px]'} />

              </div>
              <div className='tw-z-40 tw-bg-service-gradient tw-absolute -tw-bottom-2 tw-left-0 tw-w-full xl:tw-h-[230px] lg:tw-h-[180px] tw-h-[100px] md:tw-block tw-hidden ' />
            </div>
          </div>
        </Container>
      </div>
    </section>
  );
};

export default HeroSection;

const ImageComponent = ({ src, i, angle, size }) => (
  <div
    className="tw-relative"
    style={{
      width: `${size.width}px`,
      height: `${size.height}px`,
    }}
  >
    <Image
      src={src}
      alt={`Image ${i + 1}`}
      className="tw-object-cover tw-rounded-[14px] lg:tw-rounded-[30px]"
      style={{
        transform: `rotate(${angle + 90}deg)`,
        transformOrigin: 'center center',
      }}
      fill
    />
  </div>
);

const Circle = ({ images, radius, size, visible }) => {
  const isDesktop = visible === 'desktop';
  const isMobile = visible === 'mobile';

  return (
    <>
      {images.map((src, i) => {
        const angle = (360 / images.length) * i;
        const x = radius * Math.cos((angle * Math.PI) / 180);
        const y = radius * Math.sin((angle * Math.PI) / 180);
        const topOffset = size.height / 2;
        const leftOffset = size.width / 2;

        return (
          <div
            key={i}
            className={`tw-absolute ${isDesktop ? 'lg:tw-block tw-hidden' : ''} ${isMobile ? 'lg:tw-hidden tw-block' : ''}`}
            style={{
              top: `calc(50% + ${y}px - ${topOffset}px)`,
              left: `calc(50% + ${x}px - ${leftOffset}px)`,
            }}
          >
            <ImageComponent src={src} i={i} angle={angle} size={size} />
          </div>
        );
      })}
    </>
  );
};
