"use client";
import React from "react";
import { Container, Row, Col } from "reactstrap";
import { BlogCard } from "../card/BlogCard";
import profile from "../../../public/thoughts/profile.png";
import thoughts from "../../../public/thoughts/thoughts.png";
import BlogsSlider from "@/common/Slider/BlogsSlider";
import { RightArrowIcon } from "@/utils/icons";
import TextButton from "../buttons/TextButton";

export const BlogSection = ({ className }) => {

  const blogs = [
    {
      image: thoughts,
      category: "Development",
      readTime: "10 min read",
      date: "15 Apr, 2025",
      title:
        "CTO Consulting & Advisory Services: Expert Guidance for Your Business",
      author: {
        avatar: profile,
        name: "<PERSON><PERSON><PERSON> Italiya",
      },
    },
    {
      image: thoughts,
      category: "Development",
      readTime: "10 min read",
      date: "15 Apr, 2025",
      title:
        "CTO Consulting & Advisory Services: Expert Guidance for Your Business",
      author: {
        avatar: profile,
        name: "<PERSON><PERSON><PERSON>ali<PERSON>",
      },
    },
    {
      image: thoughts,
      category: "Development",
      readTime: "10 min read",
      date: "15 Apr, 2025",
      title:
        "CTO Consulting & Advisory Services: Expert Guidance for Your Business",
      author: {
        avatar: profile,
        name: "Daxesh Italiya",
      },
    },
  ];

  return (
    <section className={`customer-section-bg ${className}`}>
      <Container >
        <div
          className="tw-text-primary_black tw-font-bold tw-text-center tw-font-bricolageGrotesque tw-text-[26px] md:tw-text-[36px] 2xl:tw-text-[48px] lg:tw-mb-[50px] tw-mb-[28px] "
        >
          Thoughts and
          <span className="tw-text-primary_green"> Services</span>
        </div>
        <div className="lg:tw-block tw-hidden">
          <Row>
            {blogs.map((blog, index) => (
              <Col key={index} lg="4">
                <BlogCard
                  image={blog.image}
                  category={blog.category}
                  readTime={blog.readTime}
                  date={blog.date}
                  title={blog.title}
                  author={blog.author}
                />
              </Col>
            ))}
          </Row>
          <BottomButton />
        </div>
        <div className="lg:tw-hidden tw-block">
          <BlogsSlider blogs={blogs} />
          <BottomButton />
        </div>
      </Container>
    </section>
  );
};

const BottomButton = () => {
  return (
    <TextButton blackTitle='Explore' greenTitle='More Blogs' className='tw-mt-[67px] lg:tw-mt-[50px] lg:tw-text-[20px]' />
  );
};
