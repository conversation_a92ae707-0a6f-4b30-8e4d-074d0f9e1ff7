"use client"
import { RightArrowIcon } from '@/utils/icons'
import React from 'react'

const TextButton = ({ blackTitle, greenTitle, className }) => {
    return (
        <>
            <button
                className={`tw-w-full tw-font-normal tw-text-center tw-flex tw-gap-[10px] tw-items-center tw-justify-center tw-text-[#D2D2D2] ${className}`}
            >
                {blackTitle}
                <span className="tw-text-white"> {greenTitle}</span>
                <RightArrowIcon className={`tw-fill-white lg:tw-w-5 lg:tw-h-5 tw-w-4 tw-h-4 tw-mt-0.5`} />
            </button>
        </>
    )
}

export default TextButton