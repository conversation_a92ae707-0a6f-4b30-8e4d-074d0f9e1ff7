"use client";
import React, { useState } from "react";
import { Container, Row, Col, Button } from "reactstrap";
import { FaqItem } from "./FaqItem";
import FillButton from "@/components/buttons/FillButton";

export const FaqSection = ({ className }) => {
  const faqs = [
    {
      question: "What kind of IT services do you offer?",
      answer:
        "Our standard response time for critical issues is within one hour. For l",
    },
    {
      question: "What are your response times for support requests?",
      answer:
        "Our standard response time for critical issues is within one hour. For less urgent requests, we aim to respond within a few business hours. We also offer various service level agreements (SLAs) with guaranteed response times to suit different business needs.",
      isOpen: true,
    },
    {
      question: "How do you ensure the security of our data and systems?",
      answer:
        "Our standard response time for critical issues is within one hour. For l",
    },
    {
      question: "What are the benefits of using managed IT services?",
      answer:
        "Our standard response time for critical issues is within one hour. For l",
    },
    {
      question: "How do you handle after-hours or emergency support?",
      answer:
        "Our standard response time for critical issues is within one hour. For l",
    },
    {
      question: "What is your pricing structure?",
      answer:
        "Our standard response time for critical issues is within one hour. For l",
    },
  ];
  const [openIndex, setOpenIndex] = useState(0);

  const handleToggle = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  return (
    <Container
      tag="section"
      className={`tw-bg-primary_color tw-relative tw-w-full tw-overflow-hidden md:tw-overflow-visible tw-px-[60px] tw-max-md:tw-px-5 ${className}`}
    >
      <Row>
        <Col lg={6}>
          <div className="tw-flex tw-w-full tw-flex-col tw-items-stretch  tw-max-md:tw-mt-10">
            <div className="tw-max-w-full tw-w-[648px]">
              <div className="tw-w-full tw-font-bold ">
                <h2 className="tw-text-secondary_color lg:tw-text-5xl md:tw-text-4xl tw-text-[30px] tw-max-md:tw-text-[40px] tw-font-bold tw-font-bricolageGrotesque lg:tw-text-start tw-text-center">
                  {`FAQ's`}
                </h2>
                <h3 className="tw-text-white lg:tw-text-4xl md:tw-text-3xl tw-text-[26px] tw-text-tw-font-bold lg:tw-text-start tw-text-center">
                  Still have question? Lets talk
                </h3>
              </div>
              <p className="tw-text-white tw-text-sm tw-font-normal tw-leading-5 tw-mt-3.5  tw-mb-0 lg:tw-text-start tw-text-center">
                Lorem Ipsum is simply dummy text of the printing and typesetting
                industry. Lorem Ipsum has been the industrys standard dummy text
                ever since the 1500s, Lorem Ipsum is simply dummy text of the
                printing and typesetting industry.
              </p>
            </div>
            <div className=" lg:tw-justify-start tw-justify-center lg:tw-flex tw-hidden">
              <FillButton title={'Still Have Questions? Let’s Talk'} className={'!tw-bg-white !tw-border-none !tw-text-[#593D96]  xl:tw-mt-[50px] lg:tw-mt-[20px] tw-mt-[40px] tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-10 lg:tw-py-[8px] lg:tw-px-4 tw-px-10 tw-py-[13px]'} />
            </div>
          </div>
        </Col>
        <Col lg={6}>
          <div className="tw-w-full lg:tw-my-auto tw-my-10 tw-relative tw-z-10">
            {faqs.map((faq, index) => (
              <FaqItem
                key={index}
                question={faq.question}
                answer={faq.answer}
                isOpen={openIndex === index}
                onToggle={() => handleToggle(index)}
              />
            ))}
          </div>
        </Col>
        <div className="tw-flex lg:tw-justify-start tw-justify-center lg:tw-hidden ">
          <FillButton
            title={"Get Free Quote"}
            className={
              "tw-w-fit tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-10 lg:tw-py-[8px] lg:tw-px-4 tw-px-10 tw-py-[13px]"
            }
          />
        </div>
      </Row>
      {/* Right Side Gradient */}
      <div className=" tw-absolute tw-z-[1] tw-top-[16rem] md:tw-top-[13rem] -tw-right-[7rem] tw-blur-[80px] tw-w-[20rem] tw-h-[12rem] tw-bg-[#8C45FF66] tw-rounded-full" />
      {/* Left Side Gradient */}
      <div className="tw-absolute tw-z-[1] tw-top-[2rem] -tw-left-[7rem] tw-blur-[80px] tw-w-[20rem] tw-h-[12rem] tw-bg-[#8C45FF66] tw-rounded-full" />
    </Container>
  );
};
