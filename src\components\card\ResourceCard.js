import Image from "next/image";
import React from "react";

export const ResourceCard = ({
  image,
  category,
  fileType,
  fileTypeColor,
  fileTypeBorderColor,
  title,
  isSmall = false,
  totalCards = 0,
}) => {
  return (
    <div
      className={`tw-shadow-tech_card tw-rounded-[30px] tw-p-5 md:tw-p-6 xl:tw-p-[30px] tw-h-full tw-w-full tw-group tw-items-stretch ${isSmall
        ? totalCards === 3 ? "tw-grid sm:tw-grid-cols-[25%_1fr] lg:tw-grid-cols-[31%_1fr] xl:tw-grid-cols-2 tw-gap-5" : "tw-grid sm:tw-grid-cols-[25%_1fr] lg:tw-grid-cols-[31%_1fr] tw-gap-5"
        : "tw-grid tw-gap-5 md:tw-gap-6 xl:tw-gap-[30px] tw-items-start"
        }`}
    >
      {/* Image Section */}
      <div className={`${isSmall ? "sm:tw-block tw-hidden tw-relative tw-h-full tw-w-full " : `tw-relative tw-w-full tw-rounded-[10px] sm:tw-rounded-[15px] tw-overflow-hidden ${totalCards === 4 ? "tw-h-full" : "tw-aspect-[1.41]"}`}`}>
        {isSmall ? (
          <Image
            fill
            src={image}
            className="tw-object-cover tw-rounded-[10px] sm:tw-rounded-[15px] group-hover:tw-scale-[1.02] tw-transition-transform tw-duration-700 tw-ease-in-out"
            alt={title}
          />
        ) : (
          <Image
            fill
            src={image}
            className="tw-object-cover group-hover:tw-scale-[1.02] tw-transition-transform tw-duration-700 tw-ease-in-out"
            alt={title}
          />
        )}
      </div>

      {/* Content Section */}
      <div
        className={`tw-flex tw-flex-col tw-justify-center ${isSmall ? "tw-gap-[15px] lg:tw-gap-5" : "tw-gap-[15px] lg:tw-gap-5"
          }`}
      >
        <div className="tw-flex tw-items-center tw-gap-2.5">
          <div className="tw-text-[12px] tw-font-bold tw-border tw-px-2.5 tw-py-[5.5px] tw-text-primary_green tw-border-[#E0FBD7] tw-rounded-full">
            {category.toUpperCase()}
          </div>
          <div
            className="tw-border tw-px-2.5 tw-py-[5.5px] tw-rounded-full tw-text-[12px] tw-font-bold"
            style={{
              color: fileTypeColor,
              borderColor: fileTypeBorderColor,
            }}
          >
            {fileType}
          </div>
        </div>

        <h3 className={`tw-text-[18px] lg:tw-text-[20px] tw-leading-[1.3] tw-mb-0 tw-text-primary_black tw-font-bold group-hover:tw-text-primary_green tw-transition-colors tw-duration-700 tw-ease-in-out ${totalCards === 3 ? "tw-line-clamp-3" : "tw-line-clamp-2"} `}>
          {title}
        </h3>
      </div>
    </div>

  );
};
