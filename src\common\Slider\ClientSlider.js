"use client";
import { LeftArrowIcon, PlayIcon, RightArrowIcon } from "@/utils/icons";
import Image from "next/image";
import React, { useRef, useState } from "react";
import Slider from "react-slick";
import VideoModal from "../VideoModal";

const ClientSlider = ({ isArrow = true, isName = true, data = null, bgColorClassName = "tw-bg-primary_color" }) => {
  const sliderRef = useRef(null);
  const [count, setCount] = useState(0);
  const [isPlayVideo, setIsPlayVideo] = useState(false);
  const [videoUrl, setVideoUrl] = useState("");

  const settings = {
    dots: false,
    pauseOnHover: true,
    arrows: false,
    infinite: false,
    speed: 500,
    swipeToSlide: true,
    beforeChange: (oldIndex, newIndex) => setCount(newIndex),
  };

  const goToPrev = () => {
    if (count > 0) {
      sliderRef.current?.slickPrev();
    }
  };

  const goToNext = () => {
    if (count < data.length - 1) {
      sliderRef.current?.slickNext();
    }
  };

  return (
    <>
      <div className="custom-bg-gradient tw-rounded-[45px] tw-p-[3px]">
        <div className={`tw-h-full tw-w-full tw-max-w-full tw-rounded-[45px] tw-px-3 tw-pt-4 tw-pb-2.5 ${bgColorClassName}`}>
          <Slider {...settings} ref={sliderRef}>
            {data?.map((client, idx) => (
              <div key={idx} className="tw-relative tw-px-1">
                <div className="tw-relative tw-w-full tw-max-w-full tw-aspect-[443/540] lg:tw-block tw-flex tw-justify-center tw-rounded-[30px] tw-overflow-hidden">
                  <Image
                    src={client.image}
                    className="tw-object-cover tw-rounded-[30px]"
                    alt={`${client.name || "Image"}`}
                    fill
                  />
                </div>
                {client.url ? (
                  <div
                    className="tw-cursor-pointer tw-absolute tw-left-1/2 tw-top-1/2 tw-transform -tw-translate-x-1/2 -tw-translate-y-1/2"
                    onClick={() => {
                      setIsPlayVideo(true);
                      setVideoUrl(client?.url);
                    }}
                  >
                    <PlayIcon />
                  </div>
                ) : null}
                {isName || client.name ? (
                  <div className="tw-absolute tw-left-1/2 tw-bottom-3 tw-transform -tw-translate-x-1/2 -tw-translate-y-1/2 tw-text-white">
                    <p className="tw-font-bold tw-text-lg tw-block tw-text-center tw-mb-0">
                      {client.name}
                    </p>
                    <p className="tw-text-center tw-mb-0">{client.title}</p>
                  </div>
                ) : null}
              </div>
            ))}
          </Slider>
        </div>
      </div>

      {isArrow ? (
        <div className="tw-flex tw-justify-center tw-items-center tw-gap-5 tw-mt-5">
          <button
            className={`tw-rounded-full tw-p-[10px] tw-border ${count === 0 ? "tw-bg-transparent tw-border-white/70" : "tw-bg-secondary_color tw-border-transparent"
              }`}
            onClick={goToPrev}
            disabled={count === 0}
            aria-label="Previous"
          >
            <LeftArrowIcon
              className={`${count === 0 ? "tw-fill-secondary_color" : "tw-fill-white"
                }`}
            />
          </button>
          <button
            className={`tw-rounded-full tw-p-[10px] tw-border ${count === data.length - 1
              ? "tw-bg-transparent tw-border-white/70"
              : "tw-bg-secondary_color tw-border-transparent"
              }`}
            onClick={goToNext}
            disabled={count === data.length - 1}
            aria-label="Next"
          >
            <RightArrowIcon
              className={`tw-w-6 tw-h-6 ${count === data.length - 1
                ? "tw-fill-secondary_color"
                : "tw-fill-white"
                }`}
            />
          </button>
        </div>
      ) : null}
      {isPlayVideo && (
        <VideoModal
          isOpen={isPlayVideo}
          onClose={() => {
            setIsPlayVideo(false);
            setVideoUrl("");
          }}
          videoSrc={videoUrl}
        />
      )}
    </>
  );
};

export default ClientSlider;
