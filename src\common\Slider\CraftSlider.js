import React from "react";
import { Container } from "reactstrap";

import { SmallServiceCard } from "@/components/card/SmallServiceCard";

export const CraftSlider = ({ services }) => {
  return (
    <Container fluid className="!tw-p-0">
      <div className="tw-overflow-hidden tw-w-full">
      <Marquee dataList={services} />
      </div>
    </Container>
  );
};

const Marquee = ({ dataList }) => {
  const repeatedData = [...dataList, ...dataList];
  return (
    <div className="Marquee-content-craft">
      {repeatedData.map((service, index) => (
        <div
          className="tw-inline-flex tw-items-center tw-justify-center tw-shrink-0"
          key={index}
        >
          <SmallServiceCard icon={service.icon} title={service.title} />
        </div>
      ))}
    </div>
  );
};
