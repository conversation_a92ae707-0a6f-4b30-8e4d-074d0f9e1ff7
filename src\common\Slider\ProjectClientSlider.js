import Image from 'next/image'
import React from 'react'
import fond from '../../../public/projectLogos/fond.png';
import card from '../../../public/projectLogos/card.png';
import fitsol from '../../../public/projectLogos/fitsol.png';
import klao from '../../../public/projectLogos/klao.png';
import teektask from '../../../public/projectLogos/teektask.png';
import KP from '../../../public/projectLogos/KP.png';
import MindSmith from '../../../public/projectLogos/MindSmith.png';
import { Container } from 'reactstrap';

const logos = [
    { src: MindSmith, width: 143, height: 39, alt: 'MindSmith' },
    { src: fond, width: 123, height: 39, alt: 'Fond' },
    { src: card, width: 126, height: 43, alt: 'Card' },
    { src: fitsol, width: 107, height: 34, alt: 'Fitsol' },
    { src: teektask, width: 201, height: 39, alt: 'TeekTask' },
    { src: klao, width: 117, height: 29, alt: 'Klao' },
    { src: KP, width: 40, height: 40, alt: 'KP' },
];

const ProjectClientSlider = () => {
    return (
        <Container fluid className='!tw-p-0'>
            <div className='tw-flex tw-flex-col tw-items-center tw-justify-center xl:tw-pb-[44px] lg:tw-pb-[28px] tw-pb-[22px]'>
                <span className='md:tw-text-xl tw-text-sm tw-font-semibold tw-text-center tw-text-white'>
                    Our happy clients
                </span>
                <div className="Marquee">
                    <Marquee />
                </div>
            </div>

        </Container>
    )
}

export default ProjectClientSlider
const Marquee = () => {
    const repeatedLogos = [...logos, ...logos, ...logos];

    return (
        <div className="Marquee-content">
            {repeatedLogos.map((logo, index) => (
                <div className="Marquee-tag" key={index}>
                    <Image
                        src={logo.src}
                        width={logo.width}
                        height={logo.height}
                        alt={logo.alt}
                        className="tw-object-contain"
                    />
                </div>
            ))}
        </div>
    );
}