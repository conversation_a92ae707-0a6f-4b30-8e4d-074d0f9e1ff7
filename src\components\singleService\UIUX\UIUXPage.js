import React from "react";
import HeroSection from "./HeroSection";
import { ServicesSection } from "./ServicesSection";
import { DesignStackSection } from "./DesignStackSection";
import { BuiltSection } from "./BuiltSection";
import ExperienceSection from "./ExperienceSection";
import DetailSection from "./DetailSection";
import ProcessSection from "./ProcessSection";
import IndustrySection from "@/common/IndustrySection";
import { UIIconsList } from "@/utils/constant";
import CreativitySection from "./CreativitySection";
import { FaqSection } from "@/common/FaqSection";
import VideosSlider from "@/common/Slider/VideosSlider";
import { BlogSection } from "@/components/home/<USER>";

export const UIUXPage = () => {
  return (
    <>
      <HeroSection />
      <DetailSection
        className="md:tw-py-[100px] tw-py-[70px]"
        description={` At TST Technology, we craft digital experiences that connect with
                    users and elevate your brand. Our UI/UX services turn your goals into
                    intuitive, high-performing designs through a collaborative process—
                    from wireframes to final interfaces. Using the latest tools and best
                    practices, we create seamless, visually striking experiences, tested
                    and optimised for every device. Whether launching new or enhancing
                    existing products, we help you stand out and grow smarter.`}
      />
      <ProcessSection className="md:tw-py-[100px] tw-py-[70px]" />
      <ServicesSection className="md:tw-py-[100px] tw-py-[60px]" />
      <IndustrySection
        className=""
        iconsData={UIIconsList}
        title="UI/UX Design Solutions for Industry"
      />
      <DesignStackSection className="md:tw-pt-[100px] tw-pt-[60px]" />
      <BuiltSection className="md:tw-py-[150px] tw-py-[60px]" />
      <CreativitySection className="md:tw-pb-[250px] tw-pb-[160px]" />
      <FaqSection className="md:tw-py-[100px] tw-py-[40px]" />
      <ExperienceSection className="" />
      <VideosSlider className="md:tw-py-[100px] tw-py-[60px]" />
      <BlogSection className="md:tw-py-[50px] tw-pt-[70px] tw-pb-[40px]" />
    </>
  );
};
