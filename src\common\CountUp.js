"use client"
import React, { useState, useEffect, useRef } from 'react';

const CountUp = ({ start, end, duration }) => {
    const [count, setCount] = useState(start);
    const countRef = useRef(null);

    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        let startTime;
                        let progress;
                        let increment;

                        const startCount = () => {
                            startTime = Date.now();
                            progress = 0;
                            increment = Math.ceil((end - start) / (duration * 1000));
                            requestAnimationFrame(updateCount);
                        };

                        const updateCount = () => {
                            const elapsedTime = Date.now() - startTime;
                            progress += elapsedTime;

                            if (progress < duration * 1000) {
                                setCount((prevCount) => prevCount + increment);
                                requestAnimationFrame(updateCount);
                            } else {
                                setCount(end);
                            }
                        };

                        startCount();
                        observer.unobserve(countRef.current);
                    }
                });
            },
            { threshold: 1 } // Trigger when 50% of the component is in view
        );

        observer.observe(countRef.current);

        return () => {
            observer.disconnect();
        };
    }, [start, end, duration]);

    return <span ref={countRef}>{count}</span>;
};

export default CountUp;
