import React from "react";
import HeroSection from "./HeroSection";
import { ServicesSection } from "./ServicesSection";
import { StatsSection } from "./StatsSection";
import { FeaturesSection } from "./FeaturesSection";
import MapSection from "./MapSection";
import { TechStackSection } from "./TechStackSection";
import { HiringSection } from "./HiringSection";
import { BenefitsSection } from "./BenefitsSection";
import { TestimonialsSection } from "./TestimonialsSection";
import { PartnershipsSection } from "./PartnershipsSection";
import { ResourcesSection } from "./ResourcesSection";
import { FaqSection } from "@/common/FaqSection";
import { BlogSection } from "./BlogSection";
import IndustrySection from "@/common/IndustrySection";
import { AboutUsSection } from "./AboutUsSection";
import { fullIconsList, homeBenefitsData } from "@/utils/constant";
import { IdeaSection } from "../service/IdeaSection";
import TalkingTeamSection from "./TalkingTeamSection";

const Homepage = () => {
  return (
    <>
      <HeroSection />
      <HiringSection className="md:tw-py-[100px] tw-py-[30px]" />
      <StatsSection className="md:tw-py-[100px] tw-py-[60px]" />
      <ServicesSection className="md:tw-pt-[100px] tw-pt-[70px] tw-pb-[50px]" />
      {/* <AboutUsSection className="md:tw-py-[100px] tw-py-[60px]" /> */}


      {/* <FeaturesSection className="md:tw-py-[50px] tw-py-[40px]" /> */}
      <IdeaSection className="md:tw-py-[100px] tw-py-[60px]" />
      <IndustrySection
        className="md:tw-py-[50px] tw-py-[30px]"
        iconsData={fullIconsList}
        title="Innovating Across 20+ Industries"
      />
      <MapSection className="md:tw-pt-0 tw-pt-[30px]" />
      {/* <TechStackSection className="md:tw-py-[100px] tw-py-[70px]" /> */}

      {/* <BenefitsSection
        className="md:tw-py-[100px] tw-py-[30px]"
        data={homeBenefitsData}
      /> */}
      <TestimonialsSection
        isButton={true}
        className="md:tw-py-[100px] tw-py-[70px]"
        blackTitle="What Our "
        greenTitle="Client "
        endTitle="Say"
        subTitle="We don't just deliver software. We build long-term  partnerships. Here’s what our clients say about working with  OneBuild"
      />
      {/* <PartnershipsSection className="md:tw-py-[100px]  tw-py-[70px]" /> */}
      {/* <ResourcesSection className="md:tw-py-[50px] tw-py-[30px]" /> */}
      <FaqSection className="md:tw-py-[100px] tw-py-[40px]" />
      <TalkingTeamSection
        plainColorTitle={"Let’s Build Something Great – Talk to"}
        colorTitle={"Our Team"}
        description={"We offer a range of services. Whether you are a startup or an enterprise, you can choose from our services. We customise them for all our clients, so you get a high quality product."}
        className="  tw-max-w-5xl tw-mx-auto" />
      {/* <BlogSection className="md:tw-py-[50px] tw-pt-[70px] tw-pb-[40px]" /> */}
    </>
  );
};

export default Homepage;
