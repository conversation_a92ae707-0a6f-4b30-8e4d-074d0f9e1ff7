"use client";
import React from "react";
import { Container } from "reactstrap";
import valueImg from "/public/aboutPage/CultureAndValues/ValuesImage.png";
import { ValuesCard } from "@/components/card/ValuesCard";

export const ValuesSection = ({ className }) => {
  const values = [
    {
      image: valueImg,
      title: "Equality",
      rowSpan: 1,
      colSpan: 12,
      description: "No “sir, ma’am”. No “bosses”. Only Teammates.",
    },
    {
      image: valueImg,
      title: "Bravery",
      rowSpan: 1,
      colSpan: 12,
      description: "Take initiative for all new challenges.",
    },
    {
      image: valueImg,
      title: "Owner Mindset",
      rowSpan: 2,
      colSpan: 8,
      description:
        "It’s not “your” project, it’s “our” project. We take care of you as our own ❣️",
    },
    {
      image: valueImg,
      title: "Goal setting",
      rowSpan: 2,
      colSpan: 6,
      description:
        "Our job is not done until our job is done- all goals that are set are accomplished too.",
    },
    {
      image: valueImg,
      title: "Bookworms",
      rowSpan: 2,
      colSpan: 10,
      description:
        "Reading books and gathering knowledge consistently on upskilling, personality, life management and for fun to enjoy life!",
    },
    {
      image: valueImg,
      title: "100% Honesty",
      rowSpan: 2,
      colSpan: 6,
      description: "Truth always helps in the long run",
    },
    {
      image: valueImg,
      title: "Care for Everyone",
      rowSpan: 2,
      colSpan: 9,
      description:
        "No one cares how much you know until they know how much you care. We care :)",
    },
    {
      image: valueImg,
      title: "Always be best",
      rowSpan: 2,
      colSpan: 9,
      description: "Give everyone the most premium services we can",
    },
    {
      image: valueImg,
      title: "Rich experience",
      rowSpan: 1,
      colSpan: 12,
      description: "No “sir, ma’am”. No “bosses”. Only Teammates.",
    },
    {
      image: valueImg,
      title: "People come first",
      rowSpan: 1,
      colSpan: 12,
      description:
        "People’s dreams, comfort, safety, health, goals and family come first for us.",
    },
    {
      image: valueImg,
      title: "Everyone gets a chance",
      rowSpan: 2,
      colSpan: 7,
      description:
        "Mistakes don’t define a person- their efforts at improvement do.",
    },
    {
      image: valueImg,
      title: "Process, Productivity, Progress mindset",
      rowSpan: 2,
      colSpan: 10,
      description: "3P’s of TST Technology’s success- we follow without fail.",
    },
    {
      image: valueImg,
      title: "Not “you” and “I”, it’s “we”",
      rowSpan: 2,
      colSpan: 7,
      description:
        "We are a team, the teammates and the clients- no one is left alone.",
    },
    {
      image: valueImg,
      title: "Never compromise on ethics",
      rowSpan: 2,
      colSpan: 8,
      description: "We’ll do what’s right, always.",
    },
    {
      image: valueImg,
      title: "Honoring our commitments",
      rowSpan: 2,
      colSpan: 7,
      description: "What we say, we do.",
    },
    {
      image: valueImg,
      title: "It’s always “product”, not only “project”",
      rowSpan: 2,
      colSpan: 9,
      description:
        "Keeping overall product growth in mind beyond our services.",
    },
  ];

  return (
    <section
      className={`tw-bg-cover tw-bg-no-repeat tw-bg-center tw-bg-valuesBgMobile sm:tw-bg-valuesBgDesktop ${className}`}
    >
      <Container tag="section">
        <div className="tw-flex tw-flex-col tw-items-center md:tw-gap-2.5 tw-gap-2 tw-mb-[70px] tw-z-10">
          <h2 className="tw-text-primary_black md:tw-text-4xl tw-text-2xl tw-font-bold tw-text-center tw-font-bricolageGrotesque tw-mb-0">
            <span>Our </span>
            <span className="tw-text-primary_green"> Core Values</span>
          </h2>
          <p className="lg:tw-text-sm tw-text-[12px] tw-text-primary_gray tw-text-center tw-mb-0">
            These fundamental principles guide every decision we make and every
            relationship we build.
          </p>
        </div>

        <div className="xl:tw-hidden tw-block tw-columns-1 md:tw-columns-2 lg:tw-columns-3 tw-gap-5 md:tw-gap-6">
          {values?.map((value, index) => (
            <div
              key={index}
              className=" tw-transition-transform tw-duration-300 tw-ease-in-out hover:tw-shadow-lg tw-mb-5 md:tw-mb-6 tw-w-full tw-break-inside-avoid tw-h-fit"
            >
              <ValuesCard
                image={value.image}
                title={value.title}
                description={value.description}
                isHorizontal={value?.rowSpan === 1 ? true : false}
              />
            </div>
          ))}
        </div>

        {/* Banto Grid layout after xl */}
        <div className="tw-transition-transform tw-duration-300 tw-ease-in-out hover:tw-shadow-lg tw-hidden xl:tw-grid tw-grid-cols-24 tw-grid-rows-auto tw-gap-[30px]">
          {values?.map((value, index) => (
            <div
              key={index}
              style={{
                height: "auto",
                width: "100%",
                gridColumn: `span ${value?.colSpan}`,
                gridRow: `span ${value?.rowSpan}`,
              }}
            >
              <ValuesCard
                image={value.image}
                title={value.title}
                description={value.description}
                isHorizontal={value?.rowSpan === 1 ? true : false}
              />
            </div>
          ))}
        </div>
      </Container>
    </section>
  );
};
