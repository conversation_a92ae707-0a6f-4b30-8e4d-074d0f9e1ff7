import { LongDecorativeLinesIcon } from "@/utils/icons";
import philosophyImg from "../../../public/aboutPage/Philosophy/philosophy.png";
import Image from "next/image";
import { Container } from "reactstrap";

const PhilosophySection = ({ className = "" }) => {
    const data = [
        {
            title: "Understanding Before Building",
            description: "We invest time in truly understanding your vision, your users, and your goals, because smart development starts with smart planning. ’Add one more line of content here to make it look balance with other content box."
        },
        {
            title: "Agile, Focused Progress",
            description: "We work in structured, short phases, giving you frequent updates and real demos. You stay involved, and your product stays on track — evolving in the right direction, one milestone at a time."
        },
        {
            title: "Attention to Every Detail",
            description: "From wireframes to final testing, we sweat the small stuff. Because every detail — no matter how small — shapes the experience your users will remember."
        },
        {
            title: "Transparent Collaboration",
            description: "We see ourselves as an extension of your team. Through open communication, shared planning, and clear reporting, you’re never guessing — you’re always growing."
        },
    ]
    return (
        <section className={`${className} tw-bg-primary_color`}>
            <Container>

                <div className="tw-mx-auto tw-max-w-[54rem]">
                    <h1 className="tw-relative lg:tw-mb-[50px] tw-mb-[40px] tw-text-secondary_color tw-font-bold tw-text-center tw-font-bricolageGrotesque md:tw-text-[36px] tw-text-[26px] ">
                        <div className="tw-inline tw-relative ">
                            Our Development Philosophy
                            <LongDecorativeLinesIcon
                                fill={"#fff"}
                                className="tw-absolute -tw-right-5 425:tw-right-0 !tw-w-full"
                            />
                        </div>
                    </h1>
                    <p className="tw-text-white/80 tw-text-center">
                        {"At OneBuild, building great technology isn’t just about code — it’s about creating real value for real businesses. We believe the best digital products are made when a few simple principles come together"}
                    </p>
                </div>
                <div className="tw-mt-[100px] tw-grid tw-grid-cols-1 lg:tw-grid-cols-[minmax(480px,1fr)_2fr]
 tw-gap-2">
                    <div className="tw-flex tw-justify-center tw-items-center lg:tw-block tw-p-3">
                        <Image width={420} height={368} src={philosophyImg} alt="philosophy" />
                    </div>
                    <div className=" tw-grid tw-grid-cols-2 tw-gap-4 ">
                        {data.map((item) => <div className="tw-max-w-[19.5rem]" key={item.title}>
                            <div className=" tw-bg-[linear-gradient(90deg,#977FCB_0%,#784FAF_56.95%,#602A9A_100%)] tw-w-32 tw-h-1" />
                            <div className="py-3  tw-text-white">
                                <h2 className="tw-text-xl tw-font-normal">
                                    {item.title}
                                </h2>
                                <p className="tw-my-2 tw-text-white/60 tw-text-base">
                                    {item.description}
                                </p>
                            </div>
                        </div>)}

                    </div>
                </div>
            </Container>
        </section>
    );
}

export default PhilosophySection;