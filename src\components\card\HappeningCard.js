"use client";
import React, { useState } from "react";
import Image from "next/image";
import ModalSlider from "@/common/ModalSlider";

export const HappeningCard = ({ image, tags, title }) => {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <>
      <div onClick={() => setIsOpen(true)} className="tw-mb-2 tw-shadow-happenings_card tw-p-5 2xl:tw-p-[26px] tw-rounded-[30px] tw-h-full tw-flex tw-flex-col tw-gap-5 tw-group">
        <div className=" tw-aspect-[1.41] tw-w-full tw-rounded-[15px] tw-relative tw-overflow-hidden">
          <Image
            fill
            src={image}
            className="tw-object-cover group-hover:tw-scale-[1.02] tw-transition-transform tw-duration-700 tw-ease-in-out tw-rounded-[15px]"
            alt={title}
          />
        </div>

        <div className="tw-flex tw-flex-col tw-gap-2.5">
          <h3 className="group-hover:tw-text-primary_green tw-font-inter tw-transition-colors tw-duration-700 tw-ease-in-out tw-line-clamp-2 tw-mb-0 tw-text-primary_black tw-text-[16px]">
            {title}
          </h3>
          <div className="tw-flex tw-flex-wrap tw-gap-x-2 tw-gap-y-1">
            {tags?.map((tag, index) => (
              <span
                key={index}
                className="tw-text-primary_gray tw-font-inter tw-leading-[1.4] tw-text-[14px] tw-font-normal"
              >
                #{tag}
              </span>
            ))}
          </div>
        </div>
      </div>
      <ModalSlider isOpenModal={isOpen} setIsOpenModal={setIsOpen} />

    </>

  );
};
