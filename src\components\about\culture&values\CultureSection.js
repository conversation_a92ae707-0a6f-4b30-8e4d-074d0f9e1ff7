"use client";

import React, { useState } from "react";
import { Col, Container, Row } from "reactstrap";
import CountUp from "@/common/CountUp";

import img from "/public/aboutPage/CultureAndValues/SempleImage.png";

import ClientSlider from "@/common/Slider/ClientSlider";

export const CultureSection = ({ className }) => {
  const stats = [
    { value: "95", sign: "%", title: "Team Satisfaction" },
    { value: "4.7", sign: "", title: "Glassdoor Rating" },
    { value: "20", sign: "+", title: "Team Members" },
  ];

  const pictures = [
    {
      image: img,
    },
    {
      image: img,
    },
    {
      image: img,
    },
  ];

  return (
    <section className={` ${className}`}>
      <Container tag="section">
        <div className="tw-flex tw-flex-col tw-items-center md:tw-gap-2.5 tw-gap-2 tw-mb-[70px]">
          <h2 className="tw-text-primary_black md:tw-text-4xl tw-text-2xl tw-font-bold tw-text-center tw-font-bricolageGrotesque tw-mb-0">
            <span>Our </span>
            <span className="tw-text-primary_green"> Culture Overview</span>
          </h2>
          <p className="lg:tw-text-sm tw-text-[12px] tw-text-primary_gray tw-text-center tw-mb-0">
            Our culture is built on open communication, continuous learning, and
            boundless creativity. We foster a respectful environment where
            everyone feels empowered to share, grow, and innovate.
          </p>
        </div>

        <Row className="g-5">
          <Col lg={7} className="lg:tw-order-1 tw-order-2">
            <div className="tw-flex tw-flex-col xl:tw-gap-8 lg:tw-gap-5 tw-gap-7 tw-items-start tw-w-full">
              <p className="tw-font-inter tw-text-primary_black tw-text-[18px] md:tw-text-[22px] xl:tw-text-[30px] tw-leading-[1.3] tw-font-normal tw-mb-0 lg:tw-text-start tw-text-center">
                At TST Technology, our culture is rooted in open communication,
                continuous learning, and boundless creativity. We believe mutual
                respect creates a strong foundation where every voice is heard.
                By embracing diversity, we fuel innovation and encourage every
                team member to share ideas, challenge norms, and bring their
                unique talents forward. When people feel valued and empowered,
                they don’t just meet expectations - they exceed them and inspire
                those around them.
              </p>

              <div className="tw-self-stretch tw-grid tw-grid-cols-3 tw-gap-x-6 tw-items-center tw-text-center ">
                {stats.map((stat, index) => (
                  <div
                    key={index}
                    className="tw-relative tw-flex tw-flex-col tw-items-center"
                  >
                    <h3 className="tw-text-primary_black tw-text-[24px] md:tw-text-[36px] lg:tw-text-[48px] xl:tw-text-[70px] tw-font-bricolageGrotesque tw-font-semibold tw-leading-[1.3] tw-mb-2">
                      <CountUp start={0} end={stat?.value || 0} duration={30} />
                      {stat?.sign}
                    </h3>
                    <p className="tw-text-primary_gray tw-text-[12px] md:tw-text-[14px] lg:tw-text-[16px] xl:tw-text-[20px] tw-font-inter tw-font-normal tw-leading-[1.2] tw-mb-0">
                      {stat.title}
                    </p>
                    {index !== stats.length - 1 && (
                      <span className="tw-h-12 md:tw-h-14 lg:tw-h-16 xl:tw-h-20 tw-w-px tw-absolute tw-top-1/2 tw-right-[-12px] tw-bg-[#DFE4E8] tw-transform tw--translate-y-1/2" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </Col>
          <Col lg={5} className="lg:tw-order-2 tw-order-1">
            <ClientSlider isPlayIcon={false} data={pictures} />
          </Col>
        </Row>
      </Container>
    </section>
  );
};
