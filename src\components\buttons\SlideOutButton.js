"use client"
import { ExploreArrowIcon } from '@/utils/icons'
import React from 'react'

const SlideOutButton = ({ title }) => {
    return (
        <>
            <button className="SlideOutButton tw-group">
                <div className="SlideOutButton-text">{title}</div>
                <span className="SlideOutButton-sign">
                    <ExploreArrowIcon className="tw-w-[24px] tw-h-[24px] tw-stroke-primary_green group-hover:tw-stroke-white" />
                </span>
            </button>
        </>
    )
}

export default SlideOutButton