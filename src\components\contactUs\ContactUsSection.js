"use client";

import React from "react";
import { Col, Container, Row } from "reactstrap";

import FillButton from "@/components/buttons/FillButton";

import ClientSlider from "@/common/Slider/ClientSlider";

import clientImg2 from "/public/client/c2.png";
import clientImg1 from "/public/client/c1.png";
import clientImg3 from "/public/client/c3.png";

const clients = [
  {
    name: "<PERSON><PERSON><PERSON>",
    title: "Founder and CEO of CAARD",
    image: clientImg2,
    url: "https://cdn.tsttechnology.in/Dhai<PERSON>_Shah_TST_Testomonial_dfaeb720cc.mp4",
  },
  {
    name: "<PERSON>",
    title: "Co-founder of CAARD",
    image: clientImg3,
    url: "https://cdn.tsttechnology.in/Whats_App_Video_2025_02_25_at_11_22_58_464437f7e6.mp4",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    title: "Director of engineering at Stockpe",
    image: clientImg1,
    url: "https://cdn.tsttechnology.in/TST_Testimonial_c2c47b99c0.mp4",
  },
];

export const ContactUsSection = ({ className }) => {
  return (
    <section
      className={`tw-py-[40px] lg:tw-py-[70px] xl:tw-py-[100px] tw-bg-[#F7F9FB] ${className}`}
    >
      <Container tag="section">
        <Row className="g-4 g-xl-5">
          <Col xs="12" lg="7">
            <div className="tw-flex tw-flex-col lg:tw-items-start tw-items-center tw-gap-[40px] md:tw-gap-[45px] lg:tw-gap-[50px] xl:tw-gap-[60px] tw-w-full ">
              <Title
                title="Contact Us"
                tagLine="Your vision, our expertise — let's make it happen."
              />

              <form className="tw-w-full tw-items-stretch tw-flex tw-flex-col tw-px-2 sm:tw-px-0">
                <Row className="g-4">
                  <Col xs="12" sm="6">
                    <div>
                      <div className="tw-flex tw-justify-between tw-items-center tw-mb-2.5">
                        <label className="tw-text-primary_black tw-font-inter tw-font-normal tw-text-[14px] tw-leading-[1.2] tw-align-middle">
                          Name <span className="tw-text-[#F96565]">*</span>
                        </label>
                      </div>
                      <input
                        type="text"
                        className="tw-w-full tw-rounded-[12px] tw-border tw-border-[#DFE4E8] tw-bg-white tw-p-[17px_16px] tw-text-[14px] tw-leading-[120%] tw-font-inter tw-font-normal tw-placeholder-[#DFE4E8] tw-placeholder:tw-font-inter tw-placeholder:tw-font-normal tw-placeholder:tw-text-[14px] tw-placeholder:tw-leading-[120%] tw-h-[50px]"
                        placeholder="Enter name"
                        required
                      />
                    </div>
                  </Col>

                  <Col xs="12" sm="6">
                    <div>
                      <div className="tw-flex tw-justify-between tw-items-center tw-mb-2.5">
                        <label className="tw-text-primary_black tw-font-inter tw-font-normal tw-text-[14px] tw-leading-[1.2] tw-align-middle">
                          Email <span className="tw-text-[#F96565]">*</span>
                        </label>
                      </div>
                      <input
                        type="text"
                        className="tw-w-full tw-rounded-[12px] tw-border tw-border-[#DFE4E8] tw-bg-white tw-p-[17px_16px] tw-text-[14px] tw-leading-[120%] tw-font-inter tw-font-normal tw-placeholder-[#DFE4E8] tw-placeholder:tw-font-inter tw-placeholder:tw-font-normal tw-placeholder:tw-text-[14px] tw-placeholder:tw-leading-[120%] tw-h-[50px]"
                        placeholder="Enter email"
                        required
                      />
                    </div>
                  </Col>

                  <Col xs="12" sm="6">
                    <div>
                      <div className="tw-flex tw-justify-between tw-items-center tw-mb-2.5">
                        <label className="tw-text-primary_black tw-font-inter tw-font-normal tw-text-[14px] tw-leading-[1.2] tw-align-middle">
                          Phone Number
                        </label>
                      </div>
                      <input
                        type="email"
                        className="tw-w-full tw-rounded-[12px] tw-border tw-border-[#DFE4E8] tw-bg-white tw-p-[17px_16px] tw-text-[14px] tw-leading-[120%] tw-font-inter tw-font-normal tw-placeholder-[#DFE4E8] tw-placeholder:tw-font-inter tw-placeholder:tw-font-normal tw-placeholder:tw-text-[14px] tw-placeholder:tw-leading-[120%] tw-h-[50px]"
                        placeholder="Enter phone number"
                      />
                    </div>
                  </Col>

                  <Col xs="12" sm="6">
                    <div>
                      <div className="tw-flex tw-justify-between tw-items-center tw-mb-2.5">
                        <label className="tw-text-primary_black tw-font-inter tw-font-normal tw-text-[14px] tw-leading-[1.2] tw-align-middle">
                          Country
                        </label>
                      </div>
                      <input
                        type="text"
                        className="tw-w-full tw-rounded-[12px] tw-border tw-border-[#DFE4E8] tw-bg-white tw-p-[17px_16px] tw-text-[14px] tw-leading-[120%] tw-font-inter tw-font-normal tw-placeholder-[#DFE4E8] tw-placeholder:tw-font-inter tw-placeholder:tw-font-normal tw-placeholder:tw-text-[14px] tw-placeholder:tw-leading-[120%] tw-h-[50px]"
                        placeholder="Enter country"
                      />
                    </div>
                  </Col>

                  <Col xs="12">
                    <div>
                      <div className="tw-flex tw-justify-between tw-items-center tw-mb-2.5">
                        <label className="tw-font-inter tw-font-normal tw-text-[14px] tw-leading-[120%] tw-align-middle">
                          Message
                        </label>
                      </div>
                      <textarea
                        rows="4"
                        className="tw-appearance-none tw-w-full tw-rounded-[12px] tw-border tw-border-[#DFE4E8] tw-bg-white tw-p-[17px_16px] tw-text-[14px] tw-leading-[120%] tw-font-inter tw-font-normal tw-placeholder-[#DFE4E8] tw-placeholder:tw-font-inter tw-placeholder:tw-font-normal tw-placeholder:tw-text-[14px] tw-placeholder:tw-leading-[120%] tw-h-full"
                        placeholder="Enter message"
                      ></textarea>
                    </div>
                  </Col>
                </Row>
              </form>
              <FillButton
                title={"Submit"}
                className={
                  "tw-w-fit tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-10 lg:tw-py-[8px] lg:tw-px-4 tw-px-10 tw-py-[13px]"
                }
              />
            </div>
          </Col>
          <Col xs="12" lg="5">
            <div className="px-sm-4">
              <ClientSlider data={clients} bgColorClassName="tw-bg-[#F7F9FB]" />
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

const Title = ({ title, tagLine }) => {
  return (
    <div className="tw-w-full tw-text-center md:tw-text-left tw-flex tw-flex-col tw-items-center lg:tw-items-start lg:tw-gap-2.5 tw-gap-2">
      <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
        {title}
      </div>
      <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-leading-[140%]">
        {tagLine}
      </div>
    </div>
  );
};
