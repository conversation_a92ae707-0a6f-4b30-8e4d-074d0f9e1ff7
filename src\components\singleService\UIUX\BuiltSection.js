import React from "react";
import { Col, Container, Row } from "reactstrap";

import { BuiltCard } from "@/components/card/BuiltCard";

import experience from "/public/BuiltSection/experience.png";
import user from "/public/BuiltSection/user.png";
import innovative from "/public/BuiltSection/innovative.png";
import collaborative from "/public/BuiltSection/collaborative.png";
import transperent from "/public/BuiltSection/transperent.png";
import design from "/public/BuiltSection/design.png";
import prov from "/public/BuiltSection/prov.png";
import FillButton from "@/components/buttons/FillButton";

export const BuiltSection = ({ className }) => {
  const featuresData = [
    {
      icon: experience,
      title: "Experienced Team",
      description:
        "Our team of designers brings years of experience and a proven track record of success in UI/UX design across various industries.",
    },
    {
      icon: user,
      title: "User-Centered Approach",
      description:
        "We prioritise the needs and preferences of your users, ensuring that our designs are visually appealing, highly functional, and user-friendly.",
    },
    {
      icon: innovative,
      title: "Innovative Solutions",
      description:
        "We stay ahead of industry trends and utilize the latest design tools and technologies to deliver perfect solutions.",
    },
    {
      icon: collaborative,
      title: "Collaborative Process",
      description:
        "We work closely with our clients throughout the design process, ensuring that their vision is realised and their goals are met.",
    },
    {
      icon: transperent,
      title: "Transparent Communication",
      description:
        "We maintain clear and open communication throughout the project to ensure alignment and satisfaction.",
    },
    {
      icon: design,
      title: "Design Strategy",
      description:
        "We consider every aspect of the user journey to create immersive experiences.",
    },
    {
      icon: prov,
      title: "Proven Methodologies",
      description:
        "We use established design principles and methodologies to ensure optimal results.",
    },
  ];

  return (
    <section className={`tw-bg-white ${className}`}>
      <Container>
        <Row className=" g-3 g-md-4 g-xl-5">
          <Col xxl="4" xl="5" md="6" xs="12">
            <Title />
          </Col>

          {featuresData.map((feature, index) => (
            <Col key={index} xxl="4" xl="5" md="6" xs="12">
              <BuiltCard
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
              />
            </Col>
          ))}
        </Row>
         <div className="md:tw-hidden tw-block tw-my-auto tw-text-center tw-py-10">
        <FillButton
          title={"Experience Our Work"}
          className="tw-w-fit tw-rounded-[12px] tw-px-10 tw-py-[13px] lg:tw-px-4 lg:tw-py-[8px] xl:tw-px-10 xl:tw-py-[15px]"
        />
      </div>
      </Container>
    </section>
  );
};

const Title = () => {
  return (
    <div className="tw-pb-10 md:tw-pb-0 tw-flex tw-flex-col tw-text-center md:tw-text-left tw-items-center md:tw-items-start tw-justify-between tw-h-full">
      <div className="tw-text-primary_green tw-font-bold tw-font-bricolageGrotesque tw-leading-[120%] tw-text-[26px] md:tw-text-[32px] xl:tw-text-[36px]">
        Built for Users
        <span className="tw-text-primary_black"> Designed to Perform</span>
      </div>
      <div className="tw-hidden md:tw-block">
        <FillButton
          title={"Experience Our Work"}
          className="tw-w-fit tw-rounded-[12px] tw-px-10 tw-py-[13px] lg:tw-px-4 lg:tw-py-[8px] xl:tw-px-10 xl:tw-py-[15px]"
        />
      </div>
    </div>
  );
};
