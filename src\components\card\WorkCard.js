import React from "react";
import Image from "next/image";
import { ExploreArrowIcon } from "@/utils/icons";
import SlideOutButton from "../buttons/SlideOutButton";
export const WorkCard = ({ title, image }) => {
  return (
    <div className=" tw-flex tw-justify-center tw-relative md:tw-bg-project-card-img tw-bg-project-card-425-img tw-bg-cover tw-bg-no-repeat tw-bg-top md:tw-w-[610px] 425:tw-w-[362px] 375:tw-w-[340px] 360:tw-w-[328px] tw-w-[285px] md:tw-h-[510px] 425:tw-h-[305px] 375:tw-h-[287px] 360:tw-h-[277px] tw-h-[240px]">
      <div className="tw-relative tw-aspect-square md:tw-h-[400px] 425:tw-h-[237px] 375:tw-h-[223px] 360:tw-h-[215px] tw-h-[187px] md:tw-w-[550px] 425:tw-w-[324px] 375:tw-w-[315px] 360:tw-w-[295px] tw-w-[255px] ">
        <Image
          fill
          src={image}
          alt="Full Image"
          className="tw-object-fill"
        />

        <div className="tw-absolute tw-z-30 md:-tw-bottom-[77px] 375:-tw-bottom-[49px] -tw-bottom-[41px] tw-w-full tw-flex tw-justify-between tw-items-center ">
          <h3 className="tw-font-bricolageGrotesque md:tw-text-3xl tw-text-xl tw-font-bold tw-leading-[120%] tw-mb-0">Fond</h3>
          <SlideOutButton title={'View More'} />
        </div>
      </div>
    </div>

  );
};


