import React from "react";

export const FeatureCard = ({icon, title, description}) => {
  return (
    <div className="tw-flex tw-flex-col tw-items-stretch tw-text-center">
      <img
        src={icon}
        className="tw-aspect-[1] tw-object-contain tw-w-[60px] tw-self-center tw-rounded-[10px]"
        alt={title}
      />
      <div className="tw-w-full tw-mt-10">
        <div className="tw-text-[rgba(33,33,37,1)] tw-text-2xl tw-font-medium tw-leading-none tw-tracking-[-0.48px]">
          {title}
        </div>
        <div className="tw-text-[rgba(131,131,135,1)] tw-text-lg tw-font-normal tw-leading-6 tw-tracking-[-0.36px] tw-mt-2">
          {description}
        </div>
      </div>
    </div>
  );
};
