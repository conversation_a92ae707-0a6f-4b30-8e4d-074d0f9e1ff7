import React from "react";
import { Col, Container, Row } from "reactstrap";

import countriesIcon from "/public/aboutPage/states/countriesIcon.png";
import happyUsersIcon from "/public/aboutPage/states/happyUsersIcon.png";
import industiresIcon from "/public/aboutPage/states/industiresIcon.png";
import industryExportsIcon from "/public/aboutPage/states/industryExportsIcon.png";
import yearInBussinessIcon from "/public/aboutPage/states/yearInBussinessIcon.png";

import { AboutStatCard } from "../card/AboutStatCard";

export const StatsSection = ({ className }) => {
  const stats = [
    {
      icon: happyUsersIcon,
      value: "80",
      sign: "",
      label: "Happy end-users",
      gradientClass: "tw-bg-white-to-green",
    },
    {
      icon: yearInBussinessIcon,
      value: "5",
      label: "Years in business",
      gradientClass: "tw-bg-white-to-pink",
    },
    {
      icon: industryExportsIcon,
      value: "20",
      sign: "+",
      label: "Industry Experts",
      gradientClass: "tw-bg-white-to-purple",
    },
    {
      icon: countriesIcon,
      value: "7",
      sign: "+",
      label: "Countries Served",
      gradientClass: "tw-bg-white-to-yellow",
    },
    {
      icon: industiresIcon,
      value: "20",
      sign: "",
      label: "Industry Served",
      gradientClass: "tw-bg-white-to-blue",
    },
  ];

  return (
    <section className={` ${className}`}>
      <Container>
        <Row className="justify-content-center row-cols-3 row-cols-md-5 g-3 g-md-4 g-lg-4 g-xl-5">
          {stats.map((stat, index) => (
            <Col key={index}>
              <AboutStatCard data={stat} />
            </Col>
          ))}
        </Row>
      </Container>
    </section>
  );
};
