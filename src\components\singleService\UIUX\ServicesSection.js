import Image from "next/image";
import React from "react";
import { Col, Container, Row } from "reactstrap";
import uiuxIcon from "../../../../public/serviceLogo/uiux.png";

export const ServicesSection = ({ className }) => {
  const uiuxservices = [
    {
      title: "Website Design and Redesign",
      description:
        "We create and update websites to be visually appealing and user-friendly, ensuring a seamless and engaging user experience.",
      icon: uiuxIcon,
    },
    {
      title: "Mobile App Design and Development",
      description:
        "Our team designs and develops engaging mobile app interfaces that are user-friendly and attractive.",
      icon: uiuxIcon,
    },
    {
      title: "User Interface (UI) Design",
      description:
        "We create attractive and easy-to-use interfaces that enhance the overall visual appeal and functionality of your digital products.",
      icon: uiuxIcon,
    },
    {
      title: "User Experience (UX) Design",
      description:
        "Our UX design services focus on enhancing user satisfaction by improving the usability and accessibility of your digital platforms.",
      icon: uiuxIcon,
    },
    {
      title: "User Research and Testing",
      description:
        "We conduct user interviews, surveys, and usability testing to gather insights that inform and refine our design decisions.",
      icon: uiuxIcon,
    },
    {
      title: "UI/UX Design for Custom Software",
      description:
        "Our team designs unique interfaces for custom software solutions, ensuring they meet the specific needs and preferences of your users.",
      icon: uiuxIcon,
    },
  ];

  return (
    <section className={` tw-bg-service-gradient-2 ${className}`}>
      <Container className="tw-flex tw-flex-col tw-gap-[40px] lg:tw-gap-[70px]">
        <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
          <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            Our UI UX Design
            <span className="tw-text-primary_green"> Services We Offer</span>
          </div>
          <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-w-[80%] tw-leading-[140%]">
            Improve your brand design with our expert UI UX design services. Our
            team creates engaging user interfaces that not only attract your
            audience but also drive business success.
          </div>
        </div>

        <Row className="justify-content-center g-3 g-lg-4">
          {uiuxservices.map((service, index) => (
            <Col key={index} xs="12" sm="6" md="6" lg="4" xl="4">
              <UiServiceCard
                icon={service.icon}
                title={service.title}
                description={service.description}
              />
            </Col>
          ))}
        </Row>
      </Container>
    </section>
  );
};
const UiServiceCard = ({ icon, title, description }) => {
  return (
    <div className="flip-wrapper  tw-relative tw-aspect-square">
      <div className="flip-inner tw-rounded-[30px]">
        {/* Front Side */}
        <div className="flip-front tw-bg-white tw-flex tw-flex-col tw-items-start tw-gap-y-5 xl:tw-p-10 tw-p-8  tw-border tw-border-[#DFE4E8] tw-w-full tw-h-full">
          <div className="tw-relative tw-aspect-square tw-w-[50px] lg:tw-w-[60px]">
            <Image fill src={icon} alt={title} className="tw-bg-contain" />
            <div
              className={`tw-absolute tw-inset-0 tw-bg-primary_green tw-mix-blend-overlay tw-opacity-100`}
            ></div>
          </div>
          <div className=" tw-flex tw-flex-col tw-items-start tw-justify-between tw-w-full tw-h-full">
            <h3 className="tw-font-medium tw-text-[26px] md:tw-text-[22px] xl:tw-text-[30px] tw-text-primary_black">
              {title}
            </h3>
            <p className="tw-font-normal lg:tw-text-sm xl:tw-text-lg tw-text-primary_gray tw-leading-[1.2] tw-mb-0">
              {description}
            </p>
          </div>
        </div>

        {/* Back Side */}
        <div className="flip-back">
          <div className="tw-relative tw-aspect-square ">
            <Image
              src="/UiUxServices/UiServiceFlip.png"
              alt="Flip Image"
              fill
              className="tw-object-contain tw-rounded-[30px]"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
