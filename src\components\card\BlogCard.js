import Image from "next/image";
import React from "react";

export const BlogCard = ({
  image,
  category,
  readTime,
  date,
  title,
  author,
  isSmall = false,
}) => {
  return (
    <div
      className={`tw-bg-white tw-shadow-happenings_card tw-p-5 md:tw-p-6 xl:tw-p-[30px] tw-rounded-[30px] tw-h-full tw-w-full tw-group ${isSmall
          ? "tw-grid md:tw-grid-cols-[1fr_33%] xl:tw-grid-cols-[1fr_33%] tw-gap-5"
          : "tw-grid tw-gap-5 md:tw-gap-6 xl:tw-gap-[30px]"
        }`}
    >
      {/* Text Section */}
      <div
        className={`tw-flex tw-flex-col ${isSmall
            ? "tw-gap-[15px] lg:tw-gap-2.5 xl:tw-gap-[15px]"
            : "order-2 tw-gap-5"
          }`}
      >
        <div className="tw-flex tw-justify-between tw-items-center">
          <div className="tw-flex tw-gap-2.5 tw-items-center">
            <div className="tw-px-2 tw-py-1 tw-text-[12px] md:tw-text-[14px] xl:tw-text-[14px] tw-text-primary_green tw-bg-[#D5F0D2] tw-rounded-lg">
              {category}
            </div>
            <div className="tw-text-primary_gray tw-text-[12px] xl:tw-text-[16px]">
              {readTime}
            </div>
          </div>
          <div className="tw-text-primary_black tw-text-[12px] xl:tw-text-[16px]">
            {date}
          </div>
        </div>

        <h3
          className={`group-hover:tw-text-primary_green tw-transition-colors tw-duration-700 tw-ease-in-out tw-text-primary_black tw-font-bold tw-leading-[1.3] tw-mb-0 ${ isSmall
              ? "tw-line-clamp-2 lg:tw-line-clamp-1 s1440:tw-line-clamp-2 tw-text-[16px] md:tw-text-[18px] lg:tw-text-[20px] 2xl:tw-text-[24px]"
              : "tw-line-clamp-2 lg:tw-line-clamp-3 xl:tw-line-clamp-2 tw-text-[16px] md:tw-text-[18px] lg:tw-text-[20px] 2xl:tw-text-[26px]"
            }`}
        >
          {title}
        </h3>

        <div className="tw-flex tw-items-center tw-gap-[15px]">
          <div
            className={`tw-relative tw-aspect-square ${isSmall ? "tw-w-[30px]" : "tw-w-10"
              }`}
          >
            <Image
              src={author.avatar}
              fill
              className="tw-rounded-full tw-object-contain"
              alt={author.name}
            />
          </div>
          <div className="tw-my-auto tw-text-[14px] md:tw-text-[16px] tw-text-primary_black tw-font-medium tw-leading-[1.4]">
            {author.name}
          </div>
        </div>
      </div>

      {/* Image Section */}
      {isSmall ? (
        <div className="md:tw-block tw-hidden tw-relative tw-h-full tw-w-full">
          <Image
            fill
            src={image}
            className="tw-object-cover tw-rounded-[15px] group-hover:tw-scale-[1.02] tw-transition-transform tw-duration-700 tw-ease-in-out"
            alt={title}
          />
        </div>
      ) : (
        <Image
          src={image} className="tw-order-1 tw-aspect-[1.41] tw-object-cover tw-w-full tw-rounded-[15px] group-hover:tw-scale-[1.02] tw-transition-transform tw-duration-700 tw-ease-in-out"
          alt={title}
        />
      )}
    </div>
  );
};
