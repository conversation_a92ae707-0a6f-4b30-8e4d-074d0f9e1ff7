
import React from 'react';
import { Container } from 'reactstrap';
import FillButton from '../buttons/FillButton';
import ProjectClientSlider from '@/common/Slider/ProjectClientSlider';
import ReadOnlyRating from '@/common/ReadOnlyRating';


const HeroSection = () => {
    return (
        <section className="tw-bg-partnersHeroBg tw-bg-no-repeat tw-bg-cover   lg:tw-pb-[.9375rem] tw-pb-2">
            {/*tw-bg-service-bg-img  tw-bg-no-repeat tw-bg-cover tw-bg-top*/}
            <div className='  lg:tw-rounded-[1.25rem] tw-rounded-[.9375rem] lg:tw-mx-[.9375rem] tw-mx-2 '>
                <Container>
                    <div className='tw-flex tw-flex-col tw-items-center tw-justify-center xl:tw-py-[120px] lg:tw-py-[100px] md:tw-py-[130px] tw-py-[100px] tw-text-center lg:tw-gap-[60px] tw-gap-[50px] tw-relative tw-top-[2rem] md:tw-top-[3rem] lg:tw-top-[5rem] xl:tw-top-[7.5rem] 2xl:tw-top-[9rem]'>
                        <div className='tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px]'>
                            {/* <div className='tw-rounded-full tw-bg-white/50 tw-py-[12px] tw-px-[15px] tw-flex tw-items-center tw-justify-center tw-gap-2'>
                                <ReadOnlyRating rating={3.5} />
                                <p className='md:tw-text-sm tw-text-[12px] tw-mb-0 tw-mt-0.5'>Rated 5/5 from over 700 views</p>
                            </div> */}
                            <h1 className='tw-bg-[linear-gradient(90.84deg,#FFFFFF_23.48%,#E2DDED_34.83%,#977FCB_76.78%)] tw-bg-clip-text tw-text-transparent  tw-mb-0 xl:tw-text-[70px] lg:tw-text-[55px] md:tw-text-[40px] tw-text-[34px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold md:tw-w-[90%]'>Who We Build For and
                                What We Help Them Achieve.</h1>
                            <p className='tw-mb-0 tw-text-[22px] lg:tw-text-xl md:tw-text-lg tw-text-base tw-text-[#D2D2D2] lg:tw-w-[60%] md:tw-w-3/4 tw-w-[90%] md:tw-leading-[140%] tw-leading-[120%]'>From FinTech to eCommerce, OneBuild powers the digital growth of ambitious businesses across industries. We’ve partnered with clients in 6+ countries — delivering scalable solutions, transparent communication, and results worth talking about.</p>
                        </div>
                        <div className="tw-p-2.5 tw-border tw-border-white/30 tw-rounded-[1.25rem]" >
                            <FillButton title={`Let's Connect`} className={'tw-bg-white !tw-text-[#433878] tw-rounded-[12px] xl:tw-py-2.5 xl:tw-px-5 tw-py-2 tw-px-7 lg:!tw-px-10 tw-border-none'} />
                        </div>
                    </div>
                </Container>
                <div className='tw-relative lg:tw-top-8 2xl:tw-top-14'>
                    <ProjectClientSlider />
                </div>

            </div>
        </section>
    );
};

export default HeroSection;


