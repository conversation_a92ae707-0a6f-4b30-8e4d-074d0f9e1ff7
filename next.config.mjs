/** @type {import('next').NextConfig} */
const withPWA = (await import('next-pwa')).default({
  dest: 'public',
  register: true,
  skipWaiting: true,
});

const nextConfig = {
  poweredByHeader: false,
  reactStrictMode: true,
  output: 'standalone',
  images: {
    unoptimized: false,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**', // Allows all HTTPS domains
      },
    ],
  },
  async rewrites() {
    return [
      {
        source: '/blog/:category',
        destination: '/blog?category=:category',
      },
      {
        source: '/resources-library/:category',
        destination: '/resources-library?category=:category',
      },
    ];
  },
};

export default withPWA(nextConfig);
