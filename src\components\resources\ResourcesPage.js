"use client"
import React from 'react'
import HeroSection from './HeroSection'
import { resourceCategories, resourceSections } from '@/utils/constant'
import { TabSection } from '../blog/TabSection'
import { ResourceCard } from '../card/ResourceCard'
import { ScaleSection } from './ScaleSection'

const ResourcesPage = () => {
    return (
        <>
            <HeroSection />
            <TabSection
                className="md:tw-py-[100px] tw-py-[70px]"
                blackText="Content That "
                greenText="Creates Impact"
                subTitle="Browse expert-made guides, templates, and case studies—built to help you move faster and build smarter."
                isInput={false}
                sections={resourceSections}
                categories={resourceCategories}
                pagePathname="/resources-library"
                CardComponent={(props) => <ResourceCard {...props} />}
                ExploreComponent={() => <ScaleSection />}
            />
            {/* <ScaleSection /> */}
        </>

    )
}

export default ResourcesPage