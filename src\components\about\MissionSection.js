import { MissionIcon, VisionIcon } from "@/utils/icons";
import React from "react";
import { Container } from "reactstrap";

const MissionSection = ({ className }) => {
  return (
    <section className="tw-bg-primary_color">
      <div className={`tw-relative tw-w-full lg:tw-h-full lg:tw-aspect-video md:tw-h-[573px] tw-h-[990px] md:tw-bg-cover tw-bg-top tw-bg-no-repeat md:tw-bg-mission-bg tw-bg-mission-bg-mobile ${className}`}>
        {/* Mission Box - Top Right */}
        <div className="tw-absolute xl:tw-right-[150px] xl:tw-top-[100px] md:tw-right-10 md:tw-top-10 tw-top-[180px] tw-right-5 md:tw-w-[382px] tw-w-[286px]">
          <div className="tw-flex tw-flex-col tw-gap-y-5">
            <MissionIcon fill="#B19FD8" className="xl:tw-w-[50px] xl:tw-h-[50px] tw-w-[40px] tw-h-[40px]" />
            <h3 className="md:tw-text-2xl tw-text-xl tw-font-medium tw-font-bricolageGrotesque tw-text-white">Our Mission</h3>
          </div>
          <p className="tw-text-white/80 md:tw-text-lg">
            We are committed to delivering high-impact SaaS solutions that enable
            entrepreneurs to embrace the digital age, optimize their operations,
            and drive sustainable growth.
          </p>
        </div>

        {/* Vision Box - Bottom Left */}
        <div className="tw-absolute xl:tw-bottom-[100px] xl:tw-left-[150px] md:tw-bottom-10 md:tw-left-10 md:tw-w-[382px] tw-bottom-[180px] tw-left-5 tw-w-[286px]">
          <div className="tw-flex tw-flex-col tw-gap-y-5">
            <VisionIcon fill="#B19FD8" className="xl:tw-w-[50px] xl:tw-h-[50px] tw-w-[40px] tw-h-[40px]" />
            <h3 className="md:tw-text-2xl tw-text-xl tw-font-medium tw-font-bricolageGrotesque tw-text-white">Our Vision</h3>
          </div>
          <p className="tw-text-white/80 md:tw-text-lg">
            Our vision is to create a world where technology empowers individuals
            and businesses alike — uplifting communities and enabling a smarter
            tomorrow.
          </p>
        </div>
      </div>
      <p className="tw-text-center tw-text-white/80 tw-mx-auto tw-max-w-[40rem]">
        We envision a future where building technology is seamless, empowering, <br /> and accessible — a world where anyone with a bold idea can build something  meaningful.
      </p>
    </section>
  );
};

export default MissionSection;
