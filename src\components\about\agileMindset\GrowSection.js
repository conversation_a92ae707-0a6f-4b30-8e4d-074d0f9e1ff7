import { Col, Container, Row } from "reactstrap";
import React from "react";

import fasterTimeIcon from "/public/contactUsPage/fasterTime.png";
import continuousClientIcon from "/public/contactUsPage/refresh.png";
import transparentProgressIcon from "/public/contactUsPage/search.png";
import reducedWasteIcon from "/public/contactUsPage/dev.png";
import flexibilityIcon from "/public/contactUsPage/flexibility.png";
import Image from "next/image";

export const GrowSection = ({ className }) => {
  const values = [
    {
      icon: fasterTimeIcon,
      title: "Faster Time to Market",
    },
    {
      icon: continuousClientIcon,
      title: "Continuous Client Feedback",
    },
    {
      icon: transparentProgressIcon,
      title: "Transparent Progress",
    },
    {
      icon: reducedWasteIcon,
      title: "Reduced Development Waste",
    },
    {
      icon: flexibilityIcon,
      title: "Flexibility to Pivot",
    },
  ];

  return (
    <section className={`tw-bg-[#F7F9FB] ${className}`}>
      <Container
        className={`tw-flex tw-flex-col tw-items-center tw-gap-[40px] lg:tw-gap-[70px] `}
      >
        <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
          <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            <span>Grow Smart </span>
            <span className="tw-text-primary_green">with Agile</span>
          </div>
          <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-leading-[120%]">
            Our clients choose Agile because it delivers real business value -
            faster, leaner, and with less risk.
          </div>
        </div>

        <Row className="justify-content-center g-4 g-xl-5">
          {values.map((value, index) => (
            <Col key={index} xs="12" sm="6" lg="4">
              <div className="tw-bg-white tw-rounded-[20px] md:tw-rounded-[30px] tw-shadow-happenings_card hover:tw-scale-105 tw-transition-all tw-duration-500 tw-flex tw-flex-col tw-items-center tw-h-full tw-p-[30px] md:tw-p-8 xl:tw-p-10 tw-gap-[30px] md:tw-gap-8 xl:tw-gap-10">
                <div className="tw-flex tw-justify-center tw-items-center tw-bg-primary_green tw-rounded-[10px] tw-aspect-square tw-w-[52px] lg:tw-w-14">
                  <Image
                    src={value.icon}
                    alt={value.title}
                    width={40}
                    height={40}
                  />
                </div>
                <h3 className="tw-font-medium tw-text-center tw-font-inter tw-text-[18px] lg:tw-text-[20px] tw-leading-[1.2] tw-text-primary_black tw-mb-0">
                  {value.title}
                </h3>
              </div>
            </Col>
          ))}
        </Row>
      </Container>
    </section>
  );
};
