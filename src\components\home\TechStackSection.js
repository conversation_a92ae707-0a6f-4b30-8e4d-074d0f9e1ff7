"use client";
import Image from "next/image";
import React, { useState } from "react";
import { Container, Row, Col, Button } from "reactstrap";
import cssLogo from "../../../public/techLogos/css.svg";
import htmlLogo from "../../../public/techLogos/html.svg";
import reactLogo from "../../../public/techLogos/react.svg";
import nextLogo from "../../../public/techLogos/nextjs.svg";
import gatsbyLogo from "../../../public/techLogos/gatsby.svg";
import Slider from "react-slick";

export const TechStackSection = ({ className }) => {
  const [activeCategory, setActiveCategory] = useState("Frontend");

  const categories = [
    "UI/UX",
    "Frontend",
    "Backend",
    "Mobile App",
    "CMS",
    "Database",
    "Cloud Platforms",
    "DevOps",
    "Automation Testing",
  ];

  // Frontend tech stack icons and names
  const frontendTech = [
    {
      icon: htmlLogo,
      name: "HTM<PERSON>",
    },
    {
      icon: reactLogo,
      name: "React JS",
    },
    {
      icon: nextLogo,
      name: "Next JS",
    },
    {
      icon: gatsbyLogo,
      name: "Gatsby",
    },
    {
      icon: reactLogo,
      name: "React JS",
    },
    {
      icon: nextLogo,
      name: "Next JS",
    },
    {
      icon: gatsbyLogo,
      name: "Gatsby",
    },
    {
      icon: htmlLogo,
      name: "HTML",
    },
    {
      icon: cssLogo,
      name: "CSS",
    },
    {
      icon: htmlLogo,
      name: "HTML",
    },
    {
      icon: reactLogo,
      name: "React JS",
    },
    {
      icon: nextLogo,
      name: "Next JS",
    },
    {
      icon: gatsbyLogo,
      name: "Gatsby",
    },
    {
      icon: reactLogo,
      name: "React JS",
    },
    {
      icon: nextLogo,
      name: "Next JS",
    },
    {
      icon: gatsbyLogo,
      name: "Gatsby",
    },
    {
      icon: htmlLogo,
      name: "HTML",
    },
    {
      icon: cssLogo,
      name: "CSS",
    },
  ];
  var settings = {
    dots: false,
    autoplay: true,
    autoplaySpeed: 1500,
    pauseOnHover: true,
    arrows: false,
    infinite: true,
    speed: 500,
    slidesToShow: 8,
    slidesToScroll: 1,
    swipeToSlide: true,
    responsive: [
      {
        breakpoint: 1440,
        settings: {
          slidesToShow: 7.5,
          slidesToScroll: 1,
          infinite: true,
          dots: false,
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 5,
          slidesToScroll: 1,
          infinite: true,
          dots: false,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
          initialSlide: 2,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
    ],
  };
  return (
    <section className={`tw-bg-tech-bg ${className}`}>
      <Container
        fluid
        tag="section"
        className="tw-flex tw-flex-col tw-items-center md:tw-gap-[50px] tw-gap-[40px]"
      >
        <div className="tw-flex tw-flex-col tw-items-center md:tw-gap-2.5 tw-gap-2">
          <h2 className="tw-text-primary_black md:tw-text-4xl tw-text-2xl tw-font-bold tw-text-center tw-font-bricolageGrotesque tw-mb-0">
            <span>
              Explore Our {" "}
            </span>
            <span className="tw-text-primary_green">
              Tech Stack
            </span>
          </h2>
          <p className="lg:tw-text-sm tw-text-[12px] tw-text-primary_gray tw-text-center lg:tw-w-[64%] md:tw-w-[83%] tw-w-[90%] tw-mb-0">
            Our team of developers quickly adapts to new technological innovations
            to provide best solutions to our clients. Our tech stack is AI-enhanced
            to deliver solutions better and quicker.
          </p>
        </div>

        <div className="tw-flex md:tw-gap-6 tw-gap-3 tw-justify-center tw-flex-wrap xl:tw-w-1/2 lg:tw-w-[86%]">
          {categories.map((category) => (
            <button
              key={category}
              className={` ${activeCategory === category
                ? "tw-bg-primary_green tw-shadow-button_green tw-text-white"
                : "tw-bg-white tw-shadow-button_white tw-text-primary_gray"
                } tw-gap-2.5 tw-whitespace-nowrap md:tw-px-5 md:tw-py-2.5 tw-px-[14px] tw-py-[9px] tw-rounded-[40px] md:tw-text-sm tw-text-[11px] tw-font-medium`}
              onClick={() => setActiveCategory(category)}
            >
              {category}

            </button>
          ))}
        </div>

      </Container>
      <Container fluid className="md:tw-mt-[40px] tw-mt-[35px]">
        <Row className="">
          <Slider {...settings} className="">
            {frontendTech.map((tech, index) => (
              <Col
                key={index}
                className=" "
              >
                <div className="tw-mx-[15px] tw-my-[10px] tw-h-full tw-bg-white tw-shadow-tech_card tw-flex tw-flex-col tw-items-center tw-whitespace-nowrap tw-justify-center tw-px-[51px] tw-py-5 tw-rounded-[20px] tw-max-md:tw-px-5">
                  <div className="tw-relative lg:tw-w-[64px] lg:tw-h-[56px] tw-w-[38px] tw-h-[32px]">
                    <Image
                      fill
                      src={tech.icon}
                      className="tw-object-contain"
                      alt={tech.name}
                    />
                  </div>

                  <div className="tw-mt-[5px] xl:tw-text-lg lg:tw-text-base tw-text-sm tw-font-medium tw-text-primary_black">{tech.name}</div>
                </div>
              </Col>
            ))}
          </Slider>

        </Row>
      </Container>
    </section>
  );
};
