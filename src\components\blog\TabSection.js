"use client";
import { SearchIcon } from "@/utils/icons";
import React, { useState } from "react";
import { Container } from "reactstrap";
import { usePathname } from "next/navigation";
import Link from "next/link";

import noDataFound from "/public/blogpage/noDataFound.png";

import FillButton from "../buttons/FillButton";
import Image from "next/image";

export const TabSection = ({ className, greenText, blackText, subTitle, sections, categories, pagePathname, CardComponent, isInput, ExploreComponent }) => {
  const pathname = usePathname();
  const [searchQuery, setSearchQuery] = useState("");


  const getActiveCategory = () => {
    if (pathname === pagePathname) {
      return "view all";
    } else {
      return pathname.split('/').pop().toLowerCase();
    }
  };

  const handleSearch = () => {
    console.log("Searching for:", searchQuery);
  };

  const getCategoriesDataList = () => {

    const matchedSection = sections.find(
      section => section.title.toLowerCase() === getActiveCategory()
    );

    return matchedSection ? matchedSection.dataList : [];
  };

  return (
    <section className={`tw-relative ${className} `}>
      <Container
        className={`tw-flex tw-flex-col tw-items-center tw-gap-[40px] lg:tw-gap-[70px] `}
      >

        <div className="tw-absolute tw-w-full tw-top-0 tw-h-[500px] tw-bg-blog-gradient tw-z-0" />

        <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5 tw-gap-2 tw-z-10">
          <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            <span>{blackText} </span>
            <span className="tw-text-primary_green">{greenText}</span>
          </div>
          <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-leading-[120%]">
            {subTitle}
          </div>
        </div>

        <div className="tw-flex tw-flex-col tw-w-full tw-items-center tw-gap-5 tw-mx-auto tw-z-10">
          {/* Search bar */}
          {isInput ? (
            <div className="tw-flex tw-w-full sm:tw-w-[80%] md:tw-w-[60%] xl:tw-w-[55%] tw-h-[50px] xl:tw-h-[60px] tw-items-center tw-justify-between tw-rounded-[40px] tw-pl-3 lg:tw-pl-5 tw-pr-[5px] tw-py-[5px] tw-border tw-border-[#8093A5] md:tw-border-[#dfe4e8] tw-bg-white">
              <div className="tw-flex tw-items-center tw-gap-2.5 tw-flex-1">
                <SearchIcon className=" tw-aspect-square tw-w-6" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="tw-border-0 tw-outline-none tw-h-full tw-p-0 tw-w-full tw-font-normal tw-text-[14px] tw-text-primary_gray lg:tw-text-[16px] placeholder:tw-text-primary_gray"
                  placeholder="What are you looking for?"
                />
              </div>

              <FillButton
                title="Search"
                onClick={handleSearch}
                className="tw-h-full tw-rounded-[50px] tw-px-6 md:tw-px-8 xl:tw-px-12 tw-text-white tw-font-medium"
              />
            </div>) : null}

          <div className="tw-flex tw-gap-2 lg:tw-gap-4 tw-h-full tw-w-full tw-items-center tw-flex-wrap tw-justify-center">
            {categories?.map((category, index) => (
              <Link
                key={category.id}
                href={category.label === 'View All' ? pagePathname : `${pagePathname}/${category?.label.toLowerCase().replace(/\s+/g, '-')}`}
                className={`tw-h-[40px] xl:tw-h-[50px] tw-rounded-[42px] tw-px-6 xl:tw-px-[30px] tw-font-medium tw-text-[14px] xl:tw-text-[16px] tw-border tw-cursor-pointer tw-transition-all tw-duration-200 tw-whitespace-nowrap tw-flex tw-items-center tw-justify-center tw-no-underline ${getActiveCategory() === category.label.toLowerCase() ? "tw-bg-primary_green hover:tw-bg-[#2da023] tw-text-white tw-border-primary_green"
                  : "tw-border-[#dfe4e8] tw-text-primary_black tw-bg-white hover:tw-bg-gray-50"
                  }`}
              >
                {category.label}
              </Link>
            ))}
          </div>
        </div>


        {getActiveCategory() === "view all" ? (

          <div className="tw-w-full tw-flex tw-flex-col tw-items-center tw-gap-[70px] lg:tw-gap-[100px]">
            {sections?.map((section, index) => {
              const dataListLength = section?.dataList?.length || 0;

              return (
                <React.Fragment key={section.id}>
                  {Math.floor(sections?.length / 2) === index ? <ExploreComponent /> : null}
                  {dataListLength !== 0 ? (
                    <div
                      className="tw-flex tw-flex-col tw-items-center tw-gap-[20px] lg:tw-gap-[40px] tw-w-full"
                    >
                      <h2 className="tw-font-bricolageGrotesque tw-text-primary_black tw-font-bold tw-text-[26px] md:tw-text-[30px] xl:tw-text-[36px] tw-mb-0">
                        {section.title}
                      </h2>

                      <div
                        className={`tw-grid ${dataListLength <= 2
                          ? "tw-grid-cols-1 md:tw-grid-cols-2"
                          : "tw-grid-cols-1 lg:tw-grid-cols-[2fr_3fr] xl:tw-grid-cols-2"
                          } tw-gap-x-[30px] xl:tw-gap-x-[50px] tw-gap-5 lg:gap-y-4 xl:tw-gap-y-[25px] tw-w-full`}
                      >
                        {/* Use for ResourcesPage if changes comes md:tw-grid-cols-2  */}
                        {section?.dataList?.map((data, i) => (
                          <div
                            key={i}
                            className={` ${i === 0
                              ? dataListLength <= 2
                                ? " "
                                : dataListLength === 3 ? "tw-col-span-1 tw-row-span-2" : "tw-col-span-1 tw-row-span-3"
                              : dataListLength <= 2
                                ? " "
                                : "tw-col-span-1 tw-row-span-1"}`}
                          >
                            {i === 0 || dataListLength <= 2 ? (
                              <CardComponent {...data} />
                            ) : (
                              <CardComponent {...data} isSmall={true} totalCards={dataListLength} />
                            )}
                          </div>
                        ))}
                      </div>
                      {section?.isButton ? (
                        <FillButton
                          title={section?.buttonText || "View All Articles"}
                          className={
                            "tw-text-[16px] tw-rounded-[12px] xl:tw-py-2.5 xl:tw-px-5 tw-py-2.5 tw-px-[30px]"
                          }
                        />
                      ) : null}
                    </div>) : null}
                </React.Fragment>
              );
            })}
          </div>

        ) : getCategoriesDataList()?.length === 0 ? (
          <div className="tw-bg-white tw-flex tw-flex-col tw-items-center tw-gap-5 tw-text-center ">
            <Image
              src={noDataFound}
              alt="No Data Found"
              width={300}
              height={300}
            />
            <h2 className="tw-font-inter tw-font-semibold tw-text-[16px] md:tw-text-[18px] lg:tw-text-[20px] tw-text-primary_black tw-leading-[1.2]">Our Team is Preparing for You!</h2>
          </div>
        ) : (
          <React.Fragment>
            <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-5 lg:tw-gap-6 xl:tw-gap-[30px]">
              {getCategoriesDataList()?.map((data, i) => (
                <React.Fragment key={i}>
                  <div key={data.id}>
                    <CardComponent {...data} />
                  </div>
                  {i === 5 ? (
                    <div className="tw-col-span-1 sm:tw-col-span-2 lg:tw-col-span-3 tw-my-[70px] lg:tw-my-[100px]">
                      <ExploreComponent />
                    </div>
                  ) : null}
                </React.Fragment>
              ))}
            </div>
            {getCategoriesDataList()?.length < 6 ? <ExploreComponent /> : null}
          </React.Fragment>
        )}
      </Container>
    </section>
  );
};
