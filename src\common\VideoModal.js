"use client";
import React, { useEffect, useRef } from "react";

const VideoModal = ({ isOpen, onClose, videoSrc }) => {
    const videoRef = useRef(null);
    useEffect(() => {
        const video = videoRef.current;

        if (isOpen) {
            document.documentElement.style.overflow = "hidden";
            document.body.scroll = "no";
            if (video) {
                video.currentTime = 0;
                // video.muted = true; // Optional for autoplay
                video
                    .play()
                    .catch((error) => {
                        // play() was interrupted
                        console.warn("Video play was interrupted:", error);
                    });
            }
        } else {
            document.documentElement.style.overflow = "auto";
            document.body.scroll = "yes";
            if (video) {
                video.pause();
            }
        }

        return () => {
            if (video) {
                video.pause();
            }
            document.documentElement.style.overflow = "auto";
            document.body.scroll = "yes";
        };
    }, [isOpen]);
    // useEffect(() => {
    //     const video = videoRef.current;

    //     if (isOpen) {
    //         document.documentElement.style.overflow = "hidden";
    //         document.body.scroll = "no";
    //         if (video) {
    //             video.currentTime = 0; // Reset to start
    //             // video.muted = true; // Enable autoplay on some browsers
    //             video.play();
    //         }
    //     } else {
    //         document.documentElement.style.overflow = "auto";
    //         document.body.scroll = "yes";
    //         if (video) {
    //             video.pause();
    //         }
    //     }

    //     return () => {
    //         if (video) video.pause();
    //         document.documentElement.style.overflow = "auto";
    //         document.body.scroll = "yes";
    //     };
    // }, [isOpen]);

    return (
        <div
            className={`modal modal-xl ${isOpen ? "show d-block" : "d-none"}`}
            tabIndex="-1"
            style={{ background: "rgba(1, 1, 1, 0.5)" }} // Adds overlay effect
        >
            <div className="modal-dialog modal-dialog-centered">
                <div className="modal-content">
                    <div className="modal-header border-0">
                        <button
                            type="button"
                            className="btn-close"
                            onClick={onClose} // Close modal when button clicked
                            aria-label="Close"
                        ></button>
                    </div>
                    <div className="modal-body">
                        <div className="tw-mb-[48px]">
                            <div className="tw-w-full d-flex justify-content-center">
                                <div className="tw-aspect-video lg:tw-min-h-[520px]" style={{ maxWidth: "1000px", borderRadius: "10px", overflow: "hidden" }}>
                                    <video ref={videoRef} controls className="tw-w-full tw-h-full" style={{ outline: "none" }}>
                                        <source src={videoSrc} type="video/mp4" />
                                        Your browser does not support the video tag.
                                    </video>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default VideoModal;
