import Image from "next/image";
import React from "react";
import { Col, Container, Row } from "reactstrap";

import img1 from "/public/aboutPage/agileMindset/img1.png";
import img2 from "/public/aboutPage/agileMindset/img2.png";
import img3 from "/public/aboutPage/agileMindset/img3.png";
import img4 from "/public/aboutPage/agileMindset/img4.png";
import img5 from "/public/aboutPage/agileMindset/img5.png";
import img6 from "/public/aboutPage/agileMindset/img6.png";

export const AgileSection = () => {
  const features = [
    {
      title: "Mind open for ideas",
      description:
        "Innovation can only happen by accepting ideas that seem impossible. At TST Technology, we encourage innovative thinking and embrace ideas with our design and development team brainstorming ways of idea execution. This approach lets us test and try, to find out what works and what doesn't.",
      image: img1,
    },
    {
      title: "Being highly adaptive",
      description:
        "If you don't change with times and requirements, you fall behind. Market and user needs can change in the blink of an eye. If you stay rigid, you become outdated, which is why you get adaptive design and development at TST Technology.",
      image: img2,
    },
    {
      title: "Continuously improving",
      description:
        "If something works, don't touch it- a statement that most developers live by! But if something can be improved, optimized, or enhanced, you must do it. What's true for the market is that if you don't stay at the top of your game, someone else will come in and steal your thunder. So you keep a live feedback and improvement cycle for growth.",
      image: img3,
    },
    {
      title: "Maintaining consistency",
      description:
        "Just because you're quickly adapting and improving doesn't mean you can work erratically. With Agile development, the processes are very simple so no matter where we are in the product journey, it is very easy to integrate them in the plan without breaking the flow or losing consistency.",
      image: img4,
    },
    {
      title: "Collaborating seamlessly",
      description:
        "Our Agile Mindset allows us to do frictionless cross-team collaborations for overall successful product development and service delivery. Departments of RnD, Design, Frontend Development, Backend Development, Testing, DevOps and more come together and play their roles efficiently and on time to create one great product- yours!",
      image: img5,
    },
    {
      title: "Enter the digital age with Agile Mindset!",
      description:
        "We cultivate, integrate and practice Agile work of way at TST Technology every single day to provide high-quality service with rich experience to all our customers across 15+ industries and 7+ countries.",
      image: img6,
    },
  ];

  return (
    <Container
      tag="section"
      className=" tw-flex tw-flex-col tw-gap-[40px] md:tw-gap-[40px] lg:tw-gap-[70px]"
    >
      {/* Title */}
      <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
        <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
          <span>Agile </span>
          <span className="tw-text-primary_green"> Mindset</span>
        </div>
        <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-leading-[140%]">
          Agile isn’t just how we build software—it’s how we collaborate, learn,
          and improve. It keeps us flexible, focused, and aligned with your
          goals—at every step of the journey.
        </div>
      </div>

      <div className="tw-relative tw-flex tw-flex-col tw-gap-12 md:tw-gap-[70px] xl:tw-gap-[100px] xl:tw-bg-agile-lineBg-img lg:tw-bg-agile-lineBg-1024-img md:tw-bg-agile-lineBg-768-img tw-bg-agile-lineBg-mobile-img tw-bg-contain md:tw-bg-cover md:tw-bg-center tw-bg-no-repeat">
        {/* <div className=" tw-flex tw-flex-col tw-gap-12 md:tw-gap-[70px] xl:tw-gap-[100px]"> */}
        {features.map((feature, index) => {
          const isEven = index % 2 === 0;

          return (
            <Row key={index} className="gy-4 g-md-0 tw-items-center tw-z-0">
              {/* <div
                className={`tw-absolute tw-text-left tw-mx-auto tw-border-dotted tw-border-b-2 tw-z-10 tw-h-[400px] ${
                  isEven
                    ? "!tw-w-[52%] tw-rounded-s-[30px] tw-border-l-2 tw-left-2 -tw-bottom-[50px]"
                    : "!tw-w-[52%] tw-rounded-e-[30px] tw-border-r-2 tw-right-[45%] -tw-top-[50px]"
                }`}
              /> */}

              <Col
                md="6"
                // lg={isEven ? "7" : "5"}
                className={`tw-flex tw-flex-col tw-justify-center ${
                  isEven ? "tw-order-1" : "md:tw-order-2 tw-order-1"
                }`}
              >
                <div
                  className={`tw-flex tw-items-start lg:tw-gap-6 tw-gap-3 ${
                    isEven
                      ? "md:tw-pr-5 lg:tw-pr-[35px] xl:tw-pr-[50px]"
                      : "md:tw-pl-5 lg:tw-pl-[35px] xl:tw-pl-[50px]"
                  }`}
                >
                  <h1 className="tw-bg-white tw-text-primary_green tw-leading-[1.2] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] tw-mb-0 tw-z-20">
                    #{index + 1}
                  </h1>
                  <div className="tw-flex tw-flex-col tw-items-start tw-gap-2.5">
                    <div className="tw-shrink-0  tw-text-primary_black tw-leading-[1.2] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px]">
                      {feature.title}
                    </div>
                    <div className="tw-text-[14px] tw-text-primary_gray tw-font-inter tw-leading-[1.4]">
                      {feature.description}
                    </div>
                  </div>
                </div>
              </Col>

              <Col
                md="6"
                // lg={isEven ? "5" : "7"}
                className={` ${
                  isEven ? "tw-order-2" : "md:tw-order-1 tw-order-2"
                }`}
              >
                <div
                  className={`tw-bg-white tw-flex tw-justify-center tw-items-center tw-ml-10 md:tw-ml-0 md:tw-my-[5px] lg:tw-my-0  ${
                    isEven
                      ? "md:tw-pl-5 lg:tw-pl-[35px] xl:tw-pl-[50px]"
                      : "md:tw-pr-5 lg:tw-pr-[35px] xl:tw-pr-[50px]"
                  }`}
                >
                  <div className="tw-w-full tw-h-[250px] md:tw-h-[240px] lg:tw-h-[248px] xl:tw-h-[300px] tw-relative">
                    <Image
                      src={feature.image}
                      alt={feature.title}
                      fill
                      className="tw-object-contain"
                    />
                  </div>
                </div>
              </Col>
            </Row>
          );
        })}
      </div>
    </Container>
  );
};
