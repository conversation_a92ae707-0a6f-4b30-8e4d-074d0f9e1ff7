"use client";
import CountUp from "@/common/CountUp";
import Image from "next/image";

import React from "react";

export const AboutStatCard = ({ data }) => {
  return (
    <div
      className={`tw-h-full tw-p-[15px] md:tw-p-[24px] lg:tw-mx-2 xl:tw-mx-0 xl:tw-p-[30px] tw-rounded-b-full tw-flex tw-flex-col tw-items-center tw-justify-between tw-gap-7 lg:tw-gap-11 xl:tw-gap-[60px] ${data.gradientClass}`}
    >
      <div className="tw-flex tw-flex-col tw-items-center tw-gap-2 lg:tw-gap-3 tw-mt-2.5 md:tw-mt-6 lg:tw-mt-8 xl:tw-mt-10">
        <h1 className="tw-font-inter tw-font-bold tw-leading-[1.3] tw-tracking-[0%] tw-text-center tw-text-[36px] md:tw-text-[40px] lg:tw-text-[48px] xl:tw-text-[70px] 2xl:tw-text-[76px] tw-mb-0">
          <CountUp start={0} end={data?.value || 0} duration={30} />
          {data?.sign}
        </h1>
        <p className="tw-font-inter tw-font-normal tw-leading-[120%] tw-tracking-[0%] tw-text-center tw-text-[12px] md:tw-text-[16px] xl:tw-text-[20px] 2xl:tw-text-[24px] tw-mb-0">
          {data?.label}
        </p>
      </div>

      <div className="tw-bg-white tw-rounded-full tw-p-[30%] lg:tw-p-[28%] tw-aspect-square tw-w-full">
        <div className="tw-relative tw-aspect-square tw-w-full">
          <Image
            src={data.icon}
            fill
            alt={`${data.label} icon`}
            className="tw-object-contain"
          />
        </div>
      </div>
    </div>
  );
};
