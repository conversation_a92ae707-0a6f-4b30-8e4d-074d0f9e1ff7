"use client";

import React, { useRef, useState } from "react";
import Slider from "react-slick";
import { Col, Container, Row } from "reactstrap";

import FillButton from "@/components/buttons/FillButton";
import { ContactUsCard } from "@/components/card/ContactUsCard";
import { LeftArrowIcon, RightArrowIcon } from "@/utils/icons";
import clientLogo from "/public/aboutPage/contactUs/clientLogo.png";

export const ContactUsSection = ({ className }) => {
  const sliderRef = useRef(null);
  const [count, setCount] = useState(0);

  const clients = [
    {
      logo: clientLogo,
      description:
        "I am thrilled to recommend <PERSON><PERSON> and his company, TST, for their outstanding leadership and expertise in creating SaaS products and providing exceptional tech services.The team deliver cutting-edge SaaS products and the delivery of best-in-class tech services. I have had the privilege of witnessing TST's exceptional work firsthand, and I can confidently say that their professionalism, integrity, and customer-centric approach set them apart in the industry.",
      owner: {
        name: "<PERSON>",
        position: "Chief Executive Officer",
      },
    },
    {
      logo: client<PERSON>ogo,
      description:
        "I am thrilled to recommend <PERSON><PERSON> and his company, TST, for their outstanding leadership and expertise in creating SaaS products and providing exceptional tech services.The team deliver cutting-edge SaaS products and the delivery of best-in-class tech services. I have had the privilege of witnessing TST's exceptional work firsthand, and I can confidently say that their professionalism, integrity, and customer-centric approach set them apart in the industry.",
      owner: {
        name: "<PERSON> Mendell",
        position: "Chief Executive Officer",
      },
    },
    {
      logo: clientLogo,
      description:
        "I am thrilled to recommend Parth and his company, TST, for their outstanding leadership and expertise in creating SaaS products and providing exceptional tech services.The team deliver cutting-edge SaaS products and the delivery of best-in-class tech services. I have had the privilege of witnessing TST's exceptional work firsthand, and I can confidently say that their professionalism, integrity, and customer-centric approach set them apart in the industry.",
      owner: {
        name: "Sophia Mendell",
        position: "Chief Executive Officer",
      },
    },
  ];

  const settings = {
    dots: false,
    pauseOnHover: true,
    arrows: false,
    infinite: false,
    swipeToSlide: true,
    beforeChange: (oldIndex, newIndex) => setCount(newIndex),
  };

  const goToPrev = () => {
    if (count > 0) {
      sliderRef.current?.slickPrev();
    }
  };

  const goToNext = () => {
    if (count < clients.length - 1) {
      sliderRef.current?.slickNext();
    }
  };

  return (
    <section className={`tw-bg-[#F7F9FB] tw-py-5 ${className}`}>
      <Container fluid tag="section">
        <Row className="g-0">
          <Col xs="12" lg="7" xxl="8"
          >
            <div className="tw-flex tw-flex-col lg:tw-items-start tw-items-center tw-gap-[40px] md:tw-gap-[45px] lg:tw-gap-[50px] tw-w-full tw-p-[50px_20px] sm:tw-p-[50px_30px] lg:tw-p-[60px_32px_60px_12px] xl:tw-p-[80px_100px_80px_80px]">
              <Title
                title="Contact Us"
                tagLine="Your vision, our expertise — let's make it happen."
              />

              <form className="tw-w-full tw-items-stretch tw-flex tw-flex-col ">
                <Row className="g-4">
                  <Col xs="12" sm="6">
                    <div>
                      <div className="tw-flex tw-justify-between tw-items-center tw-mb-2.5">
                        <label className="tw-text-primary_black tw-font-inter tw-font-normal tw-text-[14px] tw-leading-[1.2] tw-align-middle">
                          Name <span className="tw-text-[#F96565]">*</span>
                        </label>
                      </div>
                      <input
                        type="text"
                        className="tw-w-full tw-rounded-[12px] tw-border tw-border-[#DFE4E8] tw-bg-white tw-p-[17px_16px] tw-text-[14px] tw-leading-[120%] tw-font-inter tw-font-normal tw-placeholder-[#DFE4E8] tw-placeholder:tw-font-inter tw-placeholder:tw-font-normal tw-placeholder:tw-text-[14px] tw-placeholder:tw-leading-[120%] tw-h-[50px]"
                        placeholder="Enter name"
                        required
                      />
                    </div>
                  </Col>

                  <Col xs="12" sm="6">
                    <div>
                      <div className="tw-flex tw-justify-between tw-items-center tw-mb-2.5">
                        <label className="tw-text-primary_black tw-font-inter tw-font-normal tw-text-[14px] tw-leading-[1.2] tw-align-middle">
                          Email <span className="tw-text-[#F96565]">*</span>
                        </label>
                      </div>
                      <input
                        type="text"
                        className="tw-w-full tw-rounded-[12px] tw-border tw-border-[#DFE4E8] tw-bg-white tw-p-[17px_16px] tw-text-[14px] tw-leading-[120%] tw-font-inter tw-font-normal tw-placeholder-[#DFE4E8] tw-placeholder:tw-font-inter tw-placeholder:tw-font-normal tw-placeholder:tw-text-[14px] tw-placeholder:tw-leading-[120%] tw-h-[50px]"
                        placeholder="Enter email"
                        required
                      />
                    </div>
                  </Col>

                  <Col xs="12" sm="6">
                    <div>
                      <div className="tw-flex tw-justify-between tw-items-center tw-mb-2.5">
                        <label className="tw-text-primary_black tw-font-inter tw-font-normal tw-text-[14px] tw-leading-[1.2] tw-align-middle">
                          Phone Number
                        </label>
                      </div>
                      <input
                        type="email"
                        className="tw-w-full tw-rounded-[12px] tw-border tw-border-[#DFE4E8] tw-bg-white tw-p-[17px_16px] tw-text-[14px] tw-leading-[120%] tw-font-inter tw-font-normal tw-placeholder-[#DFE4E8] tw-placeholder:tw-font-inter tw-placeholder:tw-font-normal tw-placeholder:tw-text-[14px] tw-placeholder:tw-leading-[120%] tw-h-[50px]"
                        placeholder="Enter phone number"
                      />
                    </div>
                  </Col>

                  <Col xs="12" sm="6">
                    <div>
                      <div className="tw-flex tw-justify-between tw-items-center tw-mb-2.5">
                        <label className="tw-text-primary_black tw-font-inter tw-font-normal tw-text-[14px] tw-leading-[1.2] tw-align-middle">
                          Country
                        </label>
                      </div>
                      <input
                        type="text"
                        className="tw-w-full tw-rounded-[12px] tw-border tw-border-[#DFE4E8] tw-bg-white tw-p-[17px_16px] tw-text-[14px] tw-leading-[120%] tw-font-inter tw-font-normal tw-placeholder-[#DFE4E8] tw-placeholder:tw-font-inter tw-placeholder:tw-font-normal tw-placeholder:tw-text-[14px] tw-placeholder:tw-leading-[120%] tw-h-[50px]"
                        placeholder="Enter country"
                      />
                    </div>
                  </Col>

                  <Col xs="12">
                    <div>
                      <div className="tw-flex tw-justify-between tw-items-center tw-mb-2.5">
                        <label className="tw-font-inter tw-font-normal tw-text-[14px] tw-leading-[120%] tw-align-middle">
                          Message
                        </label>
                      </div>
                      <textarea
                        rows="4"
                        className="tw-appearance-none tw-w-full tw-rounded-[12px] tw-border tw-border-[#DFE4E8] tw-bg-white tw-p-[17px_16px] tw-text-[14px] tw-leading-[120%] tw-font-inter tw-font-normal tw-placeholder-[#DFE4E8] tw-placeholder:tw-font-inter tw-placeholder:tw-font-normal tw-placeholder:tw-text-[14px] tw-placeholder:tw-leading-[120%] tw-h-full"
                        placeholder="Enter message"
                      ></textarea>
                    </div>
                  </Col>
                </Row>
              </form>
              <FillButton
                title={"Submit"}
                className={
                  "tw-w-fit tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-10 lg:tw-py-[8px] lg:tw-px-4 tw-px-10 tw-py-[13px]"
                }
              />
            </div>
          </Col>
          <Col xs="12" lg="5" xxl="4">
            {/* <div className="tw-bg-white tw-flex tw-flex-col tw-items-start tw-gap-[40px] md:tw-gap-[45px] lg:tw-gap-[50px] tw-px-5 lg:tw-px-[50px]  tw-w-full"> */}
            <div className="tw-bg-white tw-flex tw-flex-col tw-items-center lg:tw-items-start tw-gap-[40px] md:tw-gap-[45px] lg:tw-gap-[50px] tw-p-[30px_20px] sm:tw-p-[50px_30px] lg:tw-p-[60px_30px] xl:tw-p-[100px_50px] tw-w-full">
              <Title
                title="Real Stories. Real Results."
                tagLine="We build partnerships that deliver impact. Here's what our clients say about working with TST Technology."
              />
              <Slider {...settings} ref={sliderRef} className="tw-w-full">
                {clients?.map((client, idx) => (
                  <div key={idx} className="tw-w-full">
                    <ContactUsCard
                      Logo={client.logo}
                      description={client.description}
                      owner={client.owner}
                    />
                  </div>
                ))}
              </Slider>
              <div className="tw-flex tw-justify-center lg:tw-items-start tw-items-center tw-gap-5">
                <button
                  className={`tw-rounded-full tw-p-[10px] ${
                    count === 0 ? "tw-bg-[#e0fbd7]" : "tw-bg-primary_green"
                  }`}
                  onClick={goToPrev}
                  disabled={count === 0}
                  aria-label="Previous"
                >
                  <LeftArrowIcon
                    className={`${
                      count === 0 ? "tw-fill-primary_green" : "tw-fill-white"
                    }`}
                  />
                </button>
                <button
                  className={`tw-rounded-full tw-p-[10px] ${
                    count === clients.length - 1
                      ? "tw-bg-[#e0fbd7]"
                      : "tw-bg-primary_green"
                  }`}
                  onClick={goToNext}
                  disabled={count === clients.length - 1}
                  aria-label="Next"
                >
                  <RightArrowIcon
                    className={`tw-w-6 tw-h-6 ${
                      count === clients.length - 1
                        ? "tw-fill-primary_green"
                        : "tw-fill-white"
                    }`}
                  />
                </button>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

const Title = ({ title, tagLine }) => {
  return (
    <div className="tw-w-full tw-text-center md:tw-text-left tw-flex tw-flex-col tw-items-center lg:tw-items-start lg:tw-gap-2.5 tw-gap-2">
      <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
        {title}
      </div>
      <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-leading-[140%]">
        {tagLine}
      </div>
    </div>
  );
};
