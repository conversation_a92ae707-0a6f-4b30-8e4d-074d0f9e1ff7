import React from "react";
import Image from "next/image";

import lifeStyleIcon from "/public/aboutPage/industries/lifeStlyeIcon.png";
import healthcareIcon from "/public/aboutPage/industries/healthcareIcon.png";
import sportIcon from "/public/aboutPage/industries/sportIcon.png";
import logisticsIcon from "/public/aboutPage/industries/logisticsIcon.png";
import entertainmentIcon from "/public/aboutPage/industries/entertainmentIcon.png";
import socialIcon from "/public/aboutPage/industries/socialIcon.png";
import newsmediaIcon from "/public/aboutPage/industries/newsmediaIcon.png";
import gameIcon from "/public/aboutPage/industries/gameIcon.png";
import shoppingIcon from "/public/aboutPage/industries/shoppingIcon.png";
import fintechIcon from "/public/aboutPage/industries/fintechIcon.png";
import educationIcon from "/public/aboutPage/industries/educationIcon.png";
import consultantIcon from "/public/aboutPage/industries/consultantIcon.png";
import miningIcon from "/public/aboutPage/industries/miningIcon.png";
import solarIcon from "/public/aboutPage/industries/solarIcon.png";
import { Col, Container, Row } from "reactstrap";

export const IndustriesSection = ({ className }) => {
  const industries = [
    { name: "Life Style", icon: lifeStyleIcon },
    { name: "Social Networking", icon: socialIcon },
    { name: "Education", icon: educationIcon },
    { name: "Healtcare", icon: healthcareIcon },
    { name: "News & Media", icon: newsmediaIcon },
    { name: "Consultancy", icon: consultantIcon },
    { name: "Sport tech", icon: sportIcon },
    { name: "Gamification", icon: gameIcon },
    { name: "Mining", icon: miningIcon },
    { name: "Logistics", icon: logisticsIcon },
    { name: "Shopping", icon: shoppingIcon },
    { name: "Solar Energy", icon: solarIcon },
    { name: "Entertainment", icon: entertainmentIcon },
    { name: "Fintech", icon: fintechIcon },
  ];
  return (
    <section className={`${className}`}>
      <Container className="tw-flex tw-flex-col tw-gap-[40px] lg:tw-gap-[70px]">
        {/* Title */}
        <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
          <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
            Industries
            <span className="tw-text-primary_green"> We served</span>
          </div>
        </div>

        {/* Industries grid */}
        <Row className="g-2 gx-md-4 gx-lg-5">
          {industries.map((value, index) => (
            <Col key={index} xs="6" md="4">
              <div className="tw-flex tw-items-center tw-gap-2.5 lg:tw-gap-[15px] tw-py-5 lg:tw-py-[30px] tw-h-full tw-border-b tw-border-[#DFE4E8]">
                <div className="tw-relative md:tw-ml-[10%] lg:tw-ml-[15%] xl:tw-ml-[20%] tw-aspect-[1] tw-w-[18px] md:tw-w-6">
                  <Image
                    src={value.icon}
                    fill
                    className="tw-object-contain"
                    alt={`${value.name} icon`}
                  />
                </div>
                <h3 className="tw-font-inter tw-font-medium tw-text-[16px] lg:tw-text-[20px] tw-leading-[1.2] tw-text-primary_black tw-mb-0">
                  {value.name}
                </h3>
              </div>
            </Col>
          ))}
        </Row>
      </Container>
    </section>
  );
};
