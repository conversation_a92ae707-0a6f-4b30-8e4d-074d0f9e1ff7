"use client"
import React from "react";
import { HappeningSection } from "./HappeningSection";
import HeroSec<PERSON> from "./HeroSection";
import { usePathname } from "next/navigation";

export const HappeningPage = () => {
  const pathname = usePathname();
  return (
    <>
      <HeroSection className="md:tw-py-[100px] tw-py-[70px]" key={pathname} />
      <HappeningSection className="md:tw-py-[100px] tw-py-[70px]" />
    </>
  );
};
