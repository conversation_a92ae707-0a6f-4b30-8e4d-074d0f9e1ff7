'use client';
import Image from 'next/image';
import Slider from 'react-slick';
// import { FaStar, FaPlay } from 'react-icons/fa';
import { LeftArrowIcon, PlayIcon, RightArrowIcon, StarIcon, StartIcon } from '@/utils/icons';
import thumb1 from '/public/aboutPage/ourTeam/thumb1.jpg';
import thumb2 from '/public/aboutPage/ourTeam/thumb2.jpg';
import thumb3 from '/public/aboutPage/ourTeam/thumb3.jpg';
import { useEffect, useRef, useState } from 'react';
import VideoModal from '@/common/VideoModal';

const user1Img = "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/575fa746ac097c0d2f00a113e71baa0e130b0b4b?placeholderIfAbsent=true";

const user2Img = "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/575fa746ac097c0d2f00a113e71baa0e130b0b4b?placeholderIfAbsent=true";

const testimonials = [
    {
        id: 1,
        name: '1 Harry Maguire',
        title: 'CEO, company',
        rating: 5,
        image: user1Img, // Profile photo
        thumbnail: thumb1, // Testimonial video thumbnail
        heading: 'Top-Notch Quality',
        url: "https://cdn.tsttechnology.in/TST_Testimonial_c2c47b99c0.mp4",
        feedback:
            'UI Wiki transformed our design process! The templates are modern, user-friendly, and saved us countless hours.',
    },
    {
        id: 2,
        name: '2 Kristie Jenson',
        title: 'CMO, company',
        rating: 5,
        image: user2Img,
        thumbnail: thumb2,
        heading: "Freelancer’s Ally",
        url: "https://cdn.tsttechnology.in/TST_Testimonial_c2c47b99c0.mp4",
        feedback:
            'As a freelancer, UI Wiki is like having a full design team. It’s the best investment I’ve made for my workflow.',
    },
    {
        id: 212,
        name: '3 Kristie Jenson',
        title: 'CMO, company',
        rating: 5,
        image: user1Img,
        thumbnail: thumb3,
        heading: "Freelancer’s Ally",
        url: "https://cdn.tsttechnology.in/TST_Testimonial_c2c47b99c0.mp4",
        feedback:
            'As a freelancer, UI Wiki is like having a full design team. It’s the best investment I’ve made for my workflow.',
    },
    {
        id: 112,
        name: '4 Kristie Jenson',
        title: 'CMO, company',
        rating: 5,
        image: user2Img,
        thumbnail: thumb2,
        heading: "Freelancer’s Ally",
        url: "https://cdn.tsttechnology.in/TST_Testimonial_c2c47b99c0.mp4",
        feedback:
            'As a freelancer, UI Wiki is like having a full design team. It’s the best investment I’ve made for my workflow.',
    },
    {
        id: 12,
        name: '5 Kristie Jenson',
        title: 'CMO, company',
        rating: 5,
        image: user1Img,
        thumbnail: thumb3,
        heading: "Freelancer’s Ally",
        url: "https://cdn.tsttechnology.in/TST_Testimonial_c2c47b99c0.mp4",
        feedback:
            'As a freelancer, UI Wiki is like having a full design team. It’s the best investment I’ve made for my workflow.',
    },
    {
        id: 132,
        name: '6 Kristie Jenson',
        title: 'CMO, company',
        rating: 5,
        image: user2Img,
        thumbnail: thumb1,
        heading: "Freelancer’s Ally",
        url: "https://cdn.tsttechnology.in/TST_Testimonial_c2c47b99c0.mp4",
        feedback:
            'As a freelancer, UI Wiki is like having a full design team. It’s the best investment I’ve made for my workflow.',
    },
];



export default function OurTeam({ className }) {
    const sliderRef = useRef(null);
    const [count, setCount] = useState(0);
    const [slidesToShow, setSlidesToShow] = useState(1);
    const [isPlayVideo, setIsPlayVideo] = useState(false);
    const [videoUrl, setVideoUrl] = useState("");
    // const [mounted, setMounted] = useState(false); // 👈 this is the fix

    // useEffect(() => {
    //     setMounted(true); // 👈 ensures component only renders after window is ready
    // }, []);

    const settings = {
        dots: false,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        className: "",
        centerMode: true,
        // centerPadding: '180px',
        pauseOnHover: true,
        // autoplay: true,
        arrows: false,
        responsive: [
            {
                breakpoint: 1536,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    centerPadding: '0px',
                },
            },
            {
                breakpoint: 1440,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    centerPadding: '380px',
                },
            },
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    centerPadding: '280px',
                },
            },
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    centerPadding: '180px',
                },
            },
            {
                breakpoint: 480,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    centerPadding: '0px',
                },
            },
        ],
        beforeChange: (oldIndex, newIndex) => setCount(newIndex),
        afterChange: (current) => {
            // get current slidesToShow from slider instance
            const slick = sliderRef.current;
            if (slick) {
                const currentSlidesToShow = slick.innerSlider.props.slidesToShow;
                setSlidesToShow(currentSlidesToShow);
            }
        },
        onInit: () => {
            const slick = sliderRef.current;
            if (slick) {
                const currentSlidesToShow = slick.innerSlider.props.slidesToShow;
                setSlidesToShow(currentSlidesToShow);
            }
        },
    };

    // if (!mounted) return null;

    const goToPrev = () => {
        if (count > 0) {
            sliderRef.current?.slickPrev();
        }
    };

    const goToNext = () => {
        if (count < testimonials.length - 1) {
            sliderRef.current?.slickNext();
        }
    };






    return (
        <div className={`tw-py-16 tw-text-white ${className} tw-bg-primary_color`}>
            <div className="tw-text-center tw-mb-10">
                <h2 className="tw-text-3xl tw-font-bold">
                    Team <span className="tw-text-secondary_color">Testimonials</span>
                </h2>
                <p className="tw-text-primary_gray tw-mt-2">
                    Listen to the voices of our team sharing their testimonials and feedback
                </p>
            </div>

            <Slider {...settings} ref={sliderRef}>
                {testimonials.map((testimonial) => (
                    <div key={testimonial.id} className="tw-px-4">
                        <div className="tw-space-y-4 tw-bg-transparent tw-border tw-border-white/30 tw-rounded-2xl tw-shadow-lg tw-p-8 ">
                            {/* Top: Profile */}
                            <div className="tw-flex tw-items-center tw-justify-between">
                                <div className="tw-flex tw-items-center tw-gap-4">
                                    <div className="tw-relative tw-w-14 tw-h-14 tw-rounded-full tw-overflow-hidden">
                                        <Image
                                            src={testimonial.image}
                                            alt={testimonial.name}
                                            fill
                                            className="tw-object-cover"
                                        />
                                    </div>
                                    <div>
                                        <h3 className="tw-font-semibold tw-text-white tw-text-lg">
                                            {testimonial.name}
                                        </h3>
                                        <p className="tw-text-sm tw-text-primary_gray tw-m-0 tw-relative -tw-top-2">
                                            {testimonial.title}
                                        </p>
                                    </div>
                                </div>
                                <div className="tw-flex tw-items-center tw-gap-3 tw-text-green-400 tw-font-semibold">
                                    {/* <FaStar /> */}
                                    <StarIcon fill='#1FC16B' />
                                    {testimonial.rating}/5
                                </div>
                            </div>

                            {/* Middle: Thumbnail with Play */}
                            <div className="tw-relative tw-rounded-lg tw-overflow-hidden tw-mx-auto tw-w-full tw-h-48">
                                <Image
                                    src={testimonial.thumbnail}
                                    alt="testimonial"
                                    fill
                                    className="tw-object-cover"
                                />
                                <button onClick={() => {
                                    setIsPlayVideo(true);
                                    setVideoUrl(testimonial?.url);
                                }} className="tw-absolute tw-rounded-full tw-inset-0 tw-flex tw-items-center tw-justify-center">
                                    <div className=" tw-backdrop-blur-[6px] tw-w-[4.5rem] tw-h-[4.5rem] tw-rounded-full tw-flex tw-items-center tw-justify-center">
                                        <PlayIcon mainFill='#7D5FBE' fill='#00000057' />
                                    </div>
                                </button>
                            </div>

                            {/* Bottom: Feedback */}
                            <div>
                                <p className="tw-font-semibold tw-mb-0 tw-text-lg">{testimonial.heading}</p>
                                <p className="tw-text-sm tw-text-center tw-mt-2 tw-mb-0  tw-text-primary_gray">
                                    {testimonial.feedback}
                                </p>
                            </div>
                        </div>
                    </div>
                ))}
            </Slider>
            <div className="tw-flex tw-justify-center tw-items-center tw-gap-5 tw-mt-5">
                <button
                    className={`tw-rounded-full tw-p-[10px] tw-border ${count === 0 ? 'tw-bg-transparent tw-border-white/70' : 'tw-bg-secondary_color tw-border-transparent'}`}
                    onClick={goToPrev}
                    disabled={count === 0}
                    aria-label="Previous"
                >
                    <LeftArrowIcon className={`${count === 0 ? 'tw-fill-secondary_color' : 'tw-fill-white'}`} />
                </button>
                <button
                    className={`tw-rounded-full tw-p-[10px] tw-border ${count >= testimonials.length - slidesToShow ? 'tw-bg-transparent  tw-border-white/70' : 'tw-bg-secondary_color  tw-border-transparent'}`}
                    onClick={goToNext}
                    // disabled={count === testimonials.length - 1}
                    disabled={count >= testimonials.length - slidesToShow}
                    aria-label='Next'
                >
                    <RightArrowIcon className={`tw-w-6 tw-h-6  ${count >= testimonials.length - slidesToShow ? 'tw-fill-secondary_color' : 'tw-fill-white '}`} />
                </button>
            </div>

            {isPlayVideo && (
                <VideoModal
                    isOpen={isPlayVideo}
                    onClose={() => {
                        setIsPlayVideo(false);
                        setVideoUrl("");
                    }}
                    videoSrc={videoUrl}
                />
            )}
        </div>
    );
}
