import React from "react";
import Image from "next/image";
import { Container, Row, Col } from "reactstrap";
import { ResourceCard } from "../card/ResourceCard";
import ResourcesSlider from "@/common/Slider/ResourcesSlider";
import image1 from "../../../public/resources/image1.png";
import image2 from "../../../public/resources/image2.png";
import image3 from "../../../public/resources/image3.png";
import ExtrafeaturesIcon from "../../../public/serviceLogo/BestFeatureArrow.svg";
import { RightArrowIcon } from "@/utils/icons";
import TextButton from "../buttons/TextButton";

export const ResourcesSection = ({ className }) => {
  const resources = [
    {
      image: image1,
      category: "DEVELOPMENT",
      fileType: ".pdf",
      fileTypeColor: "#A32938",
      fileTypeBorderColor: "#F5D6DA",
      title: "Pre-built Templates vs Custom UI/UX Designs",
    },
    {
      image: image2,
      category: "DEVELOPMENT",
      fileType: ".ppt",
      fileTypeColor: "#FF7D43",
      fileTypeBorderColor: "#FFCCB6",
      title: "Agile vs Waterfall Methodologies in Software Development",
    },
    {
      image: image3,
      category: "GUIDES",
      fileType: ".docx",
      fileTypeColor: "#2943A3",
      fileTypeBorderColor: "#D6DDF5",
      title: "Key Features to Include in Your App to Reflect Your Brand Values",
    },
  ];

  return (
    <>
      <Container tag="section" className={className}>

        <div
          className="tw-text-primary_black tw-font-bold tw-text-center tw-font-bricolageGrotesque tw-text-[26px] md:tw-text-[36px] 2xl:tw-text-[48px] lg:tw-mb-[50px] tw-mb-[28px] "
        >
          Explore Our
          <span className="tw-text-primary_green"> Resources</span>
        </div>
        <div className="lg:tw-block tw-hidden">
          <Row>
            {resources.map((resource, index) => (
              <Col key={index} lg="4">
                <ResourceCard
                  image={resource.image}
                  category={resource.category}
                  fileType={resource.fileType}
                  fileTypeColor={resource.fileTypeColor}
                  fileTypeBorderColor={resource.fileTypeBorderColor}
                  title={resource.title}
                />
              </Col>
            ))}
          </Row>
          <BottomButton />
        </div>
        <div className="lg:tw-hidden tw-block">
          <ResourcesSlider resources={resources} />
          <BottomButton />
        </div>
      </Container>


    </>
  );
};

const BottomButton = () => {
  return (
    <TextButton blackTitle='Explore' greenTitle='More Resources' className='tw-mt-[67px] lg:tw-mt-[50px] lg:tw-text-[20px]' />
  );
};
