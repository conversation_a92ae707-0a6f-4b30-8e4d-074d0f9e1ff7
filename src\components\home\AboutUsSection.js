"use client"
import { PlayIcon } from "@/utils/icons";
import Image from "next/image";
import React, { useState } from "react";
import { Col, Container, Row } from "reactstrap";
import c4 from "../../../public/client/c4.png"
import CountUp from "@/common/CountUp";
import VideoModal from "@/common/VideoModal";
import FillButton from "../buttons/FillButton";
export const AboutUsSection = ({ className }) => {
  const stats = [
    { value: "15", sign: "+", title: "Skilled Developers" },
    { value: "20", sign: "+", title: "Projects Delivered" },
    { value: "25", sign: "k+", title: "Countries Served" },
  ];
  const [isPlayVideo, setIsPlayVideo] = useState(false);
  return (
    <Container
      className={`tw-flex tw-flex-col tw-gap-[70px] tw-items-center ${className}`}
    >
      <div className="tw-flex tw-flex-col tw-items-center md:tw-gap-2.5 tw-gap-2">
        <h2 className="tw-text-primary_black md:tw-text-4xl tw-text-2xl tw-font-bold tw-text-center tw-font-bricolageGrotesque tw-mb-0">
          <span>Who </span>
          <span className="tw-text-primary_green">We Are</span>
        </h2>
        <p className="lg:tw-text-sm tw-text-[12px] tw-text-primary_gray tw-text-center tw-mb-0">
          Discover the values, vision, and team driving TST Technology forward.
        </p>
      </div>

      <Row className="gx-5">
        <Col lg={7} xl={8} xxl={7} className="lg:tw-order-1 tw-order-2">
          <div className="tw-flex tw-flex-col tw-justify-center xl:tw-gap-10 lg:tw-gap-5 tw-gap-10 tw-items-start tw-w-full tw-h-full">
            <p className="tw-text-primary_black tw-text-[18px] md:tw-text-[22px] xl:tw-text-[26px] tw-mb-0 lg:tw-text-start tw-text-center">
              TST Technology is a forward-thinking IT company specialising in
              innovative, scalable, and secure digital products. We deliver web,
              mobile, and cloud solutions that help businesses automate
              operations and enhance user experiences. Our expert team
              collaborates to create reliable, future-ready technology tailored
              to each client&apos;s goals.
            </p>
            <div className="tw-flex lg:tw-justify-start tw-justify-center tw-w-full">
              <FillButton title={'Know More About Us'} className={'tw-w-fit tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-10 lg:tw-py-[8px] lg:tw-px-4 tw-px-10 tw-py-[13px]'} />
            </div>
            <div className="tw-self-stretch tw-grid tw-grid-cols-3 tw-gap-x-6 tw-items-center tw-text-center tw-mt-[19px]">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="tw-relative tw-flex tw-flex-col tw-items-center"
                >
                  <h3 className="tw-text-primary_black tw-text-[24px] md:tw-text-[36px] lg:tw-text-[48px] xl:tw-text-[70px] tw-font-bricolageGrotesque tw-font-semibold tw-leading-[1.3]">
                    <CountUp start={0} end={stat?.value || 0} duration={30} />{stat?.sign}
                  </h3>
                  <p className="tw-text-primary_gray tw-text-[12px] md:tw-text-[14px] lg:tw-text-[16px] xl:tw-text-[20px] tw-font-inter tw-font-normal tw-leading-[1.2] tw-my-0">
                    {stat.title}
                  </p>
                  {index !== stats.length - 1 && (
                    <span className="tw-h-12 md:tw-h-14 lg:tw-h-16 xl:tw-h-20 tw-w-px tw-absolute tw-top-1/2 tw-right-[-12px] tw-bg-[#DFE4E8] tw-transform tw--translate-y-1/2" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </Col>
        <Col lg={5} xl={4} xxl={5} className="lg:tw-order-2 tw-order-1">
          <div className="custom-bg-gradient tw-rounded-[45px] tw-p-[3px]" >
            <div className="tw-h-full tw-w-full tw-bg-white tw-rounded-[45px] tw-px-3 tw-pt-4 tw-pb-2.5">
              <div className="tw-relative tw-px-1">
                <div className="tw-w-full tw-h-full tw-aspect-[443/540] lg:tw-block tw-flex tw-justify-center tw-rounded-[30px] tw-overflow-hidden">
                  <Image
                    src={c4}
                    className="tw-object-contain tw-rounded-[30px]"
                    alt={"parth"}
                    fill
                  />
                </div>
                <div className="tw-absolute tw-left-1/2 tw-top-1/2 tw-transform -tw-translate-x-1/2 -tw-translate-y-1/2 tw-cursor-pointer" onClick={() => { setIsPlayVideo(true) }}>
                  <PlayIcon />
                </div>
              </div>
            </div>
          </div>
        </Col>
      </Row>
      {isPlayVideo && (
        <VideoModal
          isOpen={isPlayVideo}
          onClose={() => setIsPlayVideo(false)}
          videoSrc={"/videos/PM-Video.mp4"}
        />
      )}
    </Container>
  );
};
