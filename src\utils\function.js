import { LIVE_URL } from "@/config/config";
import axios from "axios";

export const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    const options = { day: "numeric", month: "long", year: "numeric" };
    return date?.toLocaleDateString("en-GB", options);
}

// export const metadata = {
//     title: "IT Consulting Service Provider Company - TST Technology",
//     description:
//       "Welcome to TST Technology, a leading IT consulting company. We offer web, app, SaaS, and software development services to help your business grow.",
//     openGraph: {
//       title: "IT Consulting Service Provider Company - TST Technology",
//       description:
//         "Welcome to TST Technology, a leading IT consulting company. We offer web, app, SaaS, and software development services to help your business grow.",
//       url: `https://tsttechnology.io`,
//       siteName: "TST Technology",
//       images: ["/images/logos/logo_og_tagline.png"],
//       locale: "en-us",
//       type: "website",
//       siteAuthor: "Tst",
//       width: 1200,
//       height: 600,
//     },
//     robots: {
//       index: true,
//       follow: true,
//       googleBot: {
//         index: true,
//         follow: true,
//         "max-video-preview": -1,
//         "max-image-preview": "large",
//         "max-snippet": -1,
//       },
//     },
//     twitter: {
//       title: "IT Consulting Service Provider Company - TST Technology",
//       card: "summary_large_image",
//       images: ["/images/logos/logo_og_tagline.png"],
//     },
//   };

export const metaData = {
    title: "",
    summary: "",
    locale: "en-us",
    type: "website",
    siteAuthor: "Tst",
    url: "",
    keywords: "",
    datePublished: "",
    dateModified: "",
    images: {
        data: [],
    },
    authors: [],
};

export const metaDataConvert = (data) => {
    if (!data?.seodata) return metaData;
    let seoData =
        data?.seodata && data?.seodata != null
            ? data?.seodata
            : metaData;
    const images =
        seoData?.images?.data?.length > 0
            ? seoData?.images?.data?.map((el) => {
                return el?.attributes?.url;
            })
            : [];
    const authors =
        seoData?.authors?.length > 0
            ? seoData?.authors?.map((el) => {
                return {
                    name: el?.name,
                    url: el?.url,
                };
            })
            : [];
    return {
        ...seoData,
        // datePublished: seoData?.publishedAt,
        // dateModified: seoData?.updatedAt,
        images,
        authors,
    };
};


export const metaDataGen = (seoData) => {

    return {
        metadataBase: new URL(LIVE_URL),
        // metadataBase: new URL("https://woofer-doc-demo.vercel.app/service"),
        title: {
            default: seoData?.title ?? "TST Technology IT",
            template: `%s | ${seoData?.title}`,
        },
        description: seoData?.summary,

        openGraph: {
            title: seoData?.title,
            description: seoData?.summary,
            url: `${LIVE_URL}${seoData?.url?.toLowerCase()}`,
            // url: `${"https://woofer-doc-demo.vercel.app/service"}`,
            siteName: seoData?.title ?? "TST Technology IT",
            images: seoData?.images,
            locale: seoData?.locale ?? "en-US",
            type: seoData?.type ?? "website",
        },
        alternates: {
            canonical: `${LIVE_URL}${seoData?.url?.toLowerCase()}`,
            // canonical: `${"https://woofer-doc-demo.vercel.app/service"}`,

            // types: {
            //   "application/rss+xml": `${ process.env.NEXT_DEV_BACKEND}/feed.xml`,
            // },
        },
        robots: {
            index: true,
            follow: true,
            googleBot: {
                index: true,
                follow: true,
                "max-video-preview": -1,
                "max-image-preview": "large",
                "max-snippet": -1,
            },
        },
        keywords: seoData?.keywords?.split(","),
        authors: seoData?.authors,
        // twitter: {
        //   title: seoData.title,
        //   card: "summary_large_image",
        //   images: seoData.images,
        // },
    }
}

export const loadHubspotForm = (hubspotId, portalId, formId, setShow) => {

    const script = document.createElement("script");
    script.charset = "utf-8";
    script.type = "text/javascript";
    script.src = "https://js.hsforms.net/forms/embed/v2.js";
    const config = {
        region: "na1",
        portalId: portalId
            ? portalId
            : "46795849",
        formId: formId
            ? formId
            : "fa244278-2f31-4994-ae9e-607998785a19",
    };
    script.addEventListener("load", () => {
        if (window.hbspt) {
            window.hbspt.forms.create({
                ...config,
                target: `#${hubspotId}`,
                onFormSubmit: function ($form) {

                    setTimeout(() => {
                        setShow(false);
                        if ($form && typeof $form.reset === 'function') {
                            $form.reset();
                            document.documentElement.style.overflow = "visible";
                        }
                        // setAnimateClose(false);
                        // document.documentElement.style.overflow = "visible"; // Restore scrollbar
                    }, 2000); // Duration of the animation

                },
            });
        }
    }, [])
    // script.onload = () => {
    //     if (window.hbspt) {

    //         window.hbspt.forms.create({
    //             ...config,
    //             target: `#${hubspotId}`,
    //             onFormSubmit: function ($form) {

    //                 setTimeout(() => {
    //                     setShow(false);
    //                     if ($form && typeof $form.reset === 'function') {
    //                         $form.reset();
    //                         document.documentElement.style.overflow = "visible";
    //                     }
    //                     // setAnimateClose(false);
    //                     // document.documentElement.style.overflow = "visible"; // Restore scrollbar
    //                 }, 2000); // Duration of the animation

    //             },

    //         });
    //     }
    // };

    document.body.appendChild(script);
    return () => {
        document.body.removeChild(script);
    };
}


export const isMobile_LG_View = () => {
    if (typeof window !== "undefined") {
        return window?.innerWidth <= 1024;
    }
};

export const isMobileSize = () => {
    if (typeof window !== 'undefined') {
        return window.innerWidth < 768;
    }
    return false;
};

export const convertEmbedToContentUrl = (embedUrl) => {
    const url = new URL(embedUrl);
    const videoId = url.pathname.split('/')[2];
    return `https://www.youtube.com/watch?v=${videoId}`;
}

export const fetchYouTubeVideoDetails = async (videoId) => {
    try {
        const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`);
        const text = await response.text();
        const uploadDateMatch = text.match(/"dateText":{"simpleText":"(.*?)"}/);
        const durationMatch = text.match(/"approxDurationMs":"(\d+)"/);

        if (uploadDateMatch && durationMatch) {
            const uploadDate = uploadDateMatch[1];
            const durationMs = parseInt(durationMatch[1], 10);
            const duration = new Date(durationMs).toISOString().substr(11, 8);
            return { uploadDate, duration };
        } else {
            throw new Error('Unable to extract video details');
        }
    } catch (error) {
        console.error('Error fetching video details:', error);
        return null;
    }
};

const getVideoId = (url) => {
    const match = url.match(/(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})/);
    return match ? match[1] : null;
};

export const fetchUploadDate = async (url) => {
    const videoId = getVideoId(url);
    if (!videoId) {
        console.log('Invalid YouTube URL');
        return {};
    }

    const apiKey = 'AIzaSyBHjby9vW-FH49M1XN0vvvm7Uk45RfM5ac'; // Replace with your actual API key
    const apiUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet&id=${videoId}&key=${apiKey}`;

    try {
        const response = await axios.get(apiUrl);
        const uploadDate = response.data.items[0]?.snippet?.publishedAt;
        const description = response.data.items[0]?.snippet?.description;

        return { uploadDate, description };
    } catch (err) {
        console.log('Failed to fetch the upload date:', err);
        return { uploadDate: null, description: null };
    }
};


export const sortByDate = (data) => {
    return data?.sort((a, b) => {
        const dateA = new Date(
            a?.attributes?.blog?.date || a?.attributes?.publishedAt
        )?.getTime();
        const dateB = new Date(
            b?.attributes?.blog?.date || b?.attributes?.publishedAt
        )?.getTime();
        return dateB - dateA; // Newest first
    })
}



export const filterClients = (data) => {
    return data?.filter(
        (ele) =>
            ele.title !== "Mine Magma" &&
            ele.title !== "Maple leaf" &&
            ele.title !== "Widur" &&
            ele.title !== "Toolplate" &&
            ele?.title !== "Wooffer"
    )
}

export const toCapitalize = (str) => {
    if (!str) return ""; // Handle empty or undefined strings
    return str.charAt(0).toUpperCase() + str.slice(1);
};


export const formatTheme = {
    ".pptx": "category-color-pptx",
    ".ppt": "category-color-pptx",
    ".pdf": "category-color-pdf",
    ".xlsx": "category-color-xlsx",
    ".csv": "category-color-xlsx",
    ".doc": "category-color-doc",
    ".docx": "category-color-doc",
};

