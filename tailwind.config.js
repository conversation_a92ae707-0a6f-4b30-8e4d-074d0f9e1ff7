/** @type {import('tailwindcss').Config} */

const plugin = require("tailwindcss/plugin");

module.exports = {
  prefix: "tw-", // Optional: Add a prefix to all Tailwind classes
  content: [
    "./src/**/*.{js,ts,jsx,tsx}", // App Router files
    "./pages/**/*.{js,ts,jsx,tsx}", // If using Pages Router
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        inter: "var(--font-inter)",
        bricolageGrotesque: "var(--font-bricolage-grotesque)",
      },
      colors: {
        primary_green: "#35B729",
        primary_blue: "#000623",
        primary_gray: "#75757A",
        primary_black: "#212125",
        primary_color: "#121212",
        secondary_color: "#977fcb",
        third_color: "#CBBFE5",
      },
      backdropBlur: {
        '200': '200px',
      },
      // 
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        "primary_bg": "linear-gradient(95.94deg, #602A9A 2.68%, #7D5FBE 97.07%)",
        "service-bg": "linear-gradient(129.65deg, #2E214B -6.91%, #0C0C0C 100%)",
        'service-card-bg': 'linear-gradient(121.82deg, rgba(255, 255, 255, 0.03) 1.57%, rgba(153, 153, 153, 0.03) 100%)',
        "innovation-tile": "radial-gradient(50% 50% at 50% 50%, rgba(125, 95, 190, 0.4) 0%, rgba(125, 95, 190, 0) 100%)",
        "service-gradient-2":
          "linear-gradient(180deg, #121212 0%, rgba(18, 18, 18, 0) 100%)",
        homeBg: "url('/home/<USER>')",
        aboutHeroBg: "url('/aboutPage/heroBg.png')",
        serviceHeroBg: "url('/service-page/heroBg.png')",
        partnersHeroBg: "url('/partnersAndPraise/heroBg.png')",
        contactUsHeroBg: "url('/contactUsPage/heroBg.png')",
        homeTeamBg: "url('/home/<USER>')",
        homeTeamMobileBg: "url('/home/<USER>')",
        serviceFeatureBg: "url('/service-page/featureBg.png')",
        // Old 
        "radial-green-fade":
          "radial-gradient(50% 50% at 50% 50%, rgba(53, 183, 41, 0.15) 0%, rgba(53, 183, 41, 0.01) 100%)",
        "tech-bg":
          "linear-gradient(180deg, rgba(247, 249, 251, 0) -10.65%, #F7F9FB 49.87%, rgba(247, 249, 251, 0) 110.38%)",
        "client-image-bg":
          "linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%)",
        "service-gradient":
          "linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 100%)",
        // "service-gradient-2":
        //   "linear-gradient(180deg, rgba(247, 249, 251, 0) 0%, #F7F9FB 50%, rgba(247, 249, 251, 0) 100%)",
        topRight: "url('/home/<USER>')",
        bottomLeft: "url('/home/<USER>')",
        topRightMobile: "url('/home/<USER>')",
        bottomLeftMobile: "url('/home/<USER>')",
        round: "url('/home/<USER>')",
        roundMobile: "url('/home/<USER>')",
        "footer-bg-2560": "url('/footer-bg/2560.png')",
        "footer-bg-1920": "url('/footer-bg/1920.png')",
        "footer-bg-1440": "url('/footer-bg/1440.png')",
        "footer-bg-1024": "url('/footer-bg/1024.png')",
        "footer-bg-768": "url('/footer-bg/768.png')",
        "footer-bg-375": "url('/footer-bg/375.png')",
        "footer-bg-4x": "url('/footer-bg/4x.png')",


        // "service-bg": "linear-gradient(180deg, #F7F9FB 0%, #FFFFFF 100%)",
        "state-bg": "url('/state-bg.webp')",
        "designStack-bg": "url(/DesignStackLogo/designStackBgImage.png)",
        "designStackMobile-bg": "url(/DesignStackLogo/designStackBgMobile.png)",
        "footer-logo-round": "url('/logo/footerLogoRound.png')",
        "service-bg-img": "url('/service-page/service-bg.png')",
        "service-bg-img2": "url('/service-page/single-service-bg.png')",
        "cta-bg-img": "url('/service-page/cta-bg.png')",
        "cta-bg-mobile-img": "url('/service-page/cta-bg-mobile.png')",
        "cta-bg-1024-img": "url('/service-page/cta-bg-1024.png')",
        "cta-bg-768-img": "url('/service-page/cta-bg-768.png')",
        "ex-bg-img": "url('/uiux-page/Ex-bg-1440.png')",
        "ex-bg-mobile-img": "url('/uiux-page/Ex-bg-mobile.png')",
        "ex-bg-1024-img": "url('/uiux-page/Ex-bg-1025.png')",
        "ex-bg-768-img": "url('/uiux-page/Ex-bg-768.png')",
        "agile-lineBg-img": "url('/aboutPage/agileMindset/line1440.png')",
        "agile-lineBg-mobile-img": "url('/aboutPage/agileMindset/lineMobile.png')",
        "agile-lineBg-1024-img": "url('/aboutPage/agileMindset/line1024.png')",
        "agile-lineBg-768-img": "url('/aboutPage/agileMindset/line768.png')",
        "leaders-bg-img": "url('/aboutPage/leaders/leaderBg.png')",
        "project-card-img": "url('/workicons/projectCard.png')",
        "project-card-mobile-img": "url('/workicons/projectCardMobile.png')",
        "project-card-425-img": "url('/workicons/projectCard425.png')",
        "project-img-linear":
          "linear-gradient(180deg, rgba(18, 18, 18, 0) 50%, #121212 100%)",
        "process-bg": "url('/service-page/UI-UX/processBG.png')",
        "process-mobile-bg": "url('/service-page/UI-UX/processBGMobile.png')",
        "process-mobile-bg-768": "url('/service-page/UI-UX/processBG7682.png')",
        "process-mobile-bg-1024":
          "url('/service-page/UI-UX/processBG1024.png')",
        "project-img-linear":
          "linear-gradient(180deg, rgba(18, 18, 18, 0) 50%, #121212 100%)",
        "process-mobile-bg-1024":
          "url('/service-page/UI-UX/processBG1024.png')",
        "project-img-linear":
          "linear-gradient(180deg, rgba(18, 18, 18, 0) 50%, #121212 100%)",
        "thinking-bg": "url('/service-page/UI-UX/thinkingBG.png')",
        "thinking-bg1280": "url('/service-page/UI-UX/thinkingBG1280.png')",
        "thinking-bg1024": "url('/service-page/UI-UX/thinkingBG1024.png')",
        "BE-Round": "url('/service-page/UI-UX/BeRound.png')",
        "mission-bg": "url('/aboutPage/missionBg.png')",
        "mission-bg-mobile": "url('/aboutPage/missionBgMobile.png')",
        "drive-tst-bg": "url('/aboutPage/driveTSTBG.png')",
        "drive-tst-bg-mobile": "url('/aboutPage/driveTSTBGMobile.png')",
        "white-to-green":
          "  linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, #0FC784 100%)",
        "white-to-pink":
          "  linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, #FF3A93 100%)",
        "white-to-purple":
          "  linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, #7F86FF 100%)",
        "white-to-yellow":
          " linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, #FEC21D 100%)",
        "white-to-blue":
          "  linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, #008DD4 100%)",
        "lightWhite-gradient":
          "linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 100%)",
        journeyHeroBg: "url('/aboutPage/Journey/bg.webp')",
        journeyHeroBgMobile: "url('/aboutPage/Journey/bgMobile.webp')",
        happingHeroBg: "url('/aboutPage/Journey/happening/bgImg.png')",
        teamBg: "url('/aboutPage/ValuesSection/team-bg1.png')",
        cultureBg: "url('/aboutPage/CultureAndValues/hero-bg.png')",
        valuesBgDesktop:
          "url('/aboutPage/CultureAndValues/valuesBgdesktop.png')",
        valuesBgMobile:
          "url('/aboutPage/CultureAndValues/valuesBgMobile.png')",
        agileBG:
          "url('/aboutPage/agileMindset/agileBG.png')",
        empowerbg:
          "url('/aboutPage/ourTeam/vector.png')",
        empowerbgMobile:
          "url('/aboutPage/ourTeam/empowerMobileBg.png')",
        teamHeroBg: "url('/aboutPage/ourTeam/teamHeroBG.png')",
        teamHeroBgMobile: "url('/aboutPage/ourTeam/teamHeroBGMobile.png')",
        blogHeroBg: "url('/blogpage/blogBG.png')",
        contentBgMobile: "url('/blogpage/contentBGMobile.png')",
        contentBgDesktop: "url('/blogpage/contentBG1440.png')",
        resourceBgDesktop: "url('/resourcePage/resourceBG.png')",
        "blog-gradient": "linear-gradient(180deg, #F7F9FB 0%, rgba(255, 255, 255, 0) 100%)",
      },

      screens: {
        xss: { min: "315px", max: "639px" },
        s1440: "1440px",
        1280: "1280px",
        320: "320px",
        425: "425px",
        375: "375px",
        360: "360px",
      },

      gridTemplateColumns: {
        24: "repeat(24, minmax(0, 1fr))",
      },
      borderWidth: {
        "1.5": "1.5px",
        "2.5": "2.5px",
      },
      keyframes: {
        slideInRight: {
          "0%": { transform: "translateX(100%)", opacity: "0" },
          "100%": { transform: "translateX(0)", opacity: "1" },
        },
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        fadeOut: {
          "0%": { opacity: "1" },
          "100%": { opacity: "0" },
          "0%": { opacity: "1" },
          "100%": { opacity: "0" },
        },
        bounceDown: {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(5px)" },
        },
        'spin-reverse': {
          from: { transform: 'rotate(360deg)' },
          to: { transform: 'rotate(0deg)' },
        },
      },
      boxShadow: {
        home_hero_section: "0px 0px 7px 0px #00000008",
        button_white: "0px 4px 4px 0px #0000001A",
        button_green: "0px 4px 4px 0px #35B7291A",
        tech_card: "1px 1px 10px #00000014",
        service_card: "0px 0px 15px 0px #2121251A",
        small_service_card: "0px 0px 9px 0px #0000000A",
        happenings_card: "1px 1px 10px 0px #00000014",
        founder_certification_card: "0px 0px 5px 0px #2121251A",
      },
      dropShadow: {
        service_section: "0px 0px 7px 0px #2e2e3e",
      },
      animation: {
        "spin-slow": "spin 6s linear infinite",
        "spin-slow-20": "spin 20s linear infinite",
        slideInRight: "slideInRight 1s ease-out forwards",
        fadeIn: "fadeIn 1s ease-in forwards",
        fadeOut: "fadeOut 1s ease-out forwards",
        fadeIn: "fadeIn 1s ease-in forwards",
        fadeOut: "fadeOut 1s ease-out forwards",
        fadeInOut: "fadeInOut 1s ease-in-out",
        bounceDown: "bounceDown 1s ease-in-out infinite",
        'spin-reverse': 'spin-reverse 20s linear infinite',
        'spin-reverse-slow': 'spin-reverse 40s linear infinite',
      },
    },
  },

  plugins: [],
};
