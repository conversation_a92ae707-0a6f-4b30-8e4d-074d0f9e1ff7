"use Client";

import FillButton from "@/components/buttons/FillButton";
import { EmailIcon } from "@/utils/icons";
import React from "react";
import { Container } from "reactstrap";

const CTASection = ({
  className,
  title,
  description,
  descriptionWidth = "tw-w-full",
  buttonText,
  isButton = true,
  isInput = false,
  onInputChange,
  inputValue,
  handleClick = () => { },
  inputPlaceholder = "Enter your email" }) => {
  return (
    <section
      className={`xl:tw-bg-cta-bg-img lg:tw-bg-cta-bg-1024-img md:tw-bg-cta-bg-768-img tw-bg-cta-bg-mobile-img tw-bg-cover tw-bg-no-repeat tw-bg-center ${className}`}
    >
      <Container>
        <div className="tw-flex tw-flex-col tw-items-center tw-justify-center xl:tw-py-[140px] md:tw-py-[65px] tw-py-[130px] tw-text-center tw-gap-[40px] tw-w-full">
          <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px] tw-w-full">
            <h1 className="tw-text-secondary_color tw-mb-0 xl:tw-text-[48px] lg:tw-text-[40px] md:tw-text-[35px] tw-text-[30px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold md:tw-w-[90%]">
              {title}
            </h1>
            <p className={`tw-mb-0 md:tw-text-lg tw-text-sm tw-text-[#E9E9E9] ${descriptionWidth} md:tw-leading-[140%] tw-leading-[120%]`}>
              {description}
            </p>
          </div>
          {isButton ? (
            <div className="tw-p-2.5 tw-border tw-border-white/30 tw-rounded-[1.25rem]" >
              <FillButton title={buttonText} className={'tw-bg-white !tw-text-[#433878] tw-rounded-[12px] xl:tw-py-2.5 xl:tw-px-5 tw-py-2 tw-px-7 lg:!tw-px-10 tw-border-none'} />
            </div>
          ) : null}

          {isInput ? (<div className="tw-flex tw-flex-col tw-items-center tw-gap-5 tw-w-full">
            <div className="tw-flex tw-w-full sm:tw-w-[50%] xl:tw-w-[45%] tw-h-[50px] lg:tw-h-[60px] tw-items-center tw-justify-between tw-rounded-[40px] tw-pl-3 lg:tw-pl-5 tw-pr-[5px] tw-py-[5px] tw-border tw-border-[#8093A5] md:tw-border-[#dfe4e8]">
              <div className="tw-flex tw-items-center tw-gap-2.5 tw-flex-1">
                <EmailIcon
                  strokeColor="black"
                  className="tw-aspect-square tw-w-6"
                />
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => onInputChange(e.target.value)}
                  className="tw-border-0 tw-outline-none tw-bg-transparent tw-h-full tw-p-0 tw-w-full tw-font-normal tw-text-[14px] tw-text-primary_gray lg:tw-text-[16px] placeholder:tw-text-primary_gray"
                  placeholder={inputPlaceholder}
                />
              </div>

              <FillButton
                title={buttonText}
                onClick={handleClick}
                className="lg:tw-block tw-hidden tw-h-full tw-rounded-[50px] tw-px-8 md:tw-px-5 xl:tw-px-6 tw-text-white tw-font-medium"
              />
            </div>
            <FillButton
              title={buttonText}
              onClick={handleClick}
              className="lg:tw-hidden tw-py-3 tw-rounded-[50px] tw-px-8 md:tw-px-5 xl:tw-px-6 tw-text-white tw-font-medium"
            /></div>
          ) : null}
        </div>
      </Container>
    </section>
  );
};

export default CTASection;
