import React from "react";
import { Container, Row } from "reactstrap";
import { StatCard } from "../card/StatCard";

export const StatsSection = ({ className }) => {
  const stats = [
    { value: "40", sign: "+", label: "Happy end-users" },
    { value: "4.5", label: "Client satisfaction rate" },
    { value: "3", sign: "+", label: "Years in business" },
    { value: "7", sign: "+", label: "Countries Served" },
    { value: "30", sign: "+", label: "Industry Experts" },
  ];

  return (
    <section
      className={` ${className}`}
    >
      <Container
        className="tw-grid 
      tw-gap-5 tw-grid-cols-2 sm:tw-gap-6 sm:tw-grid-cols-3 lg:tw-gap-7 lg:tw-grid-cols-5 xl:tw-gap-8 2xl:tw-gap-10 "
      >
        {stats.map((stat, index) => (
          <StatCard key={index} data={stat} />
        ))}
      </Container>
    </section>
  );
};
