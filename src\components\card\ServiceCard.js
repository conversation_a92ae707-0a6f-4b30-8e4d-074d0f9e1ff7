import Image from "next/image";
import React from "react";
import { NextArrowIcon, RightArrowIcon } from "@/utils/icons";
import Link from "next/link";

export const ServiceCard = ({ icon, title, subtitle, link }) => {
  return (
    <div className="tw-group tw-bg-service-card-bg tw-backdrop-blur-200 tw-flex tw-flex-col xl:tw-gap-[60px] tw-gap-[40px] tw-justify-between tw-p-10 tw-border  tw-border-[#FFFFFF4D] tw-rounded-[20px] tw-h-full hover:tw-shadow-service_card">

      <div className="tw-relative tw-aspect-square tw-w-[50px] lg:tw-w-[60px] 2xl:tw-w-[70px] group-hover:tw-translate-y-1 tw-transition-all tw-duration-500 tw-ease-in-out">
        <Image fill src={icon} alt={title} className="tw-invert" />
      </div>
      <div>
        <h3
          className="tw-font-medium tw-text-[22px] xl:tw-text-[30px] 2xl:tw-text-[32px] tw-text-white">
          {title}
        </h3>
        <Link href={link} className="tw-flex tw-gap-x-[10px] tw-items-center tw-cursor-pointer tw-group tw-no-underline">
          <span
            className=" md:tw-text-lg lg:tw-text-base xl:tw-text-lg 2xl:tw-text-xl tw-text-[#FFFFFFB2] group-hover:tw-text-secondary_color tw-transition-colors tw-duration-300 tw-ease-in-out"
          >
            {subtitle}
          </span>
          <NextArrowIcon className='tw-mt-0.5 tw-stroke-[#FFFFFFB2] group-hover:tw-stroke-secondary_color group-hover:tw-translate-x-1 tw-transition-all tw-duration-500 tw-ease-in-out' />
        </Link>
      </div>
    </div>
  );
};
