import { Container } from "reactstrap";
import React from "react";
import Image from "next/image";
import { Col, Row } from "reactstrap";
import FillButton from "@/components/buttons/FillButton";
import figmaLogo from "../../../../public/DesignStackLogo/figma.png";
import adobe_illustrator<PERSON>ogo from "../../../../public/DesignStackLogo/adobe_illustrator.png";
import adobe_photoshopLogo from "../../../../public/DesignStackLogo/adobe_photoshop.png";
import adobe_xdLogo from "../../../../public/DesignStackLogo/adobe_xd.png";
export const DesignStackSection = ({ className }) => {
  const technologies = [
    { image: figmaLogo },
    { image: adobe_illustratorLogo },
    { image: adobe_photoshopLogo },
    { image: adobe_xdLogo },
  ];
  return (
    <section className="tw-bg-designStackMobile-bg lg:tw-bg-designStack-bg tw-bg-no-repeat tw-bg-contain tw-bg-center">
      <Container className={`${className}`}>
        <Row className="gy-5">
          <Col lg="7" className="tw-flex tw-flex-col tw-justify-center">
            <div className="tw-flex tw-flex-col tw-items-center lg:tw-items-start lg:tw-gap-2.5 tw-gap-2">
              <div className="tw-shrink-0 tw-text-center lg:tw-text-left tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
                Design Stack
                <span className="tw-text-primary_green">
                  {" "}
                  Behind Our UI/UX Execellence
                </span>
              </div>
              <div className="tw-text-center lg:tw-text-left  lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter lg:tw-w-[80%] tw-leading-[140%]">
                At TST Technology, we leverage modern design tools and
                technologies like Adobe XD and Figma to create exceptional
                digital experiences.
              </div>
              <div className="tw-hidden lg:tw-block">
                <FillButton
                  title={"Start Your Project"}
                  className={
                    "tw-mt-0 lg:tw-mt-[30px] tw-w-fit tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-10 lg:tw-py-[8px] lg:tw-px-4 tw-px-10 tw-py-[13px]"
                  }
                />
              </div>
            </div>
          </Col>
          <Col lg="5" className="">
            <Row className="justify-content-center  g-4 ">
              {technologies.map((tech, index) => (
                <Col key={index} xs="6" md="3" lg="6">
                  <div className="tw-bg-white tw-rounded-[20px] tw-shadow-tech_card tw-p-7 md:tw-p-[32px] lg:tw-p-[40px]  tw-flex tw-justify-center tw-items-center">
                    <div className="tw-w-[55px] lg:tw-w-[70px] tw-aspect-square tw-relative">
                      <Image
                        src={tech.image}
                        alt="technology_logo"
                        fill
                        className="tw-object-contain"
                      />
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          </Col>
          <div className="lg:tw-hidden tw-flex tw-justify-center">
            <FillButton
              title={"Start Your Project"}
              className={
                " tw-w-fit tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-10 lg:tw-py-[8px] lg:tw-px-4 tw-px-10 tw-py-[13px]"
              }
            />
          </div>
        </Row>
      </Container>
    </section>
  );
};