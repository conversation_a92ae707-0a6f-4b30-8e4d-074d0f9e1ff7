"use client"
import React, { useRef, useState } from "react";
import { Container, Row, Col } from "reactstrap";
import { TestimonialCard } from "../card/TestimonialCard";
import { LeftArrowIcon, RightArrowIcon } from "@/utils/icons";
import Slider from "react-slick";

export const TestimonialsSection = ({ greenTitle, blackTitle, endTitle, subTitle, className, isButton = false }) => {
  const sliderRef = useRef(null);
  const [count, setCount] = useState(0);
  const [slidesToShow, setSlidesToShow] = useState(3);

  const testimonials = [
    {
      avatar:
        "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/575fa746ac097c0d2f00a113e71baa0e130b0b4b?placeholderIfAbsent=true",
      companyLogo:
        "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/d700f8b450b177ce62e382d40a27dab7effedbc5?placeholderIfAbsent=true",
      testimonial:
        "Chainex's AI-powered tools have transformed my trading strategy. I now make data-driven decisions with confidence and precision",
      name: "Daniel Rosewell",
      position: "Chief Executive Officer",
    },
    {
      avatar:
        "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/575fa746ac097c0d2f00a113e71baa0e130b0b4b?placeholderIfAbsent=true",
      companyLogo:
        "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/d700f8b450b177ce62e382d40a27dab7effedbc5?placeholderIfAbsent=true",
      testimonial:
        "Chainex's AI-powered tools have transformed my trading strategy. I now make data-driven decisions with confidence and precision",
      name: "Daniel Rosewell",
      position: "Chief Executive Officer",
    },
    {
      avatar:
        "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/575fa746ac097c0d2f00a113e71baa0e130b0b4b?placeholderIfAbsent=true",
      companyLogo:
        "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/d700f8b450b177ce62e382d40a27dab7effedbc5?placeholderIfAbsent=true",
      testimonial:
        "Chainex's AI-powered tools have transformed my trading strategy. I now make data-driven decisions with confidence and precision",
      name: "Daniel Rosewell",
      position: "Chief Executive Officer",
    },
    {
      avatar:
        "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/fbf9e4f15f29e1259e49f1cee6a4897835f73208?placeholderIfAbsent=true",
      companyLogo:
        "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/7f82927891ef2b536dbeab73a0ee895e28365eb8?placeholderIfAbsent=true",
      testimonial:
        "Chainex's AI-powered tools have transformed my trading strategy. I now make data-driven decisions with confidence and precision",
      name: "Sophia Mendell",
      position: "Chief Executive Officer",
    },
    {
      avatar:
        "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/81534a220104b5285741b936c3d9b1eeb59a6dfe?placeholderIfAbsent=true",
      companyLogo:
        "https://cdn.builder.io/api/v1/image/assets/3777543fb2a64ee2920b76e79e570c4e/7f82927891ef2b536dbeab73a0ee895e28365eb8?placeholderIfAbsent=true",

      testimonial:
        "Chainex's AI-powered tools have transformed my trading strategy. I now make data-driven decisions with confidence and precision",
      name: "Shirley Shetia",
      position: "Chief Executive Officer",
    },
  ];

  // console.log(count, testimonials.length);

  const settings = {
    dots: false,
    pauseOnHover: true,
    arrows: false,
    infinite: isButton ? false : true,
    autoplay: true,
    autoplaySpeed: 2000,
    speed: 1000,
    slidesToShow: 3,
    slidesToScroll: 1,
    swipeToSlide: true,
    responsive: [
      {
        breakpoint: 1440,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
    beforeChange: (oldIndex, newIndex) => setCount(newIndex),
    afterChange: (current) => {
      // get current slidesToShow from slider instance
      const slick = sliderRef.current;
      if (slick) {
        const currentSlidesToShow = slick.innerSlider.props.slidesToShow;
        setSlidesToShow(currentSlidesToShow);
      }
    },
    onInit: () => {
      const slick = sliderRef.current;
      if (slick) {
        const currentSlidesToShow = slick.innerSlider.props.slidesToShow;
        setSlidesToShow(currentSlidesToShow);
      }
    },
  };

  const goToPrev = () => {
    if (count > 0) {
      sliderRef.current?.slickPrev();
    }
  };

  const goToNext = () => {
    if (count < testimonials.length - 1) {
      sliderRef.current?.slickNext();
    }
  };
  return (
    <section className={`customer-section-bg customer-card ${className} tw-relative tw-overflow-hidden`}>
      <div className="tw-absolute tw-top-[15rem]  -tw-left-[6rem]  tw-h-[15rem] tw-w-[25rem] tw-rounded-full tw-bg-[#8C45FF66] tw-blur-[110px] " />
      <div className="tw-absolute tw-top-[1rem]  -tw-right-[6rem]  tw-h-[8rem] tw-w-[16rem] tw-rounded-full tw-bg-[#8C45FF66] tw-blur-[110px] " />
      <Container className="">
        <h2 className="tw-text-white tw-font-bricolageGrotesque md:tw-text-4xl tw-text-[26px] tw-font-bold tw-leading-none tw-text-center tw-self-center ">
          <span>
            {blackTitle}{" "}
          </span>
          <span className="tw-text-secondary_color tw-font-bricolageGrotesque">
            {greenTitle}
          </span>
          <span className="">
            {endTitle}
          </span>
        </h2>
        <p className="tw-text-primary_gray tw-text-sm  tw-text-center tw-mx-auto  tw-mt-2.5 xl:tw-w-[55%] lg:tw-w-3/4">
          {subTitle}
        </p>
        <div className="tw-mt-[57px] tw-max-md:tw-mt-10">
          <Row>
            <Slider {...settings} ref={sliderRef}>
              {testimonials.map((testimonial, index) => (
                <Col md={4} key={index} className="tw-mb-5 tw-px-3">
                  <TestimonialCard
                    avatar={testimonial.avatar}
                    companyLogo={testimonial.companyLogo}
                    testimonial={testimonial.testimonial}
                    name={testimonial.name}
                    position={testimonial.position}
                  />
                </Col>
              ))}
            </Slider>
          </Row>
        </div>
        {isButton ? <div className="tw-flex tw-justify-center tw-items-center tw-gap-5 tw-mt-5">
          <button
            className={`tw-rounded-full tw-p-[10px] tw-border ${count === 0 ? 'tw-bg-transparent tw-border-white/70' : 'tw-bg-secondary_color tw-border-transparent'}`}
            onClick={goToPrev}
            disabled={count === 0}
            aria-label="Previous"
          >
            <LeftArrowIcon className={`${count === 0 ? 'tw-fill-secondary_color' : 'tw-fill-white'}`} />
          </button>
          <button
            className={`tw-rounded-full tw-p-[10px] tw-border ${count >= testimonials.length - slidesToShow ? 'tw-bg-transparent  tw-border-white/70' : 'tw-bg-secondary_color  tw-border-transparent'}`}
            onClick={goToNext}
            // disabled={count === testimonials.length - 1}
            disabled={count >= testimonials.length - slidesToShow}
            aria-label='Next'
          >
            <RightArrowIcon className={`tw-w-6 tw-h-6  ${count >= testimonials.length - slidesToShow ? 'tw-fill-secondary_color' : 'tw-fill-white '}`} />
          </button>
        </div> : null}
      </Container>
    </section>

  );
};
