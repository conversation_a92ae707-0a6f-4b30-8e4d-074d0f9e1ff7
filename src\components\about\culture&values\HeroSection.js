import FillButton from '@/components/buttons/FillButton'
import React from 'react'
import { Container } from 'reactstrap'
import culterMen from "../../../../public/aboutPage/CultureAndValues/culterMen.png"
import Image from 'next/image'
export const HeroSection = () => {
    return (
        <section className=" tw-bg-[#F7F9FB] tw-shadow-home_hero_section lg:tw-pb-[.9375rem] tw-pb-2 tw-overflow-hidden">
            <div className=" tw-relative tw-bg-cultureBg tw-bg-cover tw-bg-no-repeat tw-bg-bottom lg:tw-mx-[.9375rem] tw-mx-2 tw-overflow-hidden">
                <Container>
                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center 2xl:tw-pt-[120px] xl:tw-pt-[100px] lg:tw-pt-[90px] md:tw-pt-[50px] tw-pt-[90px] tw-text-center lg:tw-gap-[60px] tw-gap-[50px]">
                        <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center 2xl:tw-gap-[30px] lg:tw-gap-[26px] tw-gap-[24px]">
                            <h1 className="tw-mb-0 xl:tw-text-[70px] lg:tw-text-[55px] md:tw-text-[40px] tw-text-[38px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold md:tw-w-[78%]">
                                Culture That Powers Innovation
                            </h1>
                            <p className="tw-mb-0 tw-text-[22px] lg:tw-text-xl md:tw-text-lg tw-text-base tw-text-primary_gray lg:tw-w-3/4 xl:tw-w-[55%] md:tw-w-[87%] tw-w-[90%] md:tw-leading-[140%] tw-leading-[120%]">
                                At TST Technology, our culture of trust, collaboration, and learning empowers people to innovate, grow, and celebrate success — together.
                            </p>
                        </div>
                        <FillButton title={'Discover Our Culture'} className={'tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-5 tw-py-[8px] tw-px-4'} />
                        <div className="tw-relative tw-w-[320px] tw-h-[260px] tw-z-[99]">
                            <Image
                                src={culterMen}
                                fill
                                className="tw-object-cover"
                                alt={`culterMen image`}
                            />
                        </div>
                    </div>
                </Container>
                <div className='tw-absolute md:tw-bottom-0 -tw-bottom-96 2xl:-tw-left-[1300px] xl:-tw-left-[1450px] md:-tw-left-[1650px] -tw-left-[450px]  tw-w-full tw-h-full'>
                    <svg viewBox="0 0 2079 2069" fill="none" xmlns="http://www.w3.org/2000/svg" className="tw-animate-spin-reverse md:tw-w-[2079px] md:tw-h-[2069px] tw-w-[573px] tw-h-[573px]">
                        <path d="M917.484 43.8007C988.321 -14.5552 1090.57 -14.5552 1161.41 43.8007C1209.68 83.5652 1274.3 97.3015 1334.57 80.6078C1423.02 56.1092 1516.43 97.698 1557.41 179.821C1585.33 235.781 1638.78 274.614 1700.63 283.878C1791.4 297.473 1859.82 373.459 1863.85 465.15C1866.6 527.628 1899.63 584.845 1952.36 618.464C2029.75 667.802 2061.35 765.047 2027.74 850.45C2004.84 908.645 2011.75 974.351 2046.25 1026.51C2096.88 1103.06 2086.19 1204.75 2020.75 1269.1C1976.16 1312.95 1955.74 1375.78 1966.04 1437.47C1981.16 1527.99 1930.04 1616.55 1844.08 1648.71C1785.51 1670.63 1741.3 1719.73 1725.62 1780.28C1702.62 1869.12 1619.89 1929.22 1528.28 1923.65C1465.86 1919.85 1405.5 1946.72 1366.56 1995.66C1309.4 2067.46 1209.38 2088.72 1127.96 2046.37C1072.48 2017.51 1006.41 2017.51 950.93 2046.37C869.508 2088.72 769.492 2067.46 712.336 1995.66C673.388 1946.72 613.032 1919.85 550.608 1923.65C458.999 1929.22 376.277 1869.12 353.269 1780.28C337.591 1719.73 293.383 1670.63 234.811 1648.71C148.855 1616.55 97.73 1527.99 112.849 1437.47C123.151 1375.78 102.735 1312.95 58.143 1269.1C-7.29786 1204.75 -17.9859 1103.06 32.6459 1026.51C67.147 974.351 74.053 908.645 51.1509 850.45C17.541 765.047 49.1379 667.802 126.528 618.464C179.262 584.845 212.296 527.628 215.044 465.15C219.077 373.459 287.495 297.473 378.261 283.878C440.111 274.614 493.561 235.781 521.484 179.821C562.461 97.698 655.871 56.1092 744.32 80.6078C804.59 97.3015 869.214 83.5652 917.484 43.8007Z" fill="white" />
                    </svg>
                </div>
                <div className='tw-absolute md:tw-bottom-0 -tw-bottom-96  2xl:-tw-right-[1300px]  xl:-tw-right-[1450px] md:-tw-right-[1650px] -tw-right-[450px] tw-h-full '>
                    <svg viewBox="0 0 2079 2069" fill="none" xmlns="http://www.w3.org/2000/svg" className="tw-animate-spin-slow-20 md:tw-w-[2079px] md:tw-h-[2069px] tw-w-[573px] tw-h-[573px]">
                        <path d="M917.484 43.8007C988.321 -14.5552 1090.57 -14.5552 1161.41 43.8007C1209.68 83.5652 1274.3 97.3015 1334.57 80.6078C1423.02 56.1092 1516.43 97.698 1557.41 179.821C1585.33 235.781 1638.78 274.614 1700.63 283.878C1791.4 297.473 1859.82 373.459 1863.85 465.15C1866.6 527.628 1899.63 584.845 1952.36 618.464C2029.75 667.802 2061.35 765.047 2027.74 850.45C2004.84 908.645 2011.75 974.351 2046.25 1026.51C2096.88 1103.06 2086.19 1204.75 2020.75 1269.1C1976.16 1312.95 1955.74 1375.78 1966.04 1437.47C1981.16 1527.99 1930.04 1616.55 1844.08 1648.71C1785.51 1670.63 1741.3 1719.73 1725.62 1780.28C1702.62 1869.12 1619.89 1929.22 1528.28 1923.65C1465.86 1919.85 1405.5 1946.72 1366.56 1995.66C1309.4 2067.46 1209.38 2088.72 1127.96 2046.37C1072.48 2017.51 1006.41 2017.51 950.93 2046.37C869.508 2088.72 769.492 2067.46 712.336 1995.66C673.388 1946.72 613.032 1919.85 550.608 1923.65C458.999 1929.22 376.277 1869.12 353.269 1780.28C337.591 1719.73 293.383 1670.63 234.811 1648.71C148.855 1616.55 97.73 1527.99 112.849 1437.47C123.151 1375.78 102.735 1312.95 58.143 1269.1C-7.29786 1204.75 -17.9859 1103.06 32.6459 1026.51C67.147 974.351 74.053 908.645 51.1509 850.45C17.541 765.047 49.1379 667.802 126.528 618.464C179.262 584.845 212.296 527.628 215.044 465.15C219.077 373.459 287.495 297.473 378.261 283.878C440.111 274.614 493.561 235.781 521.484 179.821C562.461 97.698 655.871 56.1092 744.32 80.6078C804.59 97.3015 869.214 83.5652 917.484 43.8007Z" fill="white" />
                    </svg>
                </div>

            </div>


        </section >
    )
}
