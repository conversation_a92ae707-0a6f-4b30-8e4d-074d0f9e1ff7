"use client"
import React, { useEffect, useRef } from 'react'
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { Container } from 'reactstrap';
import { AgileIcon } from '@/utils/icons';

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

export const HeroSection = () => {
    const svgRef = useRef(null);
    const titleRef = useRef(null);
    const paragraphRef = useRef(null);

    useEffect(() => {
        // Title and paragraph animation (top to bottom)
        if (titleRef.current && paragraphRef.current) {
            gsap.set([titleRef.current, paragraphRef.current], {
                y: -50,
                opacity: 0
            });

            const headerTl = gsap.timeline({
                scrollTrigger: {
                    trigger: titleRef.current,
                    start: "top 90%",
                    toggleActions: "play none none none"
                }
            });

            headerTl
                .to(titleRef.current, {
                    y: 0,
                    opacity: 1,
                    duration: 0.8,
                    ease: "power2.out"
                })
                .to(paragraphRef.current, {
                    y: 0,
                    opacity: 1,
                    duration: 0.8,
                    ease: "power2.out"
                }, "-=0.4"); // Start 0.4s before title finishes
        }
        if (svgRef.current) {
            // Get all path elements
            const allPaths = svgRef.current.querySelectorAll('path');

            // Separate green sections from other elements
            const greenSections = Array.from(allPaths).filter(path => {
                const fill = path.getAttribute('fill');
                return fill && (fill.includes('#42D833') || fill.includes('#1DC10E') || fill.includes('#35B729') || fill.includes('#31AA26') || fill.includes('#249E19') || fill.includes('#208B16') || fill.includes('#197111'));
            });

            const whiteTextElements = Array.from(allPaths).filter(path => {
                const fill = path.getAttribute('fill');
                return fill && fill.includes('white');
            });

            const otherElements = Array.from(allPaths).filter(path => {
                const fill = path.getAttribute('fill');
                return fill && fill.includes('#212125');
            });

            // Group green sections by color and find their corresponding white text
            const groupedGreenSections = [];
            const groupedWhiteText = [];
            const processedIndices = new Set();

            greenSections.forEach((section, index) => {
                if (processedIndices.has(index)) return;

                const fill = section.getAttribute('fill');
                const group = [section];
                processedIndices.add(index);

                // Find other sections with the same fill color
                greenSections.forEach((otherSection, otherIndex) => {
                    if (otherIndex !== index && !processedIndices.has(otherIndex) &&
                        otherSection.getAttribute('fill') === fill) {
                        group.push(otherSection);
                        processedIndices.add(otherIndex);
                    }
                });

                groupedGreenSections.push(group);

                // Find white text that comes after this green group
                const greenGroupEndIndex = Math.max(...group.map(g => Array.from(allPaths).indexOf(g)));
                const correspondingWhiteText = [];

                // Look for white text elements that come after this green group
                for (let i = greenGroupEndIndex + 1; i < allPaths.length; i++) {
                    const path = allPaths[i];
                    const pathFill = path.getAttribute('fill');

                    // If we hit another green section, stop looking
                    if (pathFill && (pathFill.includes('#42D833') || pathFill.includes('#1DC10E') || pathFill.includes('#35B729') || pathFill.includes('#31AA26') || pathFill.includes('#249E19') || pathFill.includes('#208B16') || pathFill.includes('#197111'))) {
                        break;
                    }

                    // If it's white text, add it to this group
                    if (pathFill && pathFill.includes('white')) {
                        correspondingWhiteText.push(path);
                    }
                }

                groupedWhiteText.push(correspondingWhiteText);
            });

            // Set all white text and dark text to be invisible initially
            gsap.set(whiteTextElements, { opacity: 0 });
            gsap.set(otherElements, { opacity: 0 }); // Dark text will animate last

            // Reverse the array to animate from right to left (rightmost groups first)
            const reversedGroupedSections = [...groupedGreenSections].reverse();

            // Set initial state - all green sections invisible
            gsap.set(greenSections, { opacity: 0 });

            // Create timeline for sequential animation (play only once)
            const tl = gsap.timeline({
                scrollTrigger: {
                    trigger: svgRef.current,
                    start: "top 80%",
                    toggleActions: "play none none none" // Play once, no repeat or reverse
                }
            });

            // Reverse both arrays to animate from right to left
            const reversedGroupedWhiteText = [...groupedWhiteText].reverse();

            // Animate each group of green sections with their corresponding white text
            reversedGroupedSections.forEach((greenGroup, index) => {
                const correspondingWhiteText = reversedGroupedWhiteText[index];

                // Animate green card and its white text together
                tl.to([...greenGroup, ...correspondingWhiteText], {
                    opacity: 1,
                    duration: 0.8,
                    ease: "power2.out"
                }, index * 0.7); // 0.7 second delay between each group
            });

            // Finally animate the dark text (line 133) as the last element
            tl.to(otherElements, {
                opacity: 1,
                duration: 1.0,
                ease: "power2.out"
            }, (reversedGroupedSections.length * 0.7) + 0.1); // After all green sections + 0.5s delay

            // // Debug: Log the number of elements found
            // console.log('Green sections found:', greenSections.length);
            // console.log('Grouped green sections:', groupedGreenSections.length);
            // console.log('Groups details:', groupedGreenSections.map((group, index) =>
            //     `Group ${index + 1}: ${group.length} green paths (${group[0].getAttribute('fill')}) + ${groupedWhiteText[index].length} white text elements`
            // ));
            // console.log('Total white text elements found:', whiteTextElements.length);
            // console.log('Other elements found:', otherElements.length);
        }
    }, []);
    return (
        <section className=" tw-bg-[#F7F9FB] tw-shadow-home_hero_section lg:tw-pb-[.9375rem] tw-pb-2 ">
            <div className="tw-bg-agileBG tw-bg-cover lg:tw-rounded-[1.25rem] tw-rounded-[.9375rem] lg:tw-mx-[.9375rem] tw-mx-2 ">
                <Container>
                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center xl:tw-py-[100px] lg:tw-py-[90px] md:tw-py-[70px] tw-pt-[70px] tw-text-center md:tw-gap-[70px] ">
                        <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px]">
                            <h1
                                ref={titleRef}
                                className="tw-mb-0 xl:tw-text-[70px] lg:tw-text-[55px] md:tw-text-[40px] tw-text-[38px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-semibold "
                            >
                                Agile Mindset. Smarter Delivery.
                            </h1>
                            <p
                                ref={paragraphRef}
                                className="tw-mb-0 tw-text-[22px] lg:tw-text-xl md:tw-text-lg tw-text-base tw-text-primary_gray "
                            >
                                We follow Agile not just as a process, but as a culture—one that empowers faster delivery, stronger collaboration, and solutions that evolve with your business.
                            </p>
                        </div>
                        <AgileIcon className="tw-w-full md:tw-h-[360px] tw-h-[300px]" width={856} ref={svgRef} />
                    </div>
                </Container>

            </div>
        </section>


    )
}
