"use client"
import CountUp from "@/common/CountUp";
import React from "react";

export const StatCard = ({ data }) => {
  return (
    <div className="tw-font-inter tw-text-center tw-rounded-[20px] tw-border tw-border-[#FFFFFF4D] tw-shadow-[1px_3px_10px_#00000012] tw-bg-[#43387866] tw-backdrop-blur-200">
      <div className="tw-bg-white  tw-mx-auto tw-w-[60px] tw-h-[3px] tw-rounded-[10px]" />
      <div className="md:tw-py-[30px] tw-py-[22px]">
        <span className="tw-block tw-text-white tw-font-bold tw-text-[38px] md:tw-text-[40px] lg:tw-text-[42px] xl:tw-text-[44px] 2xl:tw-text-[48px]">
          <CountUp start={0} end={data?.value || 0} duration={30} />{data?.sign}
        </span>
        <span className="tw-block tw-text-white tw-font-medium tw-text-[14px] md:tw-text-[16px] xl:tw-text-[18px]2xl:tw-text-[20px]">
          {data?.label}
        </span>
      </div>

    </div>
  );
};

