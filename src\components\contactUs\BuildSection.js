import React from "react";
import { Col, Container, Row } from "reactstrap";
import FillButton from "../buttons/FillButton";
import CountUp from "@/common/CountUp";
import Image from "next/image";

import sempleImg from "/public/aboutPage/Journey/happening/img6.png";

export const BuildSection = ({ className }) => {
  return (
    <>
      <section
        className={`${className} tw-bg-designStackMobile-bg lg:tw-bg-designStack-bg tw-bg-no-repeat tw-bg-contain tw-bg-center`}
      >
        <Container tag="section">
          <div className="tw-bg-[#F7F9FB] tw-rounded-[20px] md:tw-rounded-[30px] lg:tw-rounded-[40px] tw-p-[50px_30px] lg:tw-p-[40px] xl:tw-p-[50px]">
            <Row className="gy-5">
              <Col lg="7" className="tw-flex tw-flex-col tw-justify-center">
                <div className="tw-flex tw-flex-col tw-items-center lg:tw-items-start lg:tw-gap-2.5 tw-gap-2">
                  <div className="tw-shrink-0 tw-text-center lg:tw-text-left tw-text-primary_black tw-leading-[1.2] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px] xl:tw-w-[80%]">
                    Let’s Build Something Great Together 🚀
                  </div>
                  <div className="tw-text-center lg:tw-text-left  lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter lg:tw-w-[80%] tw-leading-[1.3]">
                    Have an idea, product, or challenge? Our team is ready to
                    design, build, and scale your vision with the power of
                    technology. We turn ambition into action.
                  </div>
                  <div className="tw-hidden lg:tw-block">
                    <FillButton
                      title={"Start Your Project"}
                      className={
                        "tw-mt-0 lg:tw-mt-[30px] tw-w-fit tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-10 lg:tw-py-[8px] lg:tw-px-4 tw-px-10 tw-py-[13px]"
                      }
                    />
                  </div>
                </div>
              </Col>
              <Col lg="5" className="">
                <div className="tw-relative tw-aspect-[500/350] tw-w-full tw-rounded-[20px]">
                  <Image
                    src={sempleImg}
                    alt="image"
                    fill
                    className="tw-object-contain"
                  />
                </div>
              </Col>
              <div className="lg:tw-hidden tw-flex tw-justify-center">
                <FillButton
                  title={"Start Your Project"}
                  className={
                    " tw-w-fit tw-rounded-[12px] xl:tw-py-[15px] xl:tw-px-10 lg:tw-py-[8px] lg:tw-px-4 tw-px-10 tw-py-[13px]"
                  }
                />
              </div>
            </Row>
          </div>
        </Container>
      </section>
    </>
  );
};
