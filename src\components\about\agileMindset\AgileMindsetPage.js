import React from "react";
import { HeroSection } from "./HeroSection";
import { GrowSection } from "./GrowSection";
import { TestimonialsSection } from "@/components/home/<USER>";
import { FaqSection } from "@/common/FaqSection";
import { AgileSection } from "./AgileSection";
import DetailSection from "@/components/singleService/UIUX/DetailSection";

export const AgileMindsetPage = () => {
  return (
    <>
      <HeroSection />
      <DetailSection
        className="md:tw-py-[100px] tw-py-[70px]"
        description={`TST Technology does everything- UI/UX design, web and mobile app development, and software development- with the Agile way of thinking. It allows TST Technology to bring design to concepts, create working features from ideas, do successful cross-team collaborations, and adapt quickly to new markets ensuring on-demand products for our clients. Staying nimble and continuously improving across any field is key; the Agile mindset places this at its core.`}
      />
      <AgileSection />
      <GrowSection className="md:tw-py-[100px] tw-py-[70px]" />
      <TestimonialsSection
        isButton={true}
        className="md:tw-py-[100px] tw-py-[70px]"
        blackTitle="What "
        greenTitle="Our Clients Say"
        subTitle="Our team of developers quickly adapts to new technological innovations to provide best solutions to our clients. Our tech stack is AI-enhanced to deliver solutions better and quicker."
      />
      <FaqSection className="md:tw-py-[100px] tw-py-[40px]" />
    </>
  );
};
