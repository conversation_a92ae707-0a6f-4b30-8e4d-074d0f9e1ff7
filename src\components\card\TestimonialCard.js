"use client";
import Image from "next/image";
import React from "react";
export const TestimonialCard = ({
  avatar,
  companyLogo,
  testimonial,
  name,
  position,
}) => {
  return (
    <div className="group tw-relative tw-inline-block tw-z-10">
      <div className="tw-relative tw-z-10 tw-rounded-[20px] tw-bg-transparent tw-border tw-border-[#FFFFFF4D] tw-shadow-[1px_1px_10px_0px_#00000014] custom-border-gradient">
        <div className="tw-pt-[24px] tw-px-[20px]">
          <div className="tw-flex tw-items-center tw-justify-between tw-mb-[24px]">
            <Image
              src={avatar}
              alt={name}
              width={60}
              height={60}
              className="tw-w-[60px] tw-h-[60px] tw-rounded-full tw-object-cover"
            />
            <div className="tw-flex tw-items-center tw-gap-1">
              <Image
                src={companyLogo}
                alt={name}
                width={126}
                height={60}
                className="tw-w-[126px] tw-h-[60px] tw-object-contain"
              />
            </div>
          </div>

          <p className="lg:tw-text-base tw-text-sm tw-leading-[120%] tw-text-[#FFFFFFE5] tw-mb-[45px]">
            {testimonial}
          </p>
        </div>

        <div className="tw-mb-5 tw-w-full tw-border-t-[1px] tw-border-[#FFFFFF4D]" />

        <div className="tw-pb-[26px] tw-px-[20px]">
          <p className="tw-font-medium tw-text-sm tw-text-[#fff] tw-mb-0">{name}</p>
          <p className="tw-text-[#FFFFFFE5] tw-mb-0">{position}</p>
        </div>
      </div>
    </div>
  );
};
