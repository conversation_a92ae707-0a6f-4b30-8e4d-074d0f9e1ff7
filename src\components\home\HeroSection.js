
import React from 'react';
import { Container } from 'reactstrap';
import ProjectClientSlider from '@/common/Slider/ProjectClientSlider';
import FillButton from '../buttons/FillButton';

const HeroSection = () => {
  return (
    <section>
      <div className="tw-relative tw-h-screen tw-bg-homeBg tw-bg-no-repeat tw-bg-cover tw-bg-center tw-shadow-home_hero_section lg:tw-pb-[0] tw-pb-2">

        <div className='tw-relative tw-top-8 md:tw-top-24 lg:tw-top-[9rem] s1440:tw-top-[12rem] lg:tw-mx-[.9375rem] tw-mx-2 '>

          <Container>
            <div className='tw-flex tw-flex-col tw-items-center tw-justify-center   tw-text-center lg:tw-gap-[60px] tw-gap-[50px] tw-relative tw-z-20'>
              <div className='tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center lg:tw-gap-[30px] tw-gap-[24px]'>
                <h1 className='tw-mb-0 tw-bg-[linear-gradient(90.86deg,#FFFFFF_1.71%,#E2DDED_22.12%,#977FCB_97.59%)] tw-bg-clip-text tw-text-transparent s1440:!tw-text-[70px] xl:tw-text-[60px]  lg:tw-text-[55px] md:tw-text-[40px] tw-text-[34px] tw-font-bricolageGrotesque tw-leading-[130%] tw-font-bold md:tw-w-[90%] lg:tw-w-full'>Your Partner for Scalable, Affordable and Premium Software Solutions.</h1>
                <p className='tw-mb-0 tw-text-[22px] lg:tw-text-xl md:tw-text-lg tw-text-base tw-text-third_color lg:tw-w-[60%] md:tw-w-3/4 tw-w-[90%] md:tw-leading-[140%] tw-leading-[120%]'>From concept to code — we build powerful digital solutions that  grow with your business. You dream it, we build it.</p>
              </div>
              <div className='tw-flex tw-justify-center tw-items-center tw-gap-6'>
                <div className="tw-p-2.5 tw-border tw-border-white/30 tw-rounded-[1.25rem]" >
                  <FillButton title={`Let's Build it!`} className={'tw-bg-white !tw-text-[#433878] tw-rounded-[12px] xl:tw-py-2.5 xl:tw-px-5 tw-py-2 tw-px-7 lg:!tw-px-10 tw-border-none'} />
                </div>
                <div className="tw-p-2.5 tw-border tw-border-white/30 tw-rounded-[1.25rem]" >
                  <FillButton title={`Portfolio`} className={'tw-bg-[#411E64] tw-rounded-[12px] xl:tw-py-2.5 xl:tw-px-5 tw-py-2 tw-px-7 lg:!tw-px-10 !tw-border !tw-border-white/60'} />
                </div>
              </div>
            </div>

          </Container>
          {/* <ProjectClientSlider /> */}
        </div>

        <div className='tw-w-full tw-absolute -tw-bottom-0 tw-z-10 tw-h-40 tw-bg-[linear-gradient(180deg,_rgba(18,18,18,0)_3.8%,#121212_100%)]' />
      </div>
      <Container className='tw-relative tw-z-20'>
        <ProjectClientSlider />
      </Container>


    </section>
  );
};

export default HeroSection;

;
