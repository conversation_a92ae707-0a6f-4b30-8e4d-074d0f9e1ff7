"use client";
import Image from "next/image";
import React, { useState } from "react";
import c4 from "../../../public/client/c4.png";
import { Container } from "reactstrap";
import { PlayIcon2 } from "@/utils/icons";
import { useHorizontalScrollDrag } from "@/hook/useHorizontalScrollDrag ";
const videoData = [
  {
    id: 1,
    src: "https://youtu.be/OfUUCYzrFhI?si=xaWEdYrsZHoiJ1lr",
    thumbnail: c4,
  },
  {
    id: 2,
    src: "https://youtu.be/OfUUCYzrFhI?si=xaWEdYrsZHoiJ1lr",
    thumbnail: c4,
  },
  {
    id: 3,
    src: "https://youtu.be/OfUUCYzrFhI?si=xaWEdYrsZHoiJ1lr",
    thumbnail: c4,
  },
  {
    id: 4,
    src: "https://youtu.be/OfUUCYzrFhI?si=xaWEdYrsZHoiJ1lr",
    thumbnail: c4,
  },
];

const VideosSlider = ({ className }) => {
  const [activeId, setActiveId] = useState(1);
  const scrollRef = useHorizontalScrollDrag();
  return (
    <Container
      className={`tw-flex tw-flex-col tw-items-center tw-gap-[40px] lg:tw-gap-[70px] ${className}`}
    >
      <div className="tw-text-center tw-flex tw-flex-col tw-items-center lg:tw-gap-2.5  tw-gap-2">
        <div className="tw-text-primary_black tw-leading-[120%] tw-font-bold tw-font-bricolageGrotesque tw-text-[26px] lg:tw-text-[32px] xl:tw-text-[36px]">
          <span>Watch These</span>
          <span className="tw-text-primary_green">
            {" "}
            Videos for More Insights
          </span>
        </div>
        <div className="lg:tw-text-[14px] tw-text-[12px] tw-text-primary_gray tw-font-inter tw-leading-[120%]">
          We rely on a curated tech stack to bring intuitive and impactful user
          experiences to life.
        </div>
      </div>

      <div
        className="tw-flex tw-overflow-x-auto tw-gap-4 hide-scrollbar tw-snap-x tw-snap-mandatory"
        ref={scrollRef}
      >
        {videoData.map((video) => (
          <div
            key={video.id}
            className={`tw-flex-shrink-0 tw-rounded-xl tw-transition-all tw-duration-500 tw-relative ${
              activeId === video.id
                ? "lg:tw-w-[685px] lg:tw-h-[392px] md:tw-w-[485px] md:tw-h-[292px] tw-w-[290px] tw-h-[200px]"
                : "md:tw-w-[170px] tw-w-[292px]"
            }`}
          >
            {activeId === video.id ? (
              <iframe
                className="tw-rounded-xl tw-w-full tw-h-full "
                width="auto"
                height="auto"
                src="https://www.youtube.com/embed/OfUUCYzrFhI?si=HsRjaQoBlYYRhu7Y"
                title="YouTube video player"
              ></iframe>
            ) : (
              <>
                <div
                  className="tw-relative tw-group tw-cursor-pointer md:tw-block tw-hidden"
                  onClick={() => setActiveId(video.id)}
                >
                  <div className="tw-relative lg:tw-h-[392px] md:tw-h-[292px] tw-w-full tw-h-[200px] tw-rounded-xl tw-brightness-75 tw-blur-[2px] ">
                    <Image
                      src={video.thumbnail}
                      alt={`Thumbnail ${video.id}`}
                      fill
                      className="tw-h-[392px] tw-rounded-xl tw-object-cover"
                    />
                  </div>

                  <div className="tw-absolute tw-inset-0 tw-flex tw-items-center tw-justify-center">
                    <PlayIcon2 />
                  </div>
                </div>
                <iframe
                  className="tw-rounded-xl tw-w-full tw-h-full md:tw-hidden tw-block "
                  width="auto"
                  height="auto"
                  src="https://www.youtube.com/embed/OfUUCYzrFhI?si=HsRjaQoBlYYRhu7Y"
                  title="YouTube video player"
                ></iframe>
              </>
            )}
          </div>
        ))}
      </div>
    </Container>
  );
};

export default VideosSlider;
